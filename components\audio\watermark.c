/**
 ******************************************************************************
 * @file    watermark.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    26-Sep-2018
 * @brief   This file contains the abstraction of linklist.
 ******************************************************************************
 *
 *  The MIT License
 *  Copyright (c) 2014 QUECTEL Inc.
 *
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is furnished
 *  to do so, subject to the following conditions:
 *
 *  The above copyright notice and this permission notice shall be included in
 *  all copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *  WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR
 *  IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 ******************************************************************************
 */

#include "watermark.h"
#include "helios_os.h"

#include "helios_include.h"

static QuecOSStatus quec_wm_lock(wm_handle_t *wm_handle)
{
	QuecOSStatus err = kNoErr;
	wm_t *wm_ptr = NULL;

	require_action(wm_handle && *wm_handle, exit, err = kParamErr);

	wm_ptr = *wm_handle;

	Helios_Mutex_Lock(wm_ptr->mutex, (uint32)HELIOS_WAIT_FOREVER);

exit:
	return err;
}

static QuecOSStatus quec_wm_try_lock(wm_handle_t *wm_handle)
{
	QuecOSStatus err = kNoErr;
	wm_t *wm_ptr = NULL;

	require_action(wm_handle && *wm_handle, exit, err = kParamErr);

	wm_ptr = *wm_handle;

	Helios_Mutex_Lock(wm_ptr->mutex, HELIOS_NO_WAIT);

exit:
	return err;
}

static QuecOSStatus quec_wm_unlock(wm_handle_t *wm_handle)
{
	QuecOSStatus err = kNoErr;
	wm_t *wm_ptr = NULL;

	require_action(wm_handle && *wm_handle, exit, err = kParamErr);

	wm_ptr = *wm_handle;

	Helios_Mutex_Unlock(wm_ptr->mutex);

exit:
	return err;
}

QuecOSStatus quec_wm_init(wm_handle_t *wm_handle, uint32 bufSz, uint32 lowVal, uint32 highVal, void (*cbReachedLow)(void *), void (*cbReachedHigh)(void *), void (*cbWMEmpty)(void *))
{
	QuecOSStatus err = kNoErr;
	wm_t *wm_ptr = NULL;

	require_action(wm_handle && bufSz, exit, err = kParamErr);

	wm_ptr = quec_malloc(sizeof(wm_t));
	require_action(wm_ptr, exit, err = kNoMemoryErr);

	memset(wm_ptr, 0, sizeof(wm_t));

	err = quec_ring_buffer_init(&wm_ptr->ringBuffer, bufSz);
	require_noerr(err, exit);

	wm_ptr->mutex = Helios_Mutex_Create();
	// require_noerr(err, exit);

	wm_ptr->lowVal = lowVal;
	wm_ptr->highVal = highVal;
	wm_ptr->isReachedLow = FALSE;
	wm_ptr->isReachedHigh = FALSE;
	wm_ptr->cbReachedLow = cbReachedLow;
	wm_ptr->cbReachedHigh = cbReachedHigh;
	wm_ptr->cbWMEmpty = cbWMEmpty;

	*wm_handle = wm_ptr;

exit:
	if (err != kNoErr)
	{
		if (wm_ptr)
			quec_free(wm_ptr);

		if (wm_ptr->ringBuffer)
			quec_ring_buffer_deinit(&wm_ptr->ringBuffer);
	}
	return err;
}

QuecOSStatus quec_wm_deinit(wm_handle_t *wm_handle)
{
	QuecOSStatus err = kNoErr;
	wm_t *wm_ptr = NULL;

	require_action(wm_handle && *wm_handle, exit, err = kParamErr);

	wm_ptr = *wm_handle;

	err = quec_ring_buffer_deinit(&wm_ptr->ringBuffer);
	require_noerr(err, exit);

	Helios_Mutex_Delete(wm_ptr->mutex);
	// require_noerr(err, exit);

	quec_free(wm_ptr);

	*wm_handle = NULL;

exit:
	return err;
}

QuecOSStatus quec_wm_read(wm_handle_t *wm_handle, uint8 *buf, uint32 toReadLen, uint32 *pActualReadLen, void *cbArgv)
{
	QuecOSStatus err = kNoErr;
	wm_t *wm_ptr = NULL;
	uint32 ring_buffer_used_size = 0;
	bool is_locked = FALSE;

	require_action(wm_handle && *wm_handle && buf, exit, err = kParamErr);

	wm_ptr = *wm_handle;

	err = quec_wm_lock(wm_handle);
	require_noerr(err, exit);

	is_locked = TRUE;

	err = quec_ring_buffer_get_used_size(wm_ptr->ringBuffer, &ring_buffer_used_size);
	require_noerr(err, exit);

	if (ring_buffer_used_size <= wm_ptr->lowVal)
	{
		wm_ptr->isReachedLow = TRUE;
		if (wm_ptr->cbReachedLow)
			wm_ptr->cbReachedLow(cbArgv);
	}

	err = quec_ring_buffer_read_ex(wm_ptr->ringBuffer, buf, toReadLen, pActualReadLen);
	require_noerr(err, exit);

	err = quec_ring_buffer_get_used_size(wm_ptr->ringBuffer, &ring_buffer_used_size);
	require_noerr(err, exit);

	if (ring_buffer_used_size <= wm_ptr->lowVal)
	{
		if (wm_ptr->isReachedLow == FALSE)
		{
			wm_ptr->isReachedLow = TRUE;
			if (wm_ptr->cbReachedLow)
				wm_ptr->cbReachedLow(cbArgv);
		}
	}

	if (ring_buffer_used_size < wm_ptr->highVal)
		wm_ptr->isReachedHigh = FALSE;

	if (ring_buffer_used_size == 0)
	{
		if (wm_ptr->cbWMEmpty)
			wm_ptr->cbWMEmpty(cbArgv);
	}

exit:
	if (is_locked)
		quec_wm_unlock(wm_handle);

	return err;
}

QuecOSStatus quec_wm_write(wm_handle_t *wm_handle, uint8 *buf, uint32 toWriteLen, uint32 *pActualWriteLen, void *cbArgv)
{
	QuecOSStatus err = kNoErr;
	wm_t *wm_ptr = NULL;
	uint32 ring_buffer_used_size = 0;
	bool is_locked = FALSE;

	require_action(wm_handle && *wm_handle && buf, exit, err = kParamErr);

	wm_ptr = *wm_handle;

	err = quec_wm_lock(wm_handle);
	require_noerr(err, exit);

	is_locked = TRUE;

	err = quec_ring_buffer_get_used_size(wm_ptr->ringBuffer, &ring_buffer_used_size);
	require_noerr(err, exit);

	if (ring_buffer_used_size >= wm_ptr->highVal)
	{
		wm_ptr->isReachedHigh = TRUE;
		if (wm_ptr->cbReachedHigh)
			wm_ptr->cbReachedHigh(cbArgv);
	}

	err = quec_ring_buffer_write_ex(wm_ptr->ringBuffer, buf, toWriteLen, pActualWriteLen);
	require_noerr(err, exit);

	err = quec_ring_buffer_get_used_size(wm_ptr->ringBuffer, &ring_buffer_used_size);
	require_noerr(err, exit);

	if (ring_buffer_used_size >= wm_ptr->highVal)
	{
		if (wm_ptr->isReachedHigh == FALSE)
		{
			wm_ptr->isReachedHigh = TRUE;
			if (wm_ptr->cbReachedHigh)
				wm_ptr->cbReachedHigh(cbArgv);
		}
	}

	if (ring_buffer_used_size > wm_ptr->lowVal)
		wm_ptr->isReachedLow = FALSE;

exit:
	if (is_locked)
		quec_wm_unlock(wm_handle);

	return err;
}

QuecOSStatus quec_wm_cnt(wm_handle_t *wm_handle, uint32 *pWmCnt)
{
	QuecOSStatus err = kNoErr;
	wm_t *wm_ptr = NULL;
	bool is_locked = FALSE;

	require_action(wm_handle && *wm_handle && pWmCnt, exit, err = kParamErr);

	wm_ptr = *wm_handle;

	err = quec_wm_lock(wm_handle);
	require_noerr(err, exit);

	is_locked = TRUE;

	err = quec_ring_buffer_get_used_size(wm_ptr->ringBuffer, pWmCnt);
	require_noerr(err, exit);

exit:
	if (is_locked)
		quec_wm_unlock(wm_handle);

	return err;
}

QuecOSStatus quec_wm_free_cnt(wm_handle_t *wm_handle, uint32 *pWmCnt)
{
	QuecOSStatus err = kNoErr;
	wm_t *wm_ptr = NULL;
	bool is_locked = FALSE;

	require_action(wm_handle && *wm_handle && pWmCnt, exit, err = kParamErr);

	wm_ptr = *wm_handle;

	err = quec_wm_lock(wm_handle);
	require_noerr(err, exit);

	is_locked = TRUE;

	err = quec_ring_buffer_get_free_size(wm_ptr->ringBuffer, pWmCnt);
	require_noerr(err, exit);

exit:
	if (is_locked)
		quec_wm_unlock(wm_handle);

	return err;
}

// QuecOSStatus quec_ring_buffer_reset(ring_buffer_handler_t ring_buffer_handler)
QuecOSStatus quec_wm_reset(wm_handle_t *wm_handle)
{
	QuecOSStatus err = kNoErr;
	wm_t *wm_ptr = NULL;
	bool is_locked = FALSE;

	require_action(wm_handle && *wm_handle, exit, err = kParamErr);

	wm_ptr = *wm_handle;

	err = quec_wm_lock(wm_handle);
	require_noerr(err, exit);

	is_locked = TRUE;

	err = quec_ring_buffer_reset(wm_ptr->ringBuffer);
	require_noerr(err, exit);

exit:
	if (is_locked)
		quec_wm_unlock(wm_handle);

	return err;
}
