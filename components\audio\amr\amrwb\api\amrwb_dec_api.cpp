/*
 * Copyright (C) 2014 The Android Open Source Project
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *  * Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *  * Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

 /*--------------------------------------------------------------------------------------------------------------------
 (C) Copyright 2020 ASR Ltd. All Rights Reserved
 -------------------------------------------------------------------------------------------------------------------*/

#include <stdint.h>
#include <string.h>
#include <stdlib.h>

#include "pvamrwbdecoder.h"
#include "amrwb_dec_api.h"
#include "audio_file.h"
extern "C" {
#include "utils.h"

    // Constants for AMR-WB.
    enum {
        kInputBufferSize = 64,
        kSamplesPerFrame = 320,
        kBitsPerSample = 16,
        kOutputBufferSize = kSamplesPerFrame * kBitsPerSample / 8,
        kSampleRate = 16000,
        kChannels = 1,
        kFileHeaderSize = 9,
        kMaxSourceDataUnitSize = 477 * sizeof(int16_t)
    };

    typedef struct amrwb_dec_state {
        AUDIO_FILE_ID fpInput;
        AUDIO_FILE_ID fpOutput;
        void* amrHandle;
        void *decoderBuf;
        int16_t *inputSampleBuf;
        int16_t *decoderCookie;
        int16_t *outputBuf;
        uint8_t *inputBuf;
        int frameSize;
        uint32_t amrFrameSize;
        int16 amrFrameMode;
        int16 amrFrameType;
    }amrwb_dec_state;

    static const uint32_t kFrameSizes[] = { 17, 23, 32, 36, 40, 46, 50, 58, 60, 5 };

    int amrwb_decode_open(const amrwb_dec_config* config, amrwb_dec_handle* handle) {
        if (config && handle) {
            uint32_t memRequirements;
            int bytesRead;
            char header[kFileHeaderSize];
            amrwb_dec_state* st = (amrwb_dec_state*)malloc(sizeof(amrwb_dec_state));
            if (!st)
                return -1;
            memset(st, 0, sizeof(amrwb_dec_state));

            // Open the input file.
            if (config->name && strlen(config->name) > 0) {
                st->fpInput = common_fopen(config->name, "rb");
                if (!st->fpInput)
                    goto amrwb_decode_open_end;
            }

            // Open the output file.
            if (config->out_name) {
                st->fpOutput = common_fopen(config->out_name, "wb");
                if (!st->fpOutput)
                    goto amrwb_decode_open_end;
            }

            // Validate the input AMR file.
            if (st->fpInput) {
                bytesRead = common_fread(header, 1, kFileHeaderSize, st->fpInput);
                if ((bytesRead != kFileHeaderSize) ||
                    (memcmp(header, "#!AMR-WB\n", kFileHeaderSize) != 0)) {
                    goto amrwb_decode_open_end;
                }
            }

            // Allocate the decoder memory.
            memRequirements = pvDecoder_AmrWbMemRequirements();
            st->decoderBuf = malloc(memRequirements);
            ASSERT(st->decoderBuf != NULL);

            // Create AMR-WB decoder instance.
            pvDecoder_AmrWb_Init(&st->amrHandle, st->decoderBuf, &st->decoderCookie);

            // Allocate input buffer.
            st->inputBuf = (uint8_t*)malloc(kInputBufferSize);
            ASSERT(st->inputBuf != NULL);

            // Allocate input sample buffer.
            st->inputSampleBuf = (int16_t*)malloc(kMaxSourceDataUnitSize);
            ASSERT(st->inputSampleBuf != NULL);

            // Allocate output buffer.
            st->outputBuf = (int16_t*)malloc(kOutputBufferSize);
            ASSERT(st->outputBuf != NULL);

            st->frameSize = kOutputBufferSize;
            *handle = (amrwb_dec_handle)st;
            return 0;

        amrwb_decode_open_end:
            if (st) {
                if (st->fpInput)
                    common_fclose(st->fpInput);
                if (st->fpOutput)
                    common_fclose(st->fpOutput);
                free(st);
                return -1;
            }
        }

        return -1;
    }

    int amrwb_decode_read(amrwb_dec_handle handle) {
        amrwb_dec_state* st = (amrwb_dec_state*)handle;
        if (st) {
            // Decode loop.
            uint32_t bytesRead = 0;
            // Read mode.
            uint8_t modeByte;
            bytesRead = common_fread(&modeByte, 1, 1, st->fpInput);
            if (bytesRead != 1)
                return -2;

            int16 mode = ((modeByte >> 3) & 0x0f);
            // AMR-WB file format cannot have mode 10, 11, 12 and 13.
            if (mode >= 0 && mode <= 9) {
                // Read rest of the frame.
                int32_t frameSize = kFrameSizes[mode];
                bytesRead = common_fread(st->inputBuf, 1, frameSize, st->fpInput);
                if (bytesRead != frameSize)
                    return -3;

                RX_State_wb rx_state;
                mime_unsorting(
                    (uint8_t *)st->inputBuf,
                    st->inputSampleBuf,
                    &st->amrFrameType, &st->amrFrameMode, 1, &rx_state);
                st->amrFrameSize = frameSize;
            }
            else if (mode >= 10 && mode <= 13) {
                return -4;
            }
            else {
                st->amrFrameSize = 0;
            }

            st->amrFrameMode = mode;
            return 0;
        }

        return -1;
    }

    int amrwb_decode_write(amrwb_dec_handle handle) {
        amrwb_dec_state* st = (amrwb_dec_state*)handle;
        if (st && st->fpOutput) {
            common_fwrite(st->outputBuf, kOutputBufferSize, 1, st->fpOutput);
            return 0;
        }

        return -1;
    }

    int amrwb_decode_do(amrwb_dec_handle handle) {
        amrwb_dec_state* st = (amrwb_dec_state*)handle;
        if (st) {
            int mode = st->amrFrameMode;
            if (mode >= 9) {
                // Produce silence for comfort noise, speech lost and no data.
                memset(st->outputBuf, 0, kOutputBufferSize);
            }
            else /* if (mode < 9) */ {
                int16_t numSamplesOutput;
                pvDecoder_AmrWb(
                    st->amrFrameMode, st->inputSampleBuf,
                    st->outputBuf,
                    &numSamplesOutput,
                    st->decoderBuf, st->amrFrameType, st->decoderCookie);

                if (numSamplesOutput != kSamplesPerFrame) {
                    return -2;
                }

                for (int i = 0; i < kSamplesPerFrame; ++i) {
                    st->outputBuf[i] &= 0xfffC;
                }
            }

            return 0;
        }

        return -1;
    }

    int amrwb_decode_loop(amrwb_dec_handle handle) {
        amrwb_dec_state* st = (amrwb_dec_state*)handle;
        if (st) {
            if (amrwb_decode_read(handle) != 0)
                return -2;

            if (amrwb_decode_do(handle) != 0)
                return -3;

            if (amrwb_decode_write(handle) != 0)
                return -4;

            return 0;
        }

        return -1;
    }

    int amrwb_decode_get(amrwb_dec_handle handle, int16_t* output_data, uint32_t* size) {
        amrwb_dec_state* st = (amrwb_dec_state*)handle;
        if (st && output_data) {
            if (size && *size >= kOutputBufferSize) {
                memcpy(output_data, st->outputBuf, kOutputBufferSize);
                *size = kOutputBufferSize;
                return 0;
            }
        }
        return -1;
    }

    int amrwb_decode_set(amrwb_dec_handle handle, const uint8_t* data, uint32_t size, uint32_t* used_size) {
        amrwb_dec_state* st = (amrwb_dec_state*)handle;
        if (st && data) {
            if (size > 0) {
                int16 mode = (int16)((data[0] >> 3) & 0x0f);
                uint32_t frame_size = 1;
                // AMR-WB file format cannot have mode 10, 11, 12 and 13.
                if (mode >= 0 && mode <= 9) {
                    if (mode < 9) {
                        frame_size = kFrameSizes[mode] + 1;
                    }
                }
                else if (mode >= 10 && mode <= 13) {
                    return -2;
                }

                if (size >= frame_size) {
                    st->amrFrameMode = mode;
                    if (frame_size > 1) {
                        // Read rest of the frame.
                        RX_State_wb rx_state;
                        memcpy(st->inputBuf, &data[1], frame_size - 1);
                        mime_unsorting(
                            (uint8_t *)st->inputBuf,
                            st->inputSampleBuf,
                            &st->amrFrameType, &st->amrFrameMode, 1, &rx_state);
                    }
                    st->amrFrameSize = frame_size - 1;
                    if (used_size)
                        *used_size = frame_size;
                    return 0;
                }
            }
        }

        return -1;
    }

    int amrwb_decode_get_amr(amrwb_dec_handle handle, uint8_t* output_data, uint32_t* size) {
        amrwb_dec_state* st = (amrwb_dec_state*)handle;
        if (st && output_data) {
            if (size && *size >= st->amrFrameSize) {
                *size = st->amrFrameSize;
                if (st->amrFrameSize > 0)
                    memcpy(output_data, st->inputSampleBuf, st->amrFrameSize);
                return 0;
            }
        }

        return -1;
    }

    int amrwb_decode_get_rate(amrwb_dec_handle handle, int32_t* rate) {
        amrwb_dec_state* st = (amrwb_dec_state*)handle;
        if (st && rate) {
            *rate = st->amrFrameMode;
        }

        return -1;
    }

    int amrwb_decode_close(amrwb_dec_handle handle) {
        amrwb_dec_state* st = (amrwb_dec_state*)handle;
        if (st) {
            // Close input and output file.
            if (st->fpInput)
                common_fclose(st->fpInput);
            if (st->fpOutput)
                common_fclose(st->fpOutput);

            // Free allocated memory.
            if (st->inputBuf)
                free(st->inputBuf);
            if (st->inputSampleBuf)
                free(st->inputSampleBuf);
            if (st->outputBuf)
                free(st->outputBuf);

            free(st);
            return 0;
        }

        return -1;
    }
}

#if DECODER_TEST_AMR_WB == 1
int main(int argc, char *argv[]) {
    amrwb_dec_config config = { 0 };
    amrwb_dec_handle handle = 0;

    if (argc > 1)
        config.name = argv[1];
    if (argc > 2)
        config.out_name = argv[2];

    if (amrwb_decode_open(&config, &handle) != 0)
        return -1;

    while (1) {
        if (amrwb_decode_loop(handle) != 0)
            break;
    }

    amrwb_decode_close(handle);
    return 0;
}
#endif
