NAME := GB2312

include config/$(KCONFIG_CONFIG)

GLOBAL_INCS = include

ifeq ($(CONFIG_GB2312), y)
$(NAME)_SRCS = gb2312.c

GLOBAL_INCS = . 

endif


$(NAME)_CFLAGS = \
		-Wno-error=unused-parameter \
		-Wno-error=unused-variable \
		-Wno-error=sign-compare \
		-Wno-error=unused-function \
		-Wno-error=unused-but-set-variable \
		-Wno-error=unused-label \
		-Wno-error=restrict \
		-Wno-error=unused-const-variable \
		-Wno-error=incompatible-pointer-types \
		-Wno-error=implicit-function-declaration \
		-Wno-error=int-conversion


$(NAME)_COMPONENTS += system
