/* Copyright (C) 2000-2001 Free Software Foundation, Inc.
   This file is part of the GNU LIBICONV Library.

   The GNU LIBICONV Library is free software; you can redistribute it
   and/or modify it under the terms of the GNU Library General Public
   License as published by the Free Software Foundation; either version 2
   of the License, or (at your option) any later version.

   The GNU LIBICONV Library is distributed in the hope that it will be
   useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
   Library General Public License for more details.

   You should have received a copy of the GNU Library General Public
   License along with the GNU LIBICONV Library; see the file COPYING.LIB.
   If not, see <https://www.gnu.org/licenses/>.  */

/* Names for locale dependent encodings. */

DEFENCODING(( "CHAR",
            ),
            local_char,
            { NULL, NULL },               { NULL, NULL })

DEFENCODING(( "WCHAR_T",                /* glibc */
            ),
            local_wchar_t,
            { NULL, NULL },               { NULL, NULL })
