
#ifndef __HELIOS_AUDIO_AMR_H__
#define __HELIOS_AUDIO_AMR_H__

typedef void (*cb_on_player)(char *p_data, int len, Helios_EnumAudPlayerState state);


int helios_amr_stream_open();

int helios_amr_stream_write(void *data, unsigned int count);

int helios_amr_stream_close();

int helios_amr_file_start(char *file_name, cb_on_player aud_cb);

int helios_amr_file_end();

int helios_amr_set_callback(cb_on_player aud_cb);

int helios_amrnbenc_init(unsigned int mode, unsigned int format);

int helios_amrnbenc_do(unsigned char *input_buff, unsigned char *output_buff);

int helios_amrnbenc_deinit(void);
#endif
