/* ------------------------------------------------------------------
 * Copyright (C) 1998-2009 PacketVideo
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied.
 * See the License for the specific language governing permissions
 * and limitations under the License.
 * -------------------------------------------------------------------
 */
/****************************************************************************************
Portions of this file are derived from the following 3GPP standard:

    3GPP TS 26.073
    ANSI-C code for the Adaptive Multi-Rate (AMR) speech codec
    Available from http://www.3gpp.org

(C) 2004, 3GPP Organizational Partners (ARIB, ATIS, CCSA, ETSI, TTA, TTC)
Permission to distribute, modify and use this file under the standard license
terms listed above has been obtained from the copyright holder.
****************************************************************************************/
/*

 Filename: /audio/gsm_amr/c/src/q_plsf_3_tbl.c

------------------------------------------------------------------------------
 REVISION HISTORY

 Description: Created this file from the reference, q_plsf_3_tbl.tab

 Description: Added #ifdef __cplusplus and removed "extern" from table
              definition.

 Description: Put "extern" back.

 Who:                       Date:
 Description:

------------------------------------------------------------------------------
 MODULE DESCRIPTION

------------------------------------------------------------------------------
*/

/*----------------------------------------------------------------------------
; INCLUDES
----------------------------------------------------------------------------*/
#include "q_plsf_3_tbl.h"

/*--------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------
; MACROS
; [Define module specific macros here]
----------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------
; DEFINES
; [Include all pre-processor statements here. Include conditional
; compile variables also.]
----------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------
; LOCAL FUNCTION DEFINITIONS
; [List function prototypes here]
----------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------
; LOCAL VARIABLE DEFINITIONS
; [Variable declaration - defined here and used outside this module]
----------------------------------------------------------------------------*/
/*
********************************************************************************
*
*      GSM AMR-NB speech codec   R98   Version 7.5.0   March 2, 2001
*                                R99   Version 3.2.0
*                                REL-4 Version 4.0.0
*
********************************************************************************
*
*      File             : q_plsf_3.tab
*      Purpose          : Table for routine LSF VQ.
*      $Id $
*
********************************************************************************
*/


/* initalization table for MA predictor in dtx mode */
const Word16 past_rq_init[80] =
{
    -258, -318, -439, -634, -656, -773, -711, -502, -268, -193,
    -2,  125,  122,  -39,   -9,  105,  129,  283,  372,  575,
    -277, -324, -197, -487, -445, -362, -292,  -27,  177,  543,
    342,  517,  516,  130,   27, -104, -120, -140,  -74,  -56,
    -564, -943, -1520, -965, -814, -526, -322,   -2,  159,  657,
    -312, -284, -386, -597, -493, -526, -418, -229,  105,  449,
    -557, -870, -1075, -919, -950, -752, -709, -316,   62,  486,
    -314, -191, -203, -330, -160, -103,  -51,  131,  338,  515
};


const Word16 mean_lsf_3[10] =
{
    1546,
    2272,
    3778,
    5488,
    6972,
    8382,
    10047,
    11229,
    12766,
    13714
};


const Word16 pred_fac_3[10] =
{
    9556,
    10769,
    12571,
    13292,
    14381,
    11651,
    10588,
    9767,
    8593,
    6484
};



/* first codebook from IS641 */

const Word16 dico1_lsf_3[DICO1_SIZE*3] =
{
    6, 82, -131,
    154, -56, -735,
    183, -65, -265,
    9, -210, -361,
    113, 718, 1817,
    1010, 1214, 1573,
    857, 1333, 2276,
    827, 1568, 1933,
    717, 1989, 2206,
    838, 1172, 1823,
    721, 1000, 2154,
    286, 476, 1509,
    -247, -531, 230,
    147, -82, 569,
    26, -177, -944,
    -27, -273, 692,
    -164, -264, -183,
    224, 790, 1039,
    899, 946, 601,
    485, 771, 1150,
    524, 677, 903,
    -140, 375, 778,
    410, 676, 429,
    301, 530, 1009,
    719, 646, 38,
    226, 367, 40,
    145, -45, -505,
    290, 121, -121,
    302, 127, 166,
    -124, -383, -956,
    -358, -455, -977,
    715, 878, 894,
    978, 923, 211,
    477, 272, 64,
    188, -78, 17,
    -143, -65, 38,
    643, 586, 621,
    -134, -426, -651,
    347, 545, 2820,
    1188, 2726, 2442,
    142, -80, 1735,
    283, 130, 461,
    -262, -399, -1145,
    -411, 155, 430,
    329, 375, 779,
    53, -226, -139,
    -129, -236, 1682,
    285, 744, 1327,
    738, 697, 1664,
    312, 409, 266,
    325, 720, 135,
    1, 221, 453,
    8, 203, 145,
    299, 640, 760,
    29, 468, 638,
    103, 429, 379,
    420, 954, 932,
    1326, 1210, 1258,
    704, 1012, 1152,
    -166, -444, -266,
    -316, -130, -376,
    191, 1151, 1904,
    -240, -543, -1260,
    -112, 268, 1207,
    70, 1062, 1583,
    278, 1360, 1574,
    -258, -272, -768,
    19, 563, 2240,
    -3, -265, 135,
    -295, -591, -388,
    140, 354, -206,
    -260, -504, -795,
    -433, -718, -1319,
    109, 331, 962,
    -429, -87, 652,
    -296, 426, 1019,
    -239, 775, 851,
    489, 1334, 1073,
    -334, -332, 25,
    543, 1206, 1807,
    326, 61, 727,
    578, 849, 1405,
    -208, -277, 329,
    -152, 64, 669,
    -434, -678, -727,
    -454, -71, 251,
    605, 480, 254,
    -482, 11, 996,
    -289, 395, 486,
    722, 1049, 1440,
    -30, -316, -786,
    -106, -115, -619,
    861, 1474, 1412,
    1055, 1366, 1184,
    812, 1237, 925,
    42, -251, -576,
    342, 141, -454,
    -168, -80, 1359,
    -342, -656, -1763,
    100, 821, 725,
    990, 747, 800,
    332, 440, 568,
    663, 379, 852,
    112, 165, -369,
    597, 910, 282,
    -8, 834, 1281,
    -352, 572, 695,
    462, 2246, 1806,
    345, 190, 1374,
    416, 915, 2166,
    168, -82, 280,
    -516, -446, 840,
    47, 533, 44,
    -362, -711, -1143,
    22, 193, 1472,
    -85, 233, 1813,
    -62, 579, 1504,
    550, 944, 1749,
    723, 650, 1148,
    972, 884, 1395,
    -425, 643, 0,
    1000, 952, 1098,
    249, 1446, 672,
    -334, -87, 2172,
    -554, 1882, 2672,
    140, 1826, 1853,
    920, 1749, 2590,
    1076, 1933, 2038,
    -137, -443, -1555,
    1269, 1174, 468,
    -493, -122, 1521,
    -451, 1033, 1214,
    482, 1695, 1118,
    815, 649, 384,
    -446, -692, 107,
    -319, -605, -118,
    -207, -505, 525,
    -468, -12, 2736,
    75, 1934, 1305,
    880, 2358, 2267,
    1285, 1575, 2004,
    -48, -304, -1186,
    -435, -461, -251,
    -366, -404, -547,
    -289, -605, -597,
    -538, -810, -165,
    -120, 3, 356,
    639, 1241, 1502,
    96, 177, 750,
    -435, -585, -1174,
    -356, 109, -79,
    -485, 288, 2005,
    9, 1116, 731,
    880, 2134, 946,
    -265, 1585, 1065,
    1157, 1210, 843,
    -498, -668, 431,
    374, 321, -229,
    1440, 2101, 1381,
    449, 461, 1155,
    -105, 39, -384,
    -263, 367, 182,
    -371, -660, 773,
    -188, 1151, 971,
    1333, 1632, 1435,
    774, 1267, 1221,
    -482, -832, -1489,
    -237, -210, 860,
    890, 1615, 1064,
    472, 1062, 1192,
    185, 1077, 989,
    -568, -992, -1704,
    -449, -902, -2043,
    -142, -377, -458,
    -210, -554, -1029,
    -11, 1133, 2265,
    -329, -675, -893,
    -250, 657, 1187,
    519, 1510, 1779,
    520, 539, 1403,
    527, 1421, 1302,
    -563, -871, -1248,
    -147, -463, 879,
    -76, 2334, 2840,
    563, 2573, 2385,
    632, 1926, 2920,
    719, 2023, 1840,
    -545, -723, 1108,
    129, -125, 884,
    1417, 1632, 925,
    -94, 1566, 1751,
    -341, 1533, 1551,
    591, 395, -274,
    -76, 981, 2831,
    153, 2985, 1844,
    1032, 2565, 2749,
    1508, 2832, 1879,
    791, 1199, 538,
    -190, -453, 1489,
    -278, -548, 1158,
    -245, 1941, 2044,
    1024, 1560, 1650,
    512, 253, 466,
    -62, -323, 1151,
    -473, -376, 507,
    -433, 1380, 2162,
    899, 1943, 1445,
    134, 704, 440,
    460, 525, -28,
    -450, 279, 1338,
    0, 971, 252,
    -445, -627, -991,
    -348, -602, -1424,
    398, 712, 1656,
    -107, 314, -178,
    93, 2226, 2238,
    518, 849, 656,
    -462, -711, -447,
    174, -34, 1191,
    -119, 42, 1005,
    -372, 274, 758,
    1036, 2352, 1838,
    675, 1724, 1498,
    430, 1286, 2133,
    -129, -439, 0,
    -373, 800, 2144,
    6, 1587, 2478,
    478, 596, 2128,
    -428, -736, 1505,
    385, 178, 980,
    139, 449, 1225,
    -526, -842, -982,
    145, 1554, 1242,
    623, 1448, 656,
    349, 1016, 1482,
    31, -280, 415,
    -316, 724, 1641,
    360, 1058, 556,
    -436, -358, 1201,
    -355, 1123, 1939,
    401, 1584, 2248,
    -527, -1012, 355,
    233, 238, 2233,
    -550, -897, -639,
    -365, -501, 1957,
    389, 1860, 1621,
    162, 1132, 1264,
    -237, 1174, 1390,
    -640, -411, 116,
    -228, 1694, 2298,
    1639, 2186, 2267,
    562, 1273, 2658,
    323, 338, 1774,
    578, 1107, 852,
    22, 594, 934,
    -143, 718, 446
};


/* second codebook from IS641 */

const Word16 dico2_lsf_3[DICO2_SIZE*3] =
{
    50, 71, -9,
    -338, -698, -1407,
    102, -138, -820,
    -310, -469, -1147,
    414, 67, -267,
    1060, 814, 1441,
    1548, 1360, 1272,
    1754, 1895, 1661,
    2019, 2133, 1820,
    1808, 2318, 1845,
    644, -93, 454,
    858, 329, -136,
    489, -258, -128,
    -198, -745, -41,
    -52, -265, -985,
    346, 137, 479,
    -1741, -748, -684,
    -1163, -1725, -367,
    -895, -1145, -784,
    -488, -946, -968,
    -85, -390, -725,
    215, -340, -171,
    1020, 916, 1969,
    564, 179, 746,
    662, 977, 1734,
    887, 622, 914,
    939, 856, 1165,
    309, 688, 803,
    917, 161, 570,
    118, -20, -283,
    -816, -42, 204,
    -1228, -325, -462,
    -963, -202, -143,
    -988, -484, -361,
    -702, -978, -477,
    -302, -790, -1188,
    -100, -786, -1088,
    -1054, -947, -1684,
    -202, -843, -782,
    -1039, -1378, -901,
    -624, -110, -85,
    356, 213, -10,
    -493, 364, 774,
    425, 822, 479,
    -83, 557, 520,
    -992, -1560, -572,
    -603, -741, -26,
    -502, -638, -903,
    209, 306, 147,
    -316, -593, -596,
    -85, -211, -225,
    -918, -529, 117,
    233, -439, -738,
    1101, 751, 633,
    1457, 1716, 1511,
    1765, 1457, 910,
    1122, 1156, 849,
    1354, 868, 470,
    -871, -1150, -1796,
    -871, -861, -992,
    -118, 155, 212,
    -1051, -849, -606,
    -1117, -1849, -2750,
    -1019, -1427, -1869,
    370, -184, -414,
    959, 493, 104,
    958, 1039, 543,
    154, 653, 201,
    1249, 507, 150,
    663, 503, 230,
    623, 777, 675,
    659, 88, -110,
    843, 244, 224,
    382, 541, 302,
    724, 433, 666,
    1166, 734, 341,
    -138, 20, -397,
    -1183, -424, -46,
    -321, -352, -124,
    1333, 1021, 1080,
    262, 366, 723,
    922, 283, -551,
    31, -636, -611,
    -689, -697, -415,
    -952, -779, -201,
    -1329, -598, -359,
    -953, -1285, 166,
    493, 305, 221,
    846, 703, 610,
    840, 936, 774,
    -723, -1324, -1261,
    -357, -1025, -1388,
    -1096, -1376, -365,
    -1416, -1881, -608,
    -1798, -1727, -674,
    -545, -1173, -703,
    678, 786, 148,
    -123, 696, 1288,
    644, 350, -10,
    414, 614, 15,
    137, 344, -211,
    -814, -1512, -819,
    -391, -930, -588,
    47, -591, -898,
    -909, -1097, -163,
    -1272, -1167, -157,
    -1464, -1525, -389,
    -1274, -1188, -624,
    671, 213, 454,
    124, -274, -525,
    -729, -496, -152,
    -1344, 122, 135,
    -2905, -589, -394,
    -1728, 441, -50,
    1476, 904, 787,
    316, 236, -440,
    -347, 217, 413,
    -911, -917, 121,
    -455, -932, 202,
    -92, -465, -375,
    488, 390, 474,
    876, 729, 316,
    -1815, -1312, -669,
    87, 962, 432,
    563, -249, -1058,
    250, 285, 1105,
    1141, 427, 696,
    -1038, -1664, -1582,
    -948, 346, 160,
    -309, -272, -858,
    670, 624, 1250,
    -944, -408, -666,
    -606, -320, -384,
    -492, 230, 65,
    334, -50, -16,
    -16, -690, -1397,
    1791, 1716, 1399,
    2478, 2063, 1404,
    1245, 1471, 1426,
    -382, -1037, -2,
    173, -398, 1145,
    1491, 2024, 1801,
    772, 1274, 1506,
    1429, 1735, 2001,
    1079, 1218, 1273,
    -1154, -1851, -1329,
    -808, -1133, -1096,
    -451, -1033, -1722,
    65, 578, -84,
    -1476, -2434, -1778,
    -765, -1366, -494,
    -218, -594, -931,
    337, -236, 562,
    2357, 2662, 1938,
    1489, 1276, 874,
    189, 358, 374,
    -1519, -2281, -2346,
    -967, -1271, -2095,
    -628, -1188, -1542,
    1661, 1043, 546,
    565, 1061, 732,
    -64, -836, -434,
    -436, -96, 203,
    1078, 1216, 1636,
    907, 1534, 986,
    326, 965, 845,
    142, -84, 197,
    470, 2379, 1570,
    1133, 470, 1214,
    395, 1376, 1200,
    1125, 1042, 348,
    -543, -1234, -376,
    -215, -181, 481,
    -1947, -1621, -210,
    -750, -1185, 390,
    29, -399, 27,
    820, 1236, 755,
    695, 979, 409,
    -174, 1197, 1035,
    912, 1356, 1846,
    -992, -1437, 484,
    -1485, -1700, 208,
    -412, 1204, 1432,
    -271, 896, 1144,
    -416, 1777, 1434,
    -1696, -2644, -204,
    -1789, -1551, 1033,
    -1656, -1559, 1303,
    -1253, -1589, 1081,
    -669, -1095, -66,
    -682, 320, -345,
    659, 305, 1069,
    -1292, -804, -19,
    -1635, -1291, 29,
    -1683, -497, 71,
    -287, -7, -100,
    -494, -962, -237,
    852, 1881, 1740,
    -1217, -1387, 227,
    -660, 302, 373,
    96, 1087, 1257,
    -1074, -1669, 160,
    485, 2076, 1798,
    -934, -220, 552,
    -596, -612, 237,
    336, 1720, 879,
    643, 629, 434,
    1267, 522, 1633,
    15, 244, -441,
    1475, 717, 184,
    1819, 1590, 1709,
    988, 261, 937,
    2093, 2345, 1520,
    2139, 1858, 1606,
    -577, -579, -1203,
    -956, 135, -488,
    -464, 51, -338,
    -629, -348, -723,
    1146, 2073, 1442,
    2192, 1466, 911,
    -1444, -1572, -2278,
    1400, 710, 1297,
    1335, 633, 928,
    1434, 2194, 2594,
    2422, 2204, 1881,
    982, 2242, 1854,
    380, 792, 1145,
    -63, -539, 414,
    -252, -964, -314,
    -1261, -683, -780,
    -831, -526, -1005,
    -1666, -1135, -424,
    -1611, -452, -299,
    1268, 1048, 642,
    1147, 853, 856,
    -675, -336, 139,
    2268, 1343, 1418,
    29, 768, 797,
    -1224, 423, 564,
    -1318, -1082, 245,
    -1302, -812, 573,
    -1298, -1617, 646,
    -968, 834, 723,
    993, 1652, 2027,
    -191, -817, 432,
    662, 60, 198,
    626, 997, 1330,
    1648, 1963, 1289,
    -1597, -93, -45,
    -1088, 37, -84,
    1653, 2607, 2337,
    1065, 2040, 2377,
    1139, 2326, 2118,
    859, 357, 1510,
    664, 1227, 1099,
    479, 1360, 912,
    1897, 1754, 2019,
    1168, 1909, 1784,
    399, 34, 256,
    -593, -304, -1053,
    547, 1694, 1407,
    647, -99, -341,
    1492, 1647, 1190,
    38, -644, -212,
    395, 846, 222,
    -704, -765, -716,
    -724, -1964, -2804,
    -150, 291, -82,
    1233, 1459, 1007,
    -140, -155, 153,
    439, 297, 1568,
    -1529, -410, -636,
    1536, 455, -237,
    -1328, -139, -260,
    531, 554, 868,
    269, 1264, 606,
    -233, 883, 463,
    742, 600, -120,
    -73, 421, 212,
    -439, -58, 804,
    -1286, -1241, 728,
    294, -490, 50,
    -591, -905, -1254,
    42, -687, 147,
    -25, 273, 596,
    -311, 1213, 601,
    -754, 849, 584,
    429, 607, 587,
    -602, -166, 461,
    -796, -823, 777,
    1380, 910, 1755,
    119, 1417, 972,
    -219, -880, -1596,
    -1049, -1010, 438,
    -713, -1379, 78,
    0, -447, -1179,
    -1136, -1319, -1573,
    2248, 1767, 1309,
    946, 1583, 1432,
    1150, 482, 436,
    -469, -1108, 618,
    -447, -966, 1088,
    -1252, -1515, -114,
    -1104, -2008, -579,
    210, 613, 497,
    -1975, -1437, 642,
    -1269, -856, 1011,
    -1646, -1185, 1063,
    -1555, -672, 1204,
    -1692, -1114, 623,
    -979, -1326, -1277,
    539, -147, 894,
    -1354, -897, -434,
    888, 475, 428,
    153, -384, 338,
    -1492, -511, 359,
    -974, -1115, -470,
    105, -550, 677,
    -937, -1145, 877,
    380, -260, 210,
    1685, 924, 1256,
    1775, 1190, 1095,
    1419, 631, 533,
    627, 299, -347,
    -411, -534, 647,
    -650, 29, -595,
    -378, -1367, 1563,
    1402, 1121, 1465,
    1089, 1410, 648,
    -2096, -1090, -6,
    311, -194, -869,
    -639, -831, 416,
    -1162, -1224, 1349,
    -1247, -941, 1813,
    -2193, -1987, 453,
    -619, -1367, -956,
    -1606, -1972, -1507,
    -1175, -1057, -1104,
    -377, 601, 201,
    1876, 825, 374,
    -430, -1323, 29,
    -1397, -1249, -1331,
    -1007, -1504, 960,
    -1401, -2009, 197,
    -1379, -1949, -236,
    -1077, 123, 422,
    615, 1269, 546,
    -306, 1526, 904,
    1194, 1788, 1177,
    -626, -884, -1526,
    199, 766, 1504,
    -1065, 862, 197,
    -1034, -1773, -887,
    -800, 145, 599,
    -1134, -519, 626,
    -1205, -1926, 500,
    -910, -1041, -1395,
    -1476, -1567, -969,
    -523, 842, 34,
    1794, 646, 862,
    -1207, -1888, -1002,
    -78, -9, -672,
    1044, 759, 80,
    -600, 1139, 1019,
    57, 2000, 1422,
    -833, 1414, 1121,
    -1202, 1630, 1260,
    -461, 1420, 1244,
    1537, 975, 253,
    -283, 324, -359,
    599, -195, 106,
    588, 62, -587,
    -757, 645, 205,
    51, 1201, 758,
    -1209, 673, -390,
    -624, 1581, 941,
    -151, 1023, 735,
    2820, 1301, 690,
    -302, 524, -99,
    -900, -1588, -1189,
    1084, 251, 238,
    2014, 1792, 1010,
    1245, 1633, 1741,
    -1227, -1540, -1208,
    -621, 456, -109,
    40, -65, 788,
    -805, -699, -1350,
    -583, 904, 832,
    -801, 532, 594,
    1972, 1408, 1351,
    -1177, -1880, -2114,
    -773, 568, 948,
    -1015, 1079, 1260,
    -1111, 482, -130,
    1778, 1044, 780,
    -1491, 245, 912,
    -316, -1141, -917,
    -536, -1442, -2346,
    -785, -1546, -1988,
    -2003, 257, 909,
    -1849, -633, -1209,
    -1538, -1918, -1054,
    1606, 2239, 1576,
    -567, -1500, -1544,
    -1279, 195, 1369,
    -817, 293, 1219,
    -525, 630, 1197,
    -1698, -2425, -1840,
    -303, 731, 747,
    -1169, -251, 269,
    -950, -75, 1684,
    -1182, -453, 1005,
    -1599, 585, 378,
    -2075, -571, -427,
    -529, -1159, -1171,
    -283, -205, -564,
    -796, 1246, 717,
    2277, 927, 539,
    -454, 559, 440,
    -717, 1460, 1615,
    -1030, 1052, 1610,
    -1169, -138, 847,
    226, 39, -612,
    -1251, -106, -729,
    -651, 968, 1302,
    -714, -636, 1727,
    353, 1069, 410,
    -798, -156, 1099,
    -574, 918, 446,
    -1310, 1012, 466,
    1408, 1591, 765,
    1429, 1380, 1757,
    1949, 1956, 2378,
    1578, 2047, 2148,
    916, 98, -7,
    1893, 1418, 2141,
    348, 1405, 1579,
    152, 1134, 1801,
    -267, 154, 1395,
    -1166, 469, 1054,
    -1142, -405, -1073,
    -1341, -2264, -1581,
    -364, 869, 1706,
    -1162, 549, 1550,
    -1225, -1932, -1666,
    -1485, -1977, -2055,
    -1727, -906, -98,
    -1897, 233, 1492,
    892, 108, -331,
    -1728, -1170, -1700,
    -1060, 1980, 1790,
    -1070, -1741, -1909,
    -11, 1539, 1317,
    -1600, 94, 497,
    421, 443, -197,
    -1578, -349, -994,
    -599, -539, 1140,
    -965, -1419, -129,
    -1341, 175, -447,
    -375, 1311, 2055,
    -371, -650, -307,
    -1073, 605, 365,
    -2057, -113, 430,
    652, 914, 967,
    -1012, -1586, -2323,
    1505, 1248, 559,
    262, -486, -401,
    -1727, 1342, 1546,
    50, 56, 432,
    -330, 119, -604,
    -1517, -1080, -810,
    946, 1127, 1055,
    -1400, -1703, -1712,
    -1270, -704, -1317,
    807, 1821, 1143,
    2760, 1606, 2171,
    1120, 409, -150,
    -147, 404, 959,
    2439, 1911, 2189,
    -906, -141, -866,
    -904, -142, -458,
    -557, -708, -1679,
    -830, -1431, -1583,
    -1842, -1346, -1086,
    -1604, -272, 915,
    -1196, 772, 1056,
    -638, -1234, -1897,
    -500, -81, -822,
    -1289, -1613, -735,
    -117, 785, 168,
    -1090, 1133, 922,
    -1096, -746, 1384,
    287, -547, -1063,
    -1376, -2201, -1204,
    -2176, -1570, -1757,
    -1511, -2241, -771,
    -1737, 1099, 830,
    -1588, 724, 1243,
    -1542, 693, 805,
    -1690, -240, 1665,
    -1700, -4, -668,
    2149, 816, 1042,
    -818, -1841, 22,
    -764, -507, 449,
    -1151, -617, 289,
    -843, -1596, -240,
    498, -234, -657,
    -752, 480, 1678,
    -319, -481, 193,
    -811, 171, -119,
    -2128, -202, -848,
    1717, 1140, 1700
};


/* third codebook from IS641 */

const Word16 dico3_lsf_3[DICO3_SIZE*4] =
{
    67, -17, 66, -12,
    -1690, -581, -104, -272,
    -1076, -1186, -1845, -376,
    -1140, -926, -420, -58,
    -259, -656, -1134, -553,
    1788, 1227, 455, 129,
    462, 441, -240, -528,
    840, 514, 130, -75,
    1114, 623, 153, 216,
    1068, 564, -6, -276,
    1119, 727, 190, -68,
    704, 306, 119, -264,
    329, 61, -100, 156,
    364, 123, 183, -208,
    -171, -123, 220, -65,
    -306, -62, 402, 17,
    -660, -938, -266, 0,
    385, 235, 276, 285,
    320, 268, -336, -200,
    -724, 17, -84, 381,
    -544, 429, 494, 519,
    -117, 288, 304, 329,
    643, 157, 701, 508,
    1200, 625, 796, 608,
    998, 421, 492, 632,
    1204, 780, 446, 132,
    1257, 844, 547, 449,
    829, 658, 541, 470,
    1132, 1258, 918, 639,
    547, 51, 423, 279,
    9, 392, 83, 94,
    542, 543, 229, -147,
    -198, 129, 194, -185,
    -863, -1321, -302, 30,
    -597, -629, -19, 114,
    -900, -1081, 466, 353,
    -1483, -1573, 15, -143,
    -1708, -2059, -751, 196,
    -1876, -2067, -642, -258,
    -2335, -1470, -450, -564,
    -584, -186, -872, -414,
    -1805, -988, -1125, -1310,
    -726, -1129, 28, 169,
    -1039, -864, -718, -246,
    484, 36, -233, -49,
    265, 67, 289, 467,
    178, 543, 810, 540,
    84, 282, 672, 703,
    -975, -777, 129, 287,
    -938, -227, 955, 595,
    -1617, -289, 836, 649,
    -1847, -215, 1106, 718,
    -2034, -1085, 650, 440,
    -2101, -529, 907, 575,
    -2011, -336, 670, 204,
    -2389, -692, 360, 137,
    -2156, -2204, -9, 280,
    -266, 119, 39, 193,
    78, -59, -120, 226,
    -975, -858, -781, -1095,
    -619, -413, -451, -842,
    -1216, -1321, -813, -883,
    -1376, -1615, -394, -428,
    -737, -1113, -549, -790,
    -880, -975, -967, -642,
    -985, -886, -1273, -1361,
    -473, -804, -1401, -1407,
    160, -265, -919, -275,
    -248, -250, -718, -380,
    97, -103, -375, -229,
    -415, -193, -135, -555,
    628, 361, 119, 216,
    579, 364, 391, 209,
    634, 522, -154, -148,
    526, 389, 170, 33,
    105, 267, 64, 380,
    -1503, -1000, -30, -369,
    -1070, 58, 647, 223,
    -1520, -291, 621, 307,
    -1531, 156, 762, 404,
    -2029, 141, 734, 499,
    -1849, -650, 306, 512,
    -187, -104, -59, 438,
    134, -230, 156, -186,
    -61, -260, -16, 10,
    -569, -3, -421, -297,
    -1725, -521, -346, 178,
    -1362, -59, -44, 157,
    -2146, -461, -470, -349,
    -2170, -1, -369, -121,
    -1579, -373, -900, -1015,
    -1117, -591, -613, -784,
    -561, 122, -75, -449,
    -4, -171, -123, -372,
    192, 168, -76, -132,
    252, -107, 340, 210,
    392, 509, 272, 181,
    -109, 145, 218, 119,
    -416, -263, 485, 265,
    -181, -8, -286, 226,
    -244, -218, 69, -290,
    -158, 191, -1, -64,
    -592, -90, 213, -96,
    255, 435, 178, -80,
    -369, -18, -33, -80,
    -42, 415, 140, -222,
    1143, 651, 649, 329,
    767, 556, 249, 235,
    948, 413, 442, 279,
    141, 339, 356, 557,
    -470, -170, 99, 237,
    -569, -800, 352, 565,
    282, 473, 470, 332,
    -199, -690, -1284, -917,
    -193, -426, -800, -1122,
    -26, -371, -490, -193,
    637, 595, 519, 330,
    408, -115, 79, 12,
    477, 87, -103, -376,
    -666, -347, -277, -291,
    -510, -481, 169, 297,
    -829, -738, -205, -171,
    -320, -540, 328, 283,
    -859, -958, 442, -2,
    556, 686, 130, 56,
    1383, 1012, 755, 427,
    612, 741, 628, 553,
    -339, -796, 134, 277,
    -633, -1085, -2, -246,
    -880, -1035, -1607, -1064,
    -994, -474, -1138, -488,
    -414, -795, 73, -206,
    -8, -139, 439, 204,
    -176, -578, 23, 131,
    -269, -757, -191, 245,
    -109, -338, 112, 316,
    120, -406, -118, 611,
    -180, -186, -645, 115,
    -173, 34, -518, -489,
    -151, 61, -583, -844,
    220, -138, -681, -1020,
    391, -17, -598, -321,
    157, -295, 129, 155,
    -926, -875, -987, 285,
    241, -83, -125, -125,
    620, 597, 432, 92,
    393, 78, 409, 61,
    -393, -739, -413, -748,
    83, 54, 361, 27,
    -1084, 130, -337, -694,
    -1565, 297, 318, -19,
    -1873, 36, 51, -317,
    -2323, -246, 231, -84,
    -2306, -783, 40, -179,
    -2233, -930, -474, -462,
    -754, -86, -288, -626,
    -2411, -455, -63, 171,
    -1099, -1094, -26, -143,
    -1193, -455, -406, -381,
    -605, -210, -96, -51,
    -580, -476, -276, -15,
    -1195, -634, -1203, -881,
    -378, -221, -669, -952,
    594, 178, -403, -676,
    763, 327, 601, 290,
    172, 300, 203, 157,
    -56, -336, 356, 24,
    -228, -296, -259, -29,
    -186, 263, 416, 14,
    -353, 373, -12, -216,
    257, 96, 174, 57,
    -1526, -616, -954, -499,
    -497, -152, -333, 125,
    105, 200, 179, -97,
    -331, -224, 765, 697,
    760, 256, 301, 59,
    455, -85, 204, 288,
    -514, 240, 251, -109,
    256, 417, -34, -413,
    101, 430, 384, 156,
    -31, -10, 206, 426,
    589, 145, 143, 71,
    808, 906, 333, 349,
    986, 938, 589, 331,
    1300, 824, 187, 509,
    1062, 653, 379, 466,
    1462, 937, 401, 274,
    787, 861, 265, 2,
    609, 553, 28, 305,
    926, 340, 106, 386,
    241, -267, -147, 225,
    -178, -534, 347, 502,
    -643, -381, 397, 30,
    -651, -733, -435, 398,
    -407, -726, -484, -248,
    -789, -914, -438, -476,
    -498, -390, 75, -295,
    -964, -590, -606, 150,
    -121, -49, -155, -78,
    935, 550, 389, 38,
    -321, 127, 424, 315,
    -285, -113, 283, 259,
    658, 203, 322, 486,
    903, 505, 748, 417,
    611, 423, 555, 512,
    239, -83, -578, -19,
    -339, -731, 349, 13,
    -934, -1399, -114, -360,
    107, 692, 182, 90,
    -1243, -1538, -1551, -725,
    -568, -903, -1363, -525,
    -517, -853, -861, -1004,
    -168, -690, -835, 63,
    -137, -556, -547, 144,
    -286, -817, 485, 319,
    -147, -408, 526, 246,
    -347, -434, 297, -28,
    -290, -471, -1110, -1285,
    -460, -359, -988, -794,
    1347, 1299, 690, 523,
    1216, 1068, 1094, 757,
    825, 1140, 752, 494,
    1252, 1365, 1195, 898,
    521, 1053, 532, 432,
    -334, -216, -313, -263,
    -160, 52, -472, -155,
    127, 136, -380, 44,
    851, 410, -162, -489,
    123, -255, -796, -667,
    1090, 917, 789, 493,
    1397, 1197, 558, 202,
    -51, -118, -342, -701,
    83, 108, -42, -441,
    61, 95, 287, 256,
    -27, 89, 524, 531,
    351, 227, 592, 545,
    697, 155, -164, 307,
    638, 274, -489, -50,
    754, 240, -166, -124,
    -116, -579, -1212, -63,
    190, -295, -1040, -1296,
    147, -376, -177, -113,
    841, 1241, 1051, 668,
    2, 293, 551, 304,
    -1096, -953, -248, 376,
    -750, -965, 87, 516,
    -275, -516, 689, 391,
    -379, -643, 876, 594,
    -390, -1013, -645, 573,
    -107, -568, -689, -826,
    -1025, -27, -328, -203,
    861, 749, 548, 233,
    -1660, -1043, 451, 108,
    -660, -620, 430, 236,
    21, -396, -1158, -631,
    1372, 1298, 967, 577,
    1125, 1125, 589, 454,
    -323, -865, -467, 153,
    -468, -699, -804, -509,
    -392, -718, -204, -35,
    -603, -1093, -567, -162,
    -505, -1004, -102, 350,
    219, 224, 423, 252,
    395, 591, 608, 363,
    -746, -96, 373, 172,
    171, 295, 714, 339,
    233, 77, 107, 277,
    157, 153, -499, -356,
    1547, 1073, 576, 494,
    -292, -339, -504, -592,
    -903, -72, -619, -481,
    -1594, -1117, -567, -254,
    -793, -507, -564, -291,
    -492, -532, 502, 560,
    -382, 427, 600, 230,
    -227, 477, 251, 75,
    285, 842, 813, 476,
    -1310, -1333, 186, 377,
    -587, -917, 643, 381,
    -1186, -553, 411, 82,
    -1127, -820, -174, -540,
    -604, 119, 543, 205,
    -380, 657, 909, 567,
    112, -298, -374, 114,
    -857, -251, 56, 159,
    401, 345, -34, -140,
    -111, -607, 41, 614,
    355, -114, -77, 474,
    578, 56, 1450, 924,
    1098, 1420, 741, 400,
    246, 22, 588, 313,
    -121, 327, 831, 472,
    -1138, -608, 856, 552,
    -1241, -1072, 638, 600,
    -358, 254, -333, -303,
    -646, 739, 358, 74,
    1226, 1671, 1221, 849,
    2241, 1624, 983, 636,
    1841, 1477, 749, 384,
    350, 263, 87, 128,
    -1902, -941, -144, -64,
    -1734, -255, 288, -31,
    -2644, -1238, 366, 235,
    -1643, -1092, -1344, -304,
    -541, -1075, -1116, 123,
    -1178, -252, -816, -180,
    -1016, 533, 565, 233,
    -487, -430, -188, 334,
    867, 1236, 534, 171,
    -1590, -1607, 635, 630,
    -2196, 310, 924, 412,
    -2358, -328, 956, 529,
    -2639, -377, 630, 278,
    -2602, 317, 799, 299,
    -2406, 133, 340, 31,
    -2156, -1468, 131, 125,
    -1184, -490, -139, 46,
    -744, 447, 891, 564,
    67, -451, 646, 604,
    -553, -429, -876, 396,
    162, -66, 1305, 915,
    479, 579, 1088, 794,
    450, 278, 566, 324,
    -1057, -154, 148, -177,
    -2545, 168, 1070, 592,
    -2351, -42, 819, 345,
    -2344, -707, 721, 250,
    -2175, -1497, -309, 122,
    -78, -73, 120, 173,
    -4, 262, -263, -261,
    -431, -64, -405, -732,
    -2609, 116, -83, -193,
    -1525, -944, -477, -725,
    -508, 307, 170, 172,
    832, 417, 832, 686,
    -225, 177, 894, 818,
    -482, -389, 1279, 1039,
    -383, 201, -350, 40,
    730, 635, 226, 526,
    503, 462, 338, 398,
    535, 714, 40, -282,
    1482, 1471, 1085, 731,
    1561, 1072, 909, 693,
    1419, 1282, 889, 879,
    1153, 728, 1186, 840,
    -226, 1130, 949, 689,
    -494, -986, -1556, -128,
    -568, -721, -713, -26,
    317, 524, 70, 135,
    -405, -865, -1766, -652,
    -174, -801, 885, 773,
    -153, -91, 1099, 751,
    -506, -1149, 853, 646,
    241, 782, 519, 539,
    1853, 1700, 1101, 684,
    -1249, -1486, -464, 188,
    -893, -1409, -1312, -341,
    -135, 438, -175, 18,
    1111, 976, 319, 208,
    -1430, -1768, 83, 458,
    -530, -1000, 307, 129,
    -840, -15, -29, -356,
    -911, -924, -1147, -242,
    -119, -528, 127, -133,
    -761, -765, 190, -83,
    -315, 895, 522, 231,
    -222, 102, -63, -428,
    316, 699, 379, 70,
    25, 716, 314, -108,
    507, 874, 566, 238,
    108, 941, 519, 195,
    425, -60, -427, 257,
    139, -103, -630, 446,
    334, 370, 412, 48,
    -172, -690, -283, 557,
    187, -286, 158, 483,
    140, 270, -344, -631,
    924, 579, -116, 132,
    142, 466, -68, -64,
    230, -145, -302, -542,
    -803, -912, 1018, 737,
    -773, 1015, 630, 297,
    -2596, 95, 445, 336,
    -2122, 491, 510, 191,
    -1253, 161, -2, -324,
    -1450, -633, -712, -105,
    -842, -254, -411, 100,
    -640, -290, 1010, 763,
    -650, 313, 1169, 730,
    140, 505, 1030, 766,
    772, 287, 1067, 823,
    495, 749, 305, 323,
    -164, 462, 78, 399,
    -342, -874, 69, 597,
    -16, 620, 621, 337,
    -138, -444, -265, 218,
    84, -450, 953, 666,
    -222, -803, 541, 604,
    -921, -1376, 244, 116,
    -841, -723, 630, 588,
    140, 663, 294, 368,
    935, 1046, 881, 759,
    1746, 1464, 916, 628,
    436, 963, 281, 1,
    -119, 74, 542, 213,
    1, -567, 301, 241,
    260, 435, 222, 396,
    936, 957, 1108, 703,
    510, 506, 808, 478,
    601, 694, 960, 620,
    972, 741, 980, 600,
    834, 717, 767, 684,
    643, 972, 935, 638,
    501, 661, 720, 851,
    -105, -632, -303, -117,
    -429, 130, 789, 442,
    -522, -188, 704, 373,
    -759, 42, 814, 523,
    -531, -1137, 373, 578,
    -682, -1203, -455, 285,
    -1163, -1577, -1098, 44,
    81, -82, 712, 363,
    477, 246, 954, 622,
    1604, 1622, 1277, 891,
    1409, 859, 924, 892,
    774, 1041, 947, 1142,
    40, -546, -75, 288,
    -616, -106, -697, -26,
    -169, -160, -891, -739,
    -279, -384, -1029, -350,
    1781, 1308, 1046, 816,
    1580, 1533, 1472, 1178,
    1505, 1076, 1216, 899,
    890, 904, 564, 654,
    920, 692, 1021, 856,
    -493, 132, 177, 505,
    71, 195, -28, 97,
    456, 351, -164, 88,
    439, 278, -40, 350,
    1395, 949, 234, -95,
    -805, -472, 38, -163,
    367, -98, 489, 523,
    1025, 1178, 1212, 906,
    319, 1314, 814, 461,
    -123, -543, -804, 447,
    -748, -324, -897, -1127,
    -737, -501, -789, -713,
    715, 777, 1239, 922,
    1949, 1939, 1368, 865,
    730, 880, 758, 388,
    -871, 454, 17, -251,
    -381, -810, -1583, 239,
    -521, -966, -792, 259,
    -890, -1358, -770, -73,
    166, 349, -212, 323,
    -840, -301, 473, 435,
    -679, -464, 728, 351,
    -156, -199, 667, 432,
    29, -252, 415, 480,
    -731, -379, 145, 559,
    -528, -631, -1158, -159,
    445, 273, 123, 639,
    373, -126, 800, 568,
    84, -162, 720, 712,
    -830, -536, -185, 222,
    408, 452, 501, 771,
    -897, -1355, -67, 442,
    -792, -1406, 566, 602,
    167, -326, 509, 330,
    -95, -626, -730, -344,
    1668, 1217, 779, 455,
    1316, 828, 584, 719,
    404, -31, 1013, 789,
    89, 107, 891, 549,
    871, 1581, 917, 671,
    866, 1479, 1289, 854,
    391, 1068, 1122, 812,
    78, -562, 345, 563,
    429, -103, 417, 787,
    -122, -437, 411, 788,
    -913, -417, 602, 754,
    -226, -16, 151, 760,
    -700, 118, -104, -14,
    -1128, 48, 284, 393,
    -390, -419, -639, -116,
    -910, 306, 316, -13,
    1207, 984, 821, 669,
    -1195, -693, 140, -213,
    -884, -416, -199, -558,
    -616, 245, -404, -664,
    262, 56, -617, -724,
    -85, -491, -320, -656,
    -570, -831, -129, -528,
    -1506, -63, -367, -385,
    -358, -321, 4, 51,
    -366, -214, 319, 511,
    146, 671, -17, -291,
    -110, 464, -139, -496,
    -202, 220, -312, -631,
    -660, -73, -655, -820,
    -662, -653, -1288, -857,
    -430, -953, -959, -264,
    -49, -468, -72, -381,
    -350, -563, -193, -407,
    55, -408, -803, 11,
    -309, 649, 188, -198,
    -512, 461, -79, -458,
    -1318, -263, -134, -523,
    -1657, -435, -495, -765,
    57, -347, -414, 434,
    -1141, -242, -664, -857,
    34, -68, -707, -338
};



/* third codebook for MR475, MR515 */

const Word16 mr515_3_lsf[MR515_3_SIZE*4] =
{
    419,   163,   -30,  -262,
    -455,  -789, -1430,  -721,
    1006,   664,   269,    25,
    619,   260,   183,    96,
    -968, -1358,  -388,   135,
    -693,   835,   456,   154,
    1105,   703,   569,   363,
    1625,  1326,   985,   748,
    -220,   219,    76,  -208,
    -1455, -1662,    49,   149,
    -964,  -172,  -752,  -336,
    625,   209,  -250,   -66,
    -1017,  -838,    -2,   317,
    -2168, -1485,  -138,   123,
    -1876, -2099,  -521,    85,
    -967,  -366,  -695,  -881,
    -921, -1011,  -763,  -949,
    -124,  -256,  -352,  -660,
    178,   463,   354,   304,
    -1744,  -591,  -282,    79,
    -2249,   175,   867,   499,
    -138,  -180,  -181,   -21,
    -2291, -1241,  -460,  -520,
    -771,   451,   -10,  -308,
    271,   -65,     4,   214,
    -279,  -435,   -43,  -348,
    -670,    35,   -65,  -211,
    806,   535,    85,   297,
    57,   239,   722,   493,
    225,   661,   840,   547,
    -540,  -376,    14,   349,
    469,   721,   331,   162,
    -544,  -752,   -62,   -10,
    398,   -88,   724,   701,
    -19,  -533,   -94,   601,
    136,   -71,  -681,  -747,
    -166,  -344,   261,   -50,
    161,   -52,   485,   337,
    -1675,    50,   190,   -93,
    -2282,  -231,  -194,   -82,
    -95,  -595,  -154,   128,
    894,   501,   588,   457,
    -345,   206,   122,   110,
    -631,  -227,  -569,     3,
    408,   239,   397,   226,
    -197,    -2,   128,   491,
    1281,   904,   292,   215,
    538,   306,   259,   509,
    -677, -1047,    13,   321,
    -679,  -588,  -358,  -212,
    -558,   243,   646,   479,
    486,   342,   634,   532,
    107,   802,   331,   136,
    -112,  -398, -1031,  -286,
    -326,  -705,   288,   272,
    1299,  1144,  1178,   860,
    -423,   121,  -385,  -148,
    -295,  -302,  -834,  -819,
    16,   -24,  -201,  -476,
    555,    91,  -245,   294,
    -38,  -379,  -962, -1221,
    -1191, -1518,  -273,  -395,
    -390, -1013,  -645,   573,
    -1843, -1030,   505,   468,
    744,   947,   609,   493,
    -689, -1172,  -628,  -135,
    -1026,   195,   411,   196,
    1582,  1147,   575,   337,
    -1239,  -777,  -648,  -142,
    595,   825,   967,   735,
    -1206,  -970,   -81,  -342,
    -745,    13,   -72,   375,
    454,    19,  1407,   921,
    -1647,  -172,   861,   562,
    928,  1537,  1063,   740,
    -2472,  -952,   264,    82,
    -502,  -965, -1334,   123,
    867,  1236,   534,   171,
    -2320,  -460,   780,   363,
    -1190,  -617,   252,   -61,
    -174,    34,  1011,   788,
    -2333,   247,   423,   153,
    -16,  -355,   262,   449,
    -1576, -1073,  -544,  -371,
    -615,  -305,  1051,   805,
    687,   528,     6,  -182,
    935,   875,  1002,   809,
    199,   257,   126,    76,
    -584, -1138,   599,   556,
    -1105, -1391, -1591,  -519,
    -977, -1325,   108,   347,
    -722,  -975,   365,   101,
    -145,   681,   249,  -153,
    0,  -334,  -570,   159,
    412,   285,  -336,  -617,
    -953,  -966,   887,   689,
    -1251,    84,  -185,  -398,
    -592,   433,  1044,   653,
    85,   329,   -40,   361,
    -433,  -705,   466,   574,
    -154,   654,   592,   290,
    -167,    72,   349,   175,
    674,   297,   977,   720,
    1235,  1204,   757,   488,
    -400,  -269,   538,   372,
    -1350, -1387, -1194,   -91,
    1262,   876,   775,   700,
    -599,   -38,  -430,  -722,
    1976,  1630,   991,   608,
    111,   276,  -226,   -96,
    -947,  -388,   -11,    -7,
    -303,  -531,  -839,   338,
    1734,  1710,  1405,  1013,
    -516,  -855,  -645,   210,
    -688,  -416,   513,   230,
    -822,  -637, -1146,  -320,
    -952,  -658,  -694,   183,
    -114,  -623,   818,   674,
    -191,  -204,   731,   635,
    51,  1221,   883,   576,
    -954,  -431,   826,   598,
    -342,  -755,  -900,  -407,
    -1126,  -354,  -206,  -512,
    -547,  -810,  -357,  -620,
    66,   515,   -73,  -410,
    -872,  -945, -1444, -1227,
    191,   -17,  -544,  -231,
    -1540,  -544,  -901,  -886
};

/* first codebook for MR795 */

const Word16 mr795_1_lsf[MR795_1_SIZE*3] =
{
    -890, -1550, -2541,
    -819,  -970,   175,
    -826, -1234,  -762,
    -599,   -22,   634,
    -811,  -987,  -902,
    -323,   203,    26,
    -383,  -235,  -781,
    -399,  1262,   906,
    -932, -1399, -1380,
    -624,    93,    87,
    -414,  -539,  -691,
    37,   633,   510,
    -387,  -476, -1330,
    399,    66,   263,
    -407,   -49,  -335,
    -417,  1041,  1865,
    -779, -1089, -1440,
    -746,  -858,   832,
    -581,  -759,  -371,
    -673,  -506,  2088,
    -560,  -634, -1179,
    271,   241,    14,
    -438,  -244,  -397,
    463,  1202,  1047,
    -606,  -797, -1438,
    -51,  -323,   481,
    -224,  -584,  -527,
    494,   881,   682,
    -433,  -306, -1002,
    554,   659,   222,
    171,  -160,  -353,
    681,  1798,  1565,
    -852, -1181, -1695,
    -336,  -666,   114,
    -581,  -756,  -744,
    -195,   375,   497,
    -465,  -804, -1098,
    154,   282,  -131,
    -50,  -191,  -719,
    323,   732,  1542,
    -722,  -819, -1404,
    105,  -250,   185,
    -178,  -502,  -742,
    321,   510,  1111,
    -323,  -567,  -966,
    127,   484,   338,
    -160,    52,  -338,
    732,  1367,  1554,
    -626,  -802, -1696,
    -286,  -586,   676,
    -695,  -343,  -370,
    -490,   295,  1893,
    -630,  -574, -1014,
    -80,   645,   -69,
    -6,  -318,  -364,
    782,  1450,  1038,
    -313,  -733, -1395,
    120,    60,   477,
    -264,  -585,  -123,
    711,  1245,   633,
    -91,  -355, -1016,
    771,   758,   261,
    253,    81,  -474,
    930,  2215,  1720,
    -808, -1099, -1925,
    -560,  -782,   169,
    -804, -1074,  -188,
    -626,   -55,  1405,
    -694,  -716, -1194,
    -660,   354,   329,
    -514,   -55,  -543,
    366,  1033,  1182,
    -658,  -959, -1357,
    -55,  -184,    93,
    -605,  -286,  -662,
    404,   449,   827,
    -286,  -350, -1263,
    628,   306,   227,
    -16,   147,  -623,
    186,   923,  2146,
    -674,  -890, -1606,
    -443,  -228,   339,
    -369,  -790,  -409,
    231,    86,  1469,
    -448,  -581, -1061,
    594,   450,  -177,
    -124,  -170,  -447,
    671,  1159,  1404,
    -476,  -667, -1511,
    -77,  -138,   716,
    -177,  -372,  -381,
    451,   934,   915,
    -250,  -432,  -822,
    272,   828,   446,
    26,    19,   -31,
    698,  1692,  2168,
    -646,  -977, -1924,
    -179,  -473,   268,
    -379,  -745,  -691,
    11,   127,  1033,
    -488,  -917,  -825,
    61,   323,   135,
    147,  -145,  -686,
    685,   786,  1682,
    -506,  -848, -1297,
    35,    90,   222,
    -23,  -346,  -670,
    455,   591,  1287,
    -203,  -593, -1086,
    652,   352,   437,
    39,    63,  -457,
    841,  1265,  2105,
    -520,  -882, -1584,
    -328,  -711,  1421,
    -596,  -342,   -70,
    209,   173,  1928,
    -423,  -598,  -921,
    421,   605,   -38,
    -2,  -245,  -127,
    896,  1969,  1135,
    -379,  -518, -1579,
    173,   118,   753,
    -55,  -381,   -52,
    985,  1021,   753,
    -2,  -291,  -891,
    753,   992,   423,
    264,   131,  -196,
    895,  2274,  2543,
    -635, -1088, -2499,
    -529,  -982,   526,
    -764,  -830,  -548,
    -436,   316,   599,
    -675,  -940,  -746,
    -57,   236,   -11,
    -201,   -81,  -798,
    16,   845,  1558,
    -737,  -985, -1212,
    -468,    17,   290,
    -279,  -584,  -700,
    183,   822,   705,
    -265,  -492, -1187,
    421,   152,   468,
    -390,   166,  -268,
    39,  1550,  1868,
    -635,  -966, -1571,
    -453,  -492,   910,
    -284, -1027,   -75,
    -181,  -133,  1852,
    -445,  -624, -1174,
    420,   367,   -49,
    -389,  -212,  -169,
    707,  1073,  1208,
    -539,  -710, -1449,
    83,  -163,   484,
    -236,  -543,  -355,
    338,  1175,   814,
    -246,  -309,  -958,
    606,   760,    60,
    166,    -8,  -163,
    -306,  1849,  2563,
    -747, -1025, -1783,
    -419,  -446,   209,
    -718,  -566,  -534,
    -506,   693,   857,
    -463,  -697, -1082,
    325,   431,  -206,
    -15,    -8,  -763,
    545,   919,  1518,
    -611,  -783, -1313,
    256,   -55,   208,
    -165,  -348,  -662,
    321,   680,   930,
    -326,  -429,  -951,
    484,   446,   570,
    -197,    72,   -73,
    909,  1455,  1741,
    -563,  -737, -1974,
    -124,  -416,   718,
    -478,  -404,  -314,
    -16,   446,  1636,
    -551,  -537,  -750,
    -58,   638,   214,
    55,  -185,  -271,
    1148,  1301,  1212,
    -483,  -671, -1264,
    117,   285,   543,
    -204,  -391,  -111,
    513,  1538,   854,
    -114,  -190,  -978,
    877,   595,   464,
    260,   260,  -311,
    748,  2283,  2216,
    -517,  -945, -2171,
    -326,  -708,   378,
    -812,  -691,  -232,
    -560,   687,  1409,
    -732,  -690,  -836,
    -359,   645,   386,
    -265,    62,  -678,
    145,  1644,  1208,
    -555,  -988, -1233,
    -78,    14,   114,
    -327,  -358,  -489,
    392,   677,   697,
    -201,  -236, -1140,
    693,   449,   178,
    -243,   256,  -433,
    611,  1385,  2456,
    -612,  -901, -1464,
    -307,   -17,   499,
    -315,  -667,  -254,
    256,   428,  1463,
    -486,  -422, -1056,
    655,   370,    18,
    -102,  -185,  -276,
    755,  1578,  1335,
    -488,  -603, -1418,
    182,   -93,   870,
    -73,  -458,  -348,
    835,   862,   957,
    -282,  -333,  -746,
    547,   839,   428,
    273,   -89,    13,
    940,  1708,  2576,
    -418, -1084, -1758,
    -44,  -358,   259,
    -497,  -643,  -560,
    99,   557,   961,
    -421,  -766,  -917,
    295,   326,   184,
    175,    15,  -626,
    532,   878,  1981,
    -443,  -768, -1275,
    221,   156,   268,
    39,  -363,  -505,
    695,   772,  1140,
    -162,  -459,  -912,
    709,   444,   658,
    25,   303,  -312,
    1268,  1410,  1715,
    -297,  -766, -1836,
    -263,  -108,  1070,
    -406,   -13,  -129,
    57,   438,  2734,
    -374,  -487,  -835,
    304,   696,   164,
    104,  -235,     5,
    1611,  1900,  1399,
    -229,  -582, -1325,
    405,   192,   817,
    -87,  -438,   111,
    1028,  1199,   993,
    68,  -175,  -934,
    1033,  1117,   451,
    478,   200,  -248,
    2127,  2696,  2042,
    -835, -1323, -2131,
    -799,  -692,   466,
    -812, -1032,  -469,
    -622,   288,   920,
    -701,  -841, -1070,
    -411,   512,     8,
    -390,   -91,  -744,
    -30,  1043,  1161,
    -822, -1148, -1156,
    -294,   -46,   110,
    -411,  -374,  -678,
    214,   531,   668,
    -406,  -420, -1194,
    487,   232,   303,
    -318,    91,  -472,
    123,  1232,  2445,
    -722,  -952, -1495,
    -738,  -675,  1332,
    -543,  -606,  -211,
    -95,   -98,  1508,
    -549,  -514, -1193,
    473,   211,    73,
    -288,  -112,  -389,
    537,  1332,  1258,
    -567,  -755, -1545,
    71,  -283,   632,
    -170,  -481,  -493,
    681,  1002,   817,
    -356,  -331,  -877,
    419,   706,   346,
    241,   -34,  -326,
    377,  1950,  1883,
    -727, -1075, -1625,
    -233,  -543,   116,
    -524,  -806,  -585,
    -73,   478,   729,
    -288,  -925, -1143,
    173,   447,   -52,
    68,  -229,  -606,
    449,   529,  1797,
    -591,  -875, -1363,
    183,  -144,   324,
    -103,  -452,  -666,
    623,   488,  1176,
    -238,  -511, -1004,
    326,   552,   458,
    136,   108,  -319,
    626,  1343,  1883,
    -490,  -646, -1730,
    -186,  -449,   984,
    -738,   -76,  -170,
    -550,   755,  2560,
    -496,  -510,  -947,
    210,   694,   -52,
    84,  -322,  -199,
    1090,  1625,  1224,
    -376,  -603, -1396,
    343,    74,   632,
    -175,  -502,   -32,
    972,  1332,   734,
    52,  -295, -1113,
    1065,   918,   160,
    393,   107,  -397,
    1214,  2649,  1741,
    -632, -1201, -1891,
    -719,  -277,   353,
    -651,  -880,  -122,
    -211,   209,  1338,
    -562,  -714, -1059,
    -208,   388,   159,
    -320,   -61,  -551,
    293,  1092,  1443,
    -648,  -865, -1253,
    -49,  -143,   305,
    -401,  -227,  -585,
    561,   532,   927,
    -117,  -443, -1188,
    507,   436,   292,
    -79,   233,  -458,
    671,  1025,  2396,
    -633,  -842, -1525,
    -308,  -286,   640,
    -373,  -621,  -407,
    418,   253,  1305,
    -315,  -581, -1137,
    572,   685,  -281,
    61,   -68,  -371,
    991,  1101,  1498,
    -493,  -683, -1362,
    -47,   164,   704,
    -256,  -314,  -268,
    631,   949,  1052,
    -118,  -348,  -833,
    68,  1180,   568,
    152,   117,    34,
    1113,  1902,  2239,
    -601,  -959, -1706,
    -143,  -489,   480,
    -332,  -655,  -574,
    54,   353,  1192,
    -462,  -652,  -796,
    150,   549,   112,
    195,  -111,  -515,
    679,  1108,  1647,
    -558,  -749, -1217,
    -9,   272,   341,
    -53,  -265,  -535,
    489,   843,  1298,
    -120,  -482, -1032,
    632,   543,   408,
    179,   306,  -526,
    1124,  1464,  2244,
    -417,  -786, -1562,
    -224,  -384,  1364,
    -377,  -459,   -25,
    385,   489,  2174,
    -332,  -651,  -829,
    544,   553,    61,
    22,  -113,   -89,
    1128,  1725,  1524,
    -216,  -373, -1653,
    161,   316,   908,
    -165,  -222,   -67,
    1362,  1175,   789,
    73,  -252,  -767,
    738,   932,   616,
    362,   246,  -126,
    787,  2654,  3027,
    -691, -1106, -2190,
    -565,  -588,   524,
    -590,  -979,  -490,
    -263,   397,   982,
    -577,  -837,  -945,
    -22,   435,   -49,
    -190,  -118,  -629,
    -88,  1240,  1513,
    -636, -1051, -1019,
    -291,   189,   259,
    -257,  -470,  -629,
    145,   945,   894,
    -326,  -364, -1094,
    543,   260,   630,
    -202,   189,  -209,
    357,  1379,  2091,
    -569, -1075, -1449,
    -714,  -239,   919,
    -420,  -705,   -84,
    -109,  -114,  2407,
    -413,  -529, -1177,
    482,   368,   131,
    -186,   -72,  -131,
    861,  1255,  1220,
    -611,  -658, -1341,
    227,  -121,   631,
    -176,  -489,  -218,
    745,  1175,   957,
    -321,  -148,  -936,
    671,   966,   216,
    340,    -3,  -143,
    469,  1848,  2437,
    -729,  -961, -1683,
    -213,  -254,   321,
    -511,  -438,  -521,
    -126,   725,   903,
    -340,  -685, -1032,
    316,   480,    20,
    23,   -89,  -551,
    353,  1051,  1789,
    -544,  -757, -1364,
    298,   -25,   436,
    -100,  -392,  -519,
    467,   754,  1078,
    -210,  -398, -1078,
    620,   658,   630,
    33,   147,  -178,
    921,  1687,  1921,
    -325,  -528, -1978,
    2,  -285,   910,
    -371,  -490,  -230,
    0,   597,  2010,
    -496,  -395,  -834,
    37,   945,   245,
    181,  -160,  -144,
    1481,  1373,  1357,
    -355,  -601, -1270,
    298,   322,   672,
    -193,  -336,    77,
    1089,  1533,   922,
    177,   -39, -1125,
    996,   781,   536,
    456,   366,  -432,
    1415,  2440,  2279,
    -466,  -758, -2325,
    -303,  -509,   387,
    -727,  -557,    66,
    -145,   643,  1248,
    -544,  -676,  -916,
    -225,   862,   588,
    -152,    40,  -533,
    423,  1423,  1558,
    -572,  -843, -1145,
    -128,    85,   461,
    -238,  -257,  -584,
    605,   748,   861,
    24,  -202, -1409,
    797,   487,   303,
    -181,   364,  -182,
    616,  1378,  2942,
    -494,  -852, -1441,
    -292,    61,   812,
    -84,  -723,  -182,
    555,   532,  1506,
    -365,  -493, -1057,
    822,   588,    11,
    -14,   -18,  -230,
    1001,  1401,  1451,
    -474,  -569, -1292,
    302,    62,  1062,
    -70,  -376,  -222,
    982,   974,  1149,
    -196,  -234,  -795,
    479,  1098,   499,
    362,    58,    70,
    1147,  2069,  2857,
    -487,  -878, -1824,
    73,  -288,   348,
    -358,  -500,  -508,
    199,   721,  1242,
    -78,  -697,  -795,
    361,   536,   196,
    374,   110,  -735,
    847,  1051,  1896,
    -366,  -713, -1182,
    315,   320,   429,
    72,  -215,  -450,
    759,   886,  1363,
    -30,  -428,  -834,
    861,   627,   796,
    118,   468,  -279,
    1355,  1883,  1893,
    -188,  -642, -1612,
    63,  -175,  1198,
    -418,  -211,    51,
    414,   587,  2601,
    -234,  -557,  -858,
    424,   889,   222,
    136,  -101,    83,
    1413,  2278,  1383,
    -84,  -445, -1389,
    414,   313,  1045,
    29,  -343,    65,
    1552,  1647,   980,
    183,   -91,  -829,
    1273,  1413,   360,
    553,   272,  -107,
    1587,  3149,  2603
};



