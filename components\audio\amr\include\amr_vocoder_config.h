#pragma once
#ifdef __cplusplus
extern "C" {
#endif

#ifndef FEATURE_AUDIO_AMR_DEC
#define FEATURE_AUDIO_AMR_DEC       (FEATURE_AUDIO_NBAMR_DEC || FEATURE_AUDIO_WBAMR_DEC)
#endif

#ifndef FEATURE_AUDIO_AMR_ENC
#define FEATURE_AUDIO_AMR_ENC       (FEATURE_AUDIO_NBAMR_ENC || FEATURE_AUDIO_WBAMR_ENC)
#endif

#ifndef AMR_DEC_FRAMES_PER_REQUEST
#define AMR_DEC_FRAMES_PER_REQUEST  (10)
#endif

#ifndef AMR_DEC_STREAM_SUPPORT
#define AMR_DEC_STREAM_SUPPORT      (0)
#endif

#ifdef __cplusplus
}
#endif
