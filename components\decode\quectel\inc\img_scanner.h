
#ifndef _IMG_SCANNER_H_
#define _IMG_SCANNER_H_

#include <quec_decoder.h>

/* internal image scanner APIs for 2D readers */

extern quec_decoder_symbol_t *_quec_decoder_image_scanner_alloc_sym(quec_decoder_image_scanner_t*,
                                                    quec_decoder_symbol_type_t,
                                                    int);
extern void _quec_decoder_image_scanner_add_sym(quec_decoder_image_scanner_t*,
                                        quec_decoder_symbol_t*);
extern void _quec_decoder_image_scanner_recycle_syms(quec_decoder_image_scanner_t*,
                                             quec_decoder_symbol_t*);

#endif
