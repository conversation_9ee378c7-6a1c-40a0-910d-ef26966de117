/*
 * Copyright (C) 2014 The Android Open Source Project
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *  * Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *  * Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

#ifndef MP3READER_H_
#define MP3READER_H_
#include <stdlib.h>
#include <stdio.h>
#include <assert.h>
#include <string.h>
#include <stdint.h>
#include "helios_audio_fs.h"

extern "C" {
//#include <crossPlatformSW.h>
#if 1//def QUECTEL_PROJECT_CUST	
//#include "ql_fs.h"
#include "ring_buffer.h"
#endif
class Mp3Reader {
public:
    Mp3Reader();
//#if 1//def QUECTEL_PROJECT_CUST		
    bool init(const char *file, uint32_t offset);
	bool init2(const char *data, uint32_t size);
	bool getFrame2(void *buffer, uint32_t *size);
	void close2();
//#endif	
    bool getFrame(void *buffer, uint32_t *size);
    uint32_t getSampleRate() { return mSampleRate;}
    uint32_t getNumChannels() { return mNumChannels;}
    void close();
    ~Mp3Reader();
private:
#if 1//def QUECTEL_PROJECT_CUST	
	HeliosAudFILE *mFp;
#else
	HeliosAudFILE*    mFp;
#endif
    uint32_t mFixedHeader;
    uint32_t  mCurrentPos;
    uint32_t mSampleRate;
    uint32_t mNumChannels;
    uint32_t mBitrate;
#if 1//def QUECTEL_PROJECT_CUST		
	ring_buffer_handler_t mRingbuf;
	bool isReadEnd;
	uint32_t  mReadPos;
	
	// for ram file
	char *mData;
	uint32_t mDataLen;
#endif	
};
}

#endif /* MP3READER_H_ */
