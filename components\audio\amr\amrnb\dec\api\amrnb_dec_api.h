/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2020 ASR Ltd. All Rights Reserved
-------------------------------------------------------------------------------------------------------------------*/

#pragma once
#ifdef __cplusplus
extern "C" {
#endif
#include <stdint.h>

    /**
    * @file amrnb_dec_api.h
    * @brief ASR nb-amr decoder related API describes the process and functions used to decode nb-amr from file|memory to file|memory on ASR RTOS platform.
    */

    typedef struct amrnb_dec_config {
        /** input nb-amr file name, specify it when reading from file is needed */
        const char* name;
        /** output pcm file name, specify it when writing to file is needed */
        const char* out_name;
    }amrnb_dec_config;

    /** nb-amr decoder handle, held and used by nb-amr decoder user */
    typedef uint32_t amrnb_dec_handle;

    /** Start nb-amr decoder with configuration.
    * @param [in] config <tt>const amrnb_dec_config*</tt>: nb-amr decoder configuration
    * @param [in,out] handle <tt>amrnb_dec_handle*</tt>: nb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_decode_open(const amrnb_dec_config* config, amrnb_dec_handle* handle);

    /** Read a frame from nb-amr file into memory.
    * @param [in] handle <tt>amrnb_dec_handle</tt>: nb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_decode_read(amrnb_dec_handle handle);

    /** Write a frame from memory to pcm file.
    * @param [in] handle <tt>amrnb_dec_handle</tt>: nb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_decode_write(amrnb_dec_handle handle);

    /** Read a frame from nb-amr file, decode to pcm and write to pcm file if possible.
    * @param [in] handle <tt>amrnb_dec_handle</tt>: nb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_decode_loop(amrnb_dec_handle handle);

    /** Decode a nb-amr frame in memory to pcm frame in memory.
    * @param [in] handle <tt>amrnb_dec_handle</tt>: nb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_decode_do(amrnb_dec_handle handle);

    /** Set a nb-amr frame in memory.
    * @param [in] handle <tt>amrnb_dec_handle</tt>: nb-amr decoder handle
    * @param [in] data <tt>const uint8_t*</tt>: nb-amr buffer address
    * @param [in] size <tt>uint32_t</tt>: nb-amr buffer size
    * @param [out] used_size <tt>uint32_t*</tt>: nb-amr frame size
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_decode_set(amrnb_dec_handle handle, const uint8_t* data, uint32_t size, uint32_t* used_size);

    /** Get a decoded pcm frame in memory.
    * @param [in] handle <tt>amrnb_dec_handle</tt>: nb-amr decoder handle
    * @param [out] data <tt>int16_t*</tt>: pcm frame address
    * @param [out] size <tt>uint32_t*</tt>: pcm frame size
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_decode_get(amrnb_dec_handle handle, int16_t* data, uint32_t* size);

    /** Get a nb-amr frame in memory.
    * @param [in] handle <tt>amrnb_dec_handle</tt>: nb-amr decoder handle
    * @param [out] data <tt>uint8_t*</tt>: nb-amr frame address
    * @param [out] size <tt>uint32_t*</tt>: nb-amr frame size
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_decode_get_amr(amrnb_dec_handle handle, uint8_t* data, uint32_t* size);

    /** Get current nb-amr rate.
    * @param [in] handle <tt>amrnb_dec_handle</tt>: nb-amr decoder handle
    * @param [out] rate <tt>int32_t*</tt>: nb-amr rate, 0 for 4.75 kbps
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_decode_get_rate(amrnb_dec_handle handle, int32_t* rate);

    /** Free nb-amr decoder resource.
    * @param [in] handle <tt>amrnb_dec_handle</tt>: nb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_decode_close(amrnb_dec_handle handle);

#ifdef __cplusplus
}
#endif