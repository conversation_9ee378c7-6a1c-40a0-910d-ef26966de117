
#include "quec_decoder_config.h"
#include <stdlib.h>     /* malloc, calloc, free */
#include <stdio.h>      /* snprintf */
#include <string.h>     /* memset, strlen */

#include <quec_decoder.h>
#include "decoder.h"
#include "ql_zo_malloc.h" 
#include "ql_imgdeal.h"

#if defined(DEBUG_DECODER) || defined(DEBUG_EAN) ||             \
    defined(DEBUG_CODE39) || defined(DEBUG_I25) ||              \
    defined(DEBUG_CODE128) || defined(DEBUG_QR_FINDER) ||       \
    (defined(DEBUG_PDF417) && (DEBUG_PDF417 >= 4))
# define DEBUG_LEVEL 1
#endif
//#include "debug.h"

quec_decoder_decoder_t *quec_decoder_decoder_create ()
{
    quec_decoder_decoder_t *dcode = calloc(1, sizeof(quec_decoder_decoder_t));
    dcode->buf_alloc = BUFFER_MIN;
    dcode->buf = malloc(dcode->buf_alloc);
    
    /* initialize default configs */
#ifdef ENABLE_EAN
    dcode->ean.enable = 1;
    dcode->ean.ean13_config = ((1 << QUEC_DECODER_CFG_ENABLE) |
                               (1 << QUEC_DECODER_CFG_EMIT_CHECK));
    dcode->ean.ean8_config = ((1 << QUEC_DECODER_CFG_ENABLE) |
                              (1 << QUEC_DECODER_CFG_EMIT_CHECK));
    dcode->ean.upca_config = 1 << QUEC_DECODER_CFG_EMIT_CHECK;
    dcode->ean.upce_config = 1 << QUEC_DECODER_CFG_EMIT_CHECK;
    dcode->ean.isbn10_config = 1 << QUEC_DECODER_CFG_EMIT_CHECK;
    dcode->ean.isbn13_config = 1 << QUEC_DECODER_CFG_EMIT_CHECK;
#endif
#ifdef ENABLE_I25
    dcode->i25.config = 1 << QUEC_DECODER_CFG_ENABLE;
    CFG(dcode->i25, QUEC_DECODER_CFG_MIN_LEN) = 6;
#endif
#ifdef ENABLE_CODE39
    dcode->code39.config = 1 << QUEC_DECODER_CFG_ENABLE;
    CFG(dcode->code39, QUEC_DECODER_CFG_MIN_LEN) = 1;
#endif
#ifdef ENABLE_CODE128
    dcode->code128.config = 1 << QUEC_DECODER_CFG_ENABLE;
#endif
#ifdef ENABLE_PDF417
    dcode->pdf417.config = 1 << QUEC_DECODER_CFG_ENABLE;
#endif
#ifdef ENABLE_QRCODE
    dcode->qrf.config = 1 << QUEC_DECODER_CFG_ENABLE;
#endif

    quec_decoder_decoder_reset(dcode);
    return(dcode);
}

void quec_decoder_decoder_destroy (quec_decoder_decoder_t *dcode)
{
    if(dcode->buf)
    {
        free(dcode->buf);
        //zo_myfree(dcode->buf);//mymalloc_test
    }
    free(dcode);
    //zo_myfree(dcode);//mymalloc_test
}

void quec_decoder_decoder_reset (quec_decoder_decoder_t *dcode)
{
    memset(dcode, 0, (long)&dcode->buf_alloc - (long)dcode);
#ifdef ENABLE_EAN
    ean_reset(&dcode->ean);
#endif
#ifdef ENABLE_I25
    i25_reset(&dcode->i25);
#endif
#ifdef ENABLE_CODE39
    code39_reset(&dcode->code39);
#endif
#ifdef ENABLE_CODE128
    code128_reset(&dcode->code128);
#endif
#ifdef ENABLE_PDF417
    pdf417_reset(&dcode->pdf417);
#endif
#ifdef ENABLE_QRCODE
    qr_finder_reset(&dcode->qrf);
#endif
}

void quec_decoder_decoder_new_scan (quec_decoder_decoder_t *dcode)
{
    /* soft reset decoder */
    memset(dcode->w, 0, sizeof(dcode->w));
    dcode->lock = 0;
    dcode->idx = 0;
#ifdef ENABLE_EAN
    ean_new_scan(&dcode->ean);
#endif
#ifdef ENABLE_I25
    i25_reset(&dcode->i25);
#endif
#ifdef ENABLE_CODE39
    code39_reset(&dcode->code39);
#endif
#ifdef ENABLE_CODE128
    code128_reset(&dcode->code128);
#endif
#ifdef ENABLE_PDF417
    pdf417_reset(&dcode->pdf417);
#endif
#ifdef ENABLE_QRCODE
    qr_finder_reset(&dcode->qrf);
#endif
}


quec_decoder_color_t quec_decoder_decoder_get_color (const quec_decoder_decoder_t *dcode)
{
    return(get_color(dcode));
}

const char *quec_decoder_decoder_get_data (const quec_decoder_decoder_t *dcode)
{
    return((char*)dcode->buf);
}

unsigned int quec_decoder_decoder_get_data_length (const quec_decoder_decoder_t *dcode)
{
    return(dcode->buflen);
}

quec_decoder_decoder_handler_t *
quec_decoder_decoder_set_handler (quec_decoder_decoder_t *dcode,
                          quec_decoder_decoder_handler_t handler)
{
    quec_decoder_decoder_handler_t *result = dcode->handler;
    dcode->handler = handler;
    return(result);
}

void quec_decoder_decoder_set_userdata (quec_decoder_decoder_t *dcode,
                                void *userdata)
{
    dcode->userdata = userdata;
}

void *quec_decoder_decoder_get_userdata (const quec_decoder_decoder_t *dcode)
{
    return(dcode->userdata);
}

quec_decoder_symbol_type_t quec_decoder_decoder_get_type (const quec_decoder_decoder_t *dcode)
{
    return(dcode->type);
}

// quec_decoder_symbol_type_t quec_decoder_decode_width (quec_decoder_decoder_t *dcode,
//                                       unsigned w)
// {
//     dcode->w[dcode->idx & (DECODE_WINDOW - 1)] = w;

//     /* each decoder processes width stream in parallel */
//     quec_decoder_symbol_type_t sym = dcode->type = QUEC_DECODER_NONE;

// #ifdef ENABLE_EAN
//     if((dcode->ean.enable) &&
//        (sym = _quec_decoder_decode_ean(dcode)))
//         dcode->type = sym;
// #endif
// #ifdef ENABLE_CODE39
//     if(TEST_CFG(dcode->code39.config, QUEC_DECODER_CFG_ENABLE) &&
//        (sym = _quec_decoder_decode_code39(dcode)) > QUEC_DECODER_PARTIAL)
//         dcode->type = sym;
// #endif
// #ifdef ENABLE_CODE128
//     if(TEST_CFG(dcode->code128.config, QUEC_DECODER_CFG_ENABLE) &&
//        (sym = _quec_decoder_decode_code128(dcode)) > QUEC_DECODER_PARTIAL)
//         dcode->type = sym;
// #endif
// #ifdef ENABLE_I25
//     if(TEST_CFG(dcode->i25.config, QUEC_DECODER_CFG_ENABLE) &&
//        (sym = _quec_decoder_decode_i25(dcode)) > QUEC_DECODER_PARTIAL)
//         dcode->type = sym;
// #endif
// #ifdef ENABLE_PDF417
//     if(TEST_CFG(dcode->pdf417.config, QUEC_DECODER_CFG_ENABLE) &&
//        (sym = _quec_decoder_decode_pdf417(dcode)) > QUEC_DECODER_PARTIAL)
//         dcode->type = sym;
// #endif
// #ifdef ENABLE_QRCODE
//     if(TEST_CFG(dcode->qrf.config, QUEC_DECODER_CFG_ENABLE) &&
//        (sym = _quec_decoder_find_qr(dcode)) > QUEC_DECODER_PARTIAL)
//         dcode->type = sym;
// #endif

//     dcode->idx++;
//     if(dcode->type) {
//         if(dcode->handler)
//             dcode->handler(dcode);
//         if(dcode->lock && dcode->type > QUEC_DECODER_PARTIAL)
//             dcode->lock = 0;
//     }
//     return(dcode->type);
// }

quec_decoder_symbol_type_t quec_decoder_decode_width (quec_decoder_decoder_t *dcode,
                                      unsigned w, unsigned char flag)
{
    dcode->w[dcode->idx & (DECODE_WINDOW - 1)] = w;

    /* each decoder processes width stream in parallel */
    quec_decoder_symbol_type_t sym = dcode->type = QUEC_DECODER_NONE;
if(flag == BARCODE)
{
    #ifdef ENABLE_EAN
    if(dcode->ean.enable)
    {
        sym = _quec_decoder_decode_ean(dcode);    
        if(sym)
        {
            //DEBUG("EAN sym = %d", sym);
            dcode->type = sym;
        }
            
    }
       
    #endif
    #ifdef ENABLE_CODE39
    if(TEST_CFG(dcode->code39.config, QUEC_DECODER_CFG_ENABLE))
    {
        sym = _quec_decoder_decode_code39(dcode);
       
        if(sym > QUEC_DECODER_PARTIAL)
        {
            //DEBUG("CODE39 sym = %d", sym);
            dcode->type = sym;
        }
           
    }

    #endif
    #ifdef ENABLE_CODE128
    if(TEST_CFG(dcode->code128.config, QUEC_DECODER_CFG_ENABLE))
    {
        sym = _quec_decoder_decode_code128(dcode);
        if(sym > QUEC_DECODER_PARTIAL)
        {
            //DEBUG("CODE128 sym = %d", sym);
            dcode->type = sym;
        }
            
    }
    #endif
    #ifdef ENABLE_I25
    if(TEST_CFG(dcode->i25.config, QUEC_DECODER_CFG_ENABLE) &&
       (sym = _quec_decoder_decode_i25(dcode)) > QUEC_DECODER_PARTIAL)
        dcode->type = sym;
    #endif
}
else
{
    #ifdef ENABLE_PDF417
    if(TEST_CFG(dcode->pdf417.config, QUEC_DECODER_CFG_ENABLE) &&
       (sym = _quec_decoder_decode_pdf417(dcode)) > QUEC_DECODER_PARTIAL)
        dcode->type = sym;
    #endif
    #ifdef ENABLE_QRCODE
    if(TEST_CFG(dcode->qrf.config, QUEC_DECODER_CFG_ENABLE))
    {
        sym = _quec_decoder_find_qr(dcode);
        //DEBUG("dcode->idx = %d", dcode->idx);
        if(sym > QUEC_DECODER_PARTIAL)
        {
            //DEBUG("QR sym = %d", sym);
            dcode->type = sym;
        }
            
    }
    #endif
    #ifdef ENABLE_I25
    if(TEST_CFG(dcode->i25.config, QUEC_DECODER_CFG_ENABLE) &&
       (sym = _quec_decoder_decode_i25(dcode)) > QUEC_DECODER_PARTIAL)
        dcode->type = sym;
    #endif
    #ifdef ENABLE_EAN
    if(dcode->ean.enable)
    {
        sym = _quec_decoder_decode_ean(dcode);    
        if(sym)
        {
            //DEBUG("EAN sym = %d", sym);
            dcode->type = sym;
        }
            
    }
    #endif
    // #ifdef ENABLE_CODE39
    // if(TEST_CFG(dcode->code39.config, QUEC_DECODER_CFG_ENABLE))
    // {
    //     sym = _quec_decoder_decode_code39(dcode);
       
    //     if(sym > QUEC_DECODER_PARTIAL)
    //     {
    //         //DEBUG("CODE39 sym = %d", sym);
    //         dcode->type = sym;
    //     }
           
    // }

    // #endif
    // #ifdef ENABLE_CODE128
    // if(TEST_CFG(dcode->code128.config, QUEC_DECODER_CFG_ENABLE))
    // {
    //     sym = _quec_decoder_decode_code128(dcode);
    //     if(sym > QUEC_DECODER_PARTIAL)
    //     {
    //         //DEBUG("CODE128 sym = %d", sym);
    //         dcode->type = sym;
    //     }
            
    // }
    // #endif
}

    
    dcode->idx++;
    if(dcode->type) {
        if(dcode->handler)
            dcode->handler(dcode);
        if(dcode->lock && dcode->type > QUEC_DECODER_PARTIAL)
            dcode->lock = 0;
    }
    //DEBUG("END sym = %d", sym);
    return(dcode->type);
}

static inline int decoder_set_config_bool (quec_decoder_decoder_t *dcode,
                                           quec_decoder_symbol_type_t sym,
                                           quec_decoder_config_t cfg,
                                           int val)
{
    unsigned *config = NULL;
    switch(sym) {
#ifdef ENABLE_EAN
    case QUEC_DECODER_EAN13:
        config = &dcode->ean.ean13_config;
        break;

    case QUEC_DECODER_EAN8:
        config = &dcode->ean.ean8_config;
        break;

    case QUEC_DECODER_UPCA:
        config = &dcode->ean.upca_config;
        break;

    case QUEC_DECODER_UPCE:
        config = &dcode->ean.upce_config;
        break;

    case QUEC_DECODER_ISBN10:
        config = &dcode->ean.isbn10_config;
        break;

    case QUEC_DECODER_ISBN13:
        config = &dcode->ean.isbn13_config;
        break;
#endif

#ifdef ENABLE_I25
    case QUEC_DECODER_I25:
        config = &dcode->i25.config;
        break;
#endif

#ifdef ENABLE_CODE39
    case QUEC_DECODER_CODE39:
        config = &dcode->code39.config;
        break;
#endif

#ifdef ENABLE_CODE128
    case QUEC_DECODER_CODE128:
        config = &dcode->code128.config;
        break;
#endif

#ifdef ENABLE_PDF417
    case QUEC_DECODER_PDF417:
        config = &dcode->pdf417.config;
        break;
#endif

#ifdef ENABLE_QRCODE
    case QUEC_DECODER_QRCODE:
        config = &dcode->qrf.config;
        break;
#endif

    /* FIXME handle addons */

    default:
        return(1);
    }
    if(!config || cfg >= QUEC_DECODER_CFG_NUM)
        return(1);

    if(!val)
        *config &= ~(1 << cfg);
    else if(val == 1)
        *config |= (1 << cfg);
    else
        return(1);

#ifdef ENABLE_EAN
    dcode->ean.enable = TEST_CFG(dcode->ean.ean13_config |
                                 dcode->ean.ean8_config |
                                 dcode->ean.upca_config |
                                 dcode->ean.upce_config |
                                 dcode->ean.isbn10_config |
                                 dcode->ean.isbn13_config,
                                 QUEC_DECODER_CFG_ENABLE);
#endif

    return(0);
}

static inline int decoder_set_config_int (quec_decoder_decoder_t *dcode,
                                          quec_decoder_symbol_type_t sym,
                                          quec_decoder_config_t cfg,
                                          int val)
{
    switch(sym) {

#ifdef ENABLE_I25
    case QUEC_DECODER_I25:
        CFG(dcode->i25, cfg) = val;
        break;
#endif
#ifdef ENABLE_CODE39
    case QUEC_DECODER_CODE39:
        CFG(dcode->code39, cfg) = val;
        break;
#endif
#ifdef ENABLE_CODE128
    case QUEC_DECODER_CODE128:
        CFG(dcode->code128, cfg) = val;
        break;
#endif
#ifdef ENABLE_PDF417
    case QUEC_DECODER_PDF417:
        CFG(dcode->pdf417, cfg) = val;
        break;
#endif

    default:
        return(1);
    }
    return(0);
}

int quec_decoder_decoder_set_config (quec_decoder_decoder_t *dcode,
                             quec_decoder_symbol_type_t sym,
                             quec_decoder_config_t cfg,
                             int val)
{
    if(sym == QUEC_DECODER_NONE) {
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_EAN13, cfg, val);
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_EAN8, cfg, val);
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_UPCA, cfg, val);
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_UPCE, cfg, val);
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_ISBN10, cfg, val);
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_ISBN13, cfg, val);
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_I25, cfg, val);
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_CODE39, cfg, val);
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_CODE128, cfg, val);
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_PDF417, cfg, val);
        quec_decoder_decoder_set_config(dcode, QUEC_DECODER_QRCODE, cfg, val);
        return(0);
    }

    if(cfg >= 0 && cfg < QUEC_DECODER_CFG_NUM)
        return(decoder_set_config_bool(dcode, sym, cfg, val));
    else if(cfg >= QUEC_DECODER_CFG_MIN_LEN && cfg <= QUEC_DECODER_CFG_MAX_LEN)
        return(decoder_set_config_int(dcode, sym, cfg, val));
    else
        return(1);
}


static char *decoder_dump = NULL;
static unsigned decoder_dumplen = 0;

const char *_quec_decoder_decoder_buf_dump (unsigned char *buf,
                                    unsigned int buflen)
{
    int dumplen = (buflen * 3) + 12;
    if(!decoder_dump || dumplen > decoder_dumplen) {
        if(decoder_dump)
            free(decoder_dump);
        decoder_dump = malloc(dumplen);
        decoder_dumplen = dumplen;
    }
    char *p = decoder_dump +
        snprintf(decoder_dump, 12, "buf[%04x]=",
                 (buflen > 0xffff) ? 0xffff : buflen);
    int i;
    for(i = 0; i < buflen; i++)
        p += snprintf(p, 4, "%s%02x", (i) ? " " : "",  buf[i]);
    return(decoder_dump);
}
