#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <assert.h>


#include "helios_os.h"
#include "helios_audio_fs.h"
#include "helios_audio_montage.h"
#include "helios_include.h"

#define MONTAGE_LEN 2048
#define MONTAGE_FLAGE_LEN 8
#define MONTAGE_MAX_NUMBER 16
#define MONTAGE_IDENTIFICATION "montage"
#define ARRAY_IS_USED 1
#define ARRAY_NOT_USED 0
static char montage_is_break = 0;

Helios_Mutex_t audio_montage_mutex = 0;

Helios_Thread_t montage_play_task_id = 0;
Helios_MsgQ_t montage_play_queue = 0;

static void *g_audio_callback;

static helios_cb_on_player play_user_cb = NULL;

int helios_audio_montage_callback(char *p_data, int len, Helios_EnumAudPlayerState state);



static int is_play = 0;
struct audio_file_list
{
    montage_elem *elem;
    char file_name[128];
    struct audio_file_list *next;
};

static montage_info all_montage[MONTAGE_MAX_NUMBER] = {0};

#define FREE_BUFF(X)  \
    do                \
    {                 \
        if (X)        \
        {             \
            free(X);  \
            X = NULL; \
        }             \
    } while (0);

#define CALLOC_BUFF_SET(X, Y)          \
    do                                 \
    {                                  \
        X = calloc(1, strlen(Y) + 16); \
        if(!X){							\
			AUDLOGE("malloc size[%d] fail\n", strlen(Y) + 16);	\
			assert(X != NULL);	\
		}	\
        memset(X, 0, strlen(Y) + 16);  \
        if (X)                         \
        {                              \
            strncpy(X, Y, strlen(Y));  \
        }                              \
    } while (0);

// Check whether it has been preprocessed
// 检查是否预处理
static char __check_is_preprocessed(char *montage_name)
{
    int i = 0;
    char ret = -1;

    for (i = 0; i < MONTAGE_MAX_NUMBER; i++)
    {
        if (all_montage[i].is_used == ARRAY_IS_USED && (0 == strncmp(all_montage[i].montage_name, montage_name, strlen(montage_name))))
        {
            ret = i;
            break;
        }
    }
    return ret;
}

// 获取空闲的预处理数组
static char __Get_idle_preprocessing_array()
{
    int i = 0;
    char ret = -1;

    for (i = 0; i < MONTAGE_MAX_NUMBER; i++)
    {
        if (all_montage[i].is_used == ARRAY_NOT_USED)
        {
            ret = i;
            break;
        }
    }
    return ret;
}

// 预处理过�?
static char __montage_file_preprocessing(char *montage_name)
{
    char montage_buf[MONTAGE_FLAGE_LEN + 1] = {0};
    char NumberOfFilesBuff[4] = {0};
    uint32_t Number_of_files = 0;
    montage_elem *file_information = NULL;
    montage_info *ret = NULL;
    int8_t idle_index = -1;
    int8_t montage_index = -1;

    montage_index = __check_is_preprocessed(montage_name);
    if (montage_index != -1)
    {
        AUDLOGE("%s has been pretreated\n", montage_name);
        return montage_index;
    }

    idle_index = __Get_idle_preprocessing_array();
    if (idle_index == -1)
    {
        AUDLOGE("No idle preprocessing array");
        return -1;
    }

    HeliosAudFILE *fp = Helios_Aud_fopen(montage_name, "r");
    if (NULL == fp)
    {
        AUDLOGE("montage(%s) open fail\n", montage_name);
        idle_index = -1;
        goto err;
    }

    // Check document identification
    if (MONTAGE_FLAGE_LEN != Helios_Aud_fread(&montage_buf, 1, MONTAGE_FLAGE_LEN, fp))
    {
        AUDLOGE("montage read identification fail\n");
        idle_index = -1;
        goto err;
    }

    if (0 != strncmp(montage_buf, MONTAGE_IDENTIFICATION, MONTAGE_FLAGE_LEN))
    {
        AUDLOGE("This file is not a splice file\n");
        idle_index = -1;
        goto err;
    }

    // Get number of files
    if (4 != Helios_Aud_fread(&NumberOfFilesBuff, 4, 1, fp))
    {
        AUDLOGE("Failed to read the number of files\n");
        idle_index = -1;
        goto err;
    }

    Number_of_files = *(uint32_t *)NumberOfFilesBuff;
    AUDLOGE("The number of documents is %d\n", Number_of_files);
    if (0 == Number_of_files)
    {
        idle_index = -1;
        goto err;
    }

    // Application buf save header
    file_information = calloc(Number_of_files, sizeof(montage_elem));
    if (NULL == file_information)
    {
        AUDLOGE("File information memory request failed\n");
        idle_index = -1;
        goto err;
    }

    int aa_debug = Helios_Aud_fread((void *)file_information, (Number_of_files * sizeof(montage_elem)), 1, fp);
    AUDLOGI("read size= %d, actual = %d\n", Number_of_files * sizeof(montage_elem), aa_debug);
    if ((int)(Number_of_files * sizeof(montage_elem)) != aa_debug)
    {
        AUDLOGE("Failed to read file information\n");
        if (file_information)
        {
            free(file_information);
            file_information = NULL;
        }
        return -1;
    }
    all_montage[idle_index].is_used = ARRAY_IS_USED;
    memcpy(&(all_montage[idle_index].montage_name), montage_name, strlen(montage_name));
    all_montage[idle_index].file_num = Number_of_files;
    all_montage[idle_index].file_info_buf = file_information;

err:
    if (fp)
    {
        Helios_Aud_fclose(fp);
        fp = NULL;
    }

    return idle_index;
}

// 获取拼接文件中的文件信息
static montage_elem *__getElemByName(char *montage_name, char *file_name)
{
    int8_t montage_index = -1;
    int8_t elem_index = -1;
    montage_elem *elem = NULL;
    char actual_name[128] = {0};

    montage_index = __montage_file_preprocessing(montage_name);
    if (-1 == montage_index)
    {
        return NULL;
    }
    AUDLOGI("__getElemByName montage index = %d\n", montage_index);
    elem = (montage_elem *)(all_montage[montage_index].file_info_buf);

    sprintf(actual_name, file_name + 3, strlen(file_name) - 3);

    uint32_t i = 0;
    for (i = 0; i < all_montage[montage_index].file_num; i++)
    {
        AUDLOGI("actual_name:%s, elem:%s, offset %d, size=%d\n", actual_name, elem->file_name, elem->offset, elem->size);
        if (0 == strncmp(file_name, elem->file_name, strlen(actual_name)+3))
        {
            AUDLOGI("elem addr : 0x%x\n", elem);
            return elem;
        }
        elem++;
    }

    return NULL;
}

static Helios_AudStreamFormat auStreamFormatBySuffix(const char *fname)
{
    if (fname == NULL)
        return HELIOS_AUDIO_FORMAT_UNKNOWN;

    char *dot = strrchr(fname, '.');
    if (dot == NULL)
        return HELIOS_AUDIO_FORMAT_AMRNB;

    if (strcasecmp(dot, ".pcm") == 0)
        return HELIOS_AUDIO_FORMAT_PCM;

    if (strcasecmp(dot, ".wav") == 0)
        return HELIOS_AUDIO_FORMAT_WAVPCM;

    if (strcasecmp(dot, ".mp3") == 0)
        return HELIOS_AUDIO_FORMAT_MP3;

    if (strcasecmp(dot, ".amr") == 0)
        return HELIOS_AUDIO_FORMAT_AMRNB;

    if (strcasecmp(dot, ".awb") == 0)
        return HELIOS_AUDIO_FORMAT_AMRWB;

    return HELIOS_AUDIO_FORMAT_UNKNOWN;
}

static void audio_file_list_push(struct audio_file_list *head, struct audio_file_list *item)
{
    struct audio_file_list *p = NULL;
    if (head == NULL)
    {
        return;
    }
    p = head;
    while (p->next != NULL)
    {
        p = p->next;
    }

    p->next = item;
}

static struct audio_file_list *audio_file_list_pop(struct audio_file_list *head)
{
    struct audio_file_list *p = NULL;
    if (head == NULL)
    {
        return NULL;
    }

    if (head->next)
    {
        p = head->next;
        head->next = p->next;
        return p;
    }
    else
    {
        return NULL;
    }
}

static void audio_file_list_destroy(struct audio_file_list *head)
{
    if (head == NULL)
    {
        return;
    }
    do
    {
        struct audio_file_list *item = audio_file_list_pop(head);
        if (item == NULL)
        {
            break;
        }
        FREE_BUFF(item);
    } while (head->next != NULL);
}

int helios_set_montage_break_flag(char set){
	if(set == 1 || set == 0)
	{
		montage_is_break = set;
		return 0;
	}
	return -1;
}


static int helios_audio_play_morefiles(montage_play_elem *play_elem)
{
    struct audio_file_list list_head = {.next = NULL};
    char *path_dir = NULL;
    char *p = NULL;
    int total_files_size = 0;
    int data_size = 0;
    char *data = NULL;
    char *files_tmp = NULL;
    char tmp[128] = {0};
    HeliosAudFILE *fp = NULL;
    int result_ret = PLAY_MOREFILES_ERR_OK;
    int file_cnt = 0;
    montage_elem *elem = NULL;
    void *elem_buf = NULL;
    uint32_t count = 0;
    int ret = 0;
    int format = 0;
    uint32_t cur_count = 0;
    montage_is_break = 0;

    uint32_t cur_count_offset = 0;
    uint32_t cur_count_size = 0;
	char is_wav_first = 1;

    AUDLOGI("files = %s\n", play_elem->file_name);

    if (audio_montage_mutex == 0)
    {
        audio_montage_mutex = Helios_Mutex_Create();
    }

    if (strncmp(play_elem->file_name, "mp3files=", 9) == 0)
    {
    }
    else if (strncmp(play_elem->file_name, "amrfiles=", 9) == 0)
    {
    }
    else if (strncmp(play_elem->file_name, "wavfiles=", 9) == 0)
    {
    //TODO 
    }
    else
    {
        /* not continue play */
        result_ret = PLAY_MOREFILES_ERR_SINGLEFILE;
        goto err2;
    }

    files_tmp = malloc(strlen(play_elem->file_name) + 1);
	if(!files_tmp) goto err2;
	
    memset(files_tmp, 0, strlen(play_elem->file_name) + 1);
    strcpy(files_tmp, play_elem->file_name);

    /* parse files info */
    p = strtok(files_tmp + 9, "+");
    AUDLOGI("path_dir = %s\n", p);
    if (p != NULL)
    {
        AUDLOGI("p = %s\n", p);
    }
    else
    {
        result_ret = PLAY_MOREFILES_ERR_FILENOTEXIST;
        AUDLOGE("p = %s\n", p);
        goto err2;
    }

    while (p != NULL)
    {
        struct audio_file_list *item = NULL;
        elem = __getElemByName(play_elem->montage_name, p);
        if (elem != NULL)
        {
            AUDLOGI("XXX elem:%s, offset %d, size=%d\n", elem->file_name, elem->offset, elem->size);
            item = calloc(1, sizeof(struct audio_file_list));
			if(!item) goto err2;
			
            sprintf(item->file_name, "%s", p);
            item->elem = elem;
            item->next = NULL;
            audio_file_list_push(&list_head, item);
            file_cnt++;
        }
        else
        {
            while (list_head.next != NULL)
            {
                struct audio_file_list *item = audio_file_list_pop(&list_head);
                if (item)
                {
                    free(item);
                }
            }
            result_ret = PLAY_MOREFILES_ERR_FILENOTEXIST;
            goto err2;
        }
        p = strtok(NULL, "+");
    }

    /* play files in once */
    // data = malloc(total_files_size);
    fp = Helios_Aud_fopen(play_elem->montage_name, "r");
    if (NULL == fp)
    {
        result_ret = PLAY_MOREFILES_ERR_FILENOTEXIST;
        goto err2;
    }
    elem_buf = (void *)calloc(1, MONTAGE_LEN);
	if(!elem_buf) {
		AUDLOGE("malloc size(%d) fail\n", MONTAGE_LEN);
		goto err2;
	}

    do
    {
        struct audio_file_list *item = audio_file_list_pop(&list_head);
        if (item == NULL)
        {
            break;
        }

        format = auStreamFormatBySuffix(item->elem->file_name);
        AUDLOGI("format = %d\n", format);

        if (item)
        {
            AUDLOGI("%s, offset %d, size=%d\n", item->elem->file_name, item->elem->offset, item->elem->size);

            if ((format == HELIOS_AUDIO_FORMAT_AMRNB) || (format == HELIOS_AUDIO_FORMAT_AMRWB))
            {
                cur_count_offset = item->elem->offset + 6;
                cur_count_size = item->elem->size - 6;
            }
            else if (format == HELIOS_AUDIO_FORMAT_MP3)
            {
                char mp3_head[10] = {0};
                int skip_size = 0;
                Helios_Aud_fseek(fp, item->elem->offset, SEEK_SET);
                Helios_Aud_fread(mp3_head, 1, 10, fp);
                if (mp3_head[0] == 73 && mp3_head[1] == 68 && mp3_head[2] == 51)
                {
                    skip_size = (mp3_head[6] & 0x7F) * 0x200000 + (mp3_head[7] & 0x7F) * 0x4000 + (mp3_head[8] & 0x7F) * 0x80 + (mp3_head[9] & 0x7F) + 10;
                    AUDLOGI("is mp3 file, skip size[%d]\n", skip_size);
                }
                cur_count_offset = item->elem->offset + skip_size;
                cur_count_size = item->elem->size - skip_size;
            }
            else if(format == HELIOS_AUDIO_FORMAT_WAVPCM){
				if(is_wav_first == 1) {
					is_wav_first = 0;
					helios_audio_montage_callback(NULL, 0, HELIOS_AUD_PLAYER_START);
					cur_count_offset = item->elem->offset;
					cur_count_size = item->elem->size;
				} else {
					cur_count_offset = item->elem->offset+44;
					cur_count_size = item->elem->size-44;
				}
			} else
            {
                cur_count_offset = item->elem->offset;
                cur_count_size = item->elem->size;
            }

            ret = Helios_Aud_fseek(fp, cur_count_offset, SEEK_SET);
            AUDLOGI("Helios_Aud_fseek = %d\n", ret);
            if ((uint32_t)ret != cur_count_offset)
            {
                result_ret = PLAY_MOREFILES_ERR_FILENFSFAIL;
                FREE_BUFF(item);
                goto err2;
            }

            count = 0;

            AUDLOGI("cur_count_size = %d\n", cur_count_size);
			
            while (count < cur_count_size)
            {
                if (count + MONTAGE_LEN < cur_count_size)
                {
                    cur_count = Helios_Aud_fread(elem_buf, 1, MONTAGE_LEN, fp);
                }
                else
                {
                    cur_count = Helios_Aud_fread(elem_buf, 1, cur_count_size - count, fp);
                }

                AUDLOGI("cur_count = %d\n", cur_count);
                if (cur_count <= 0)
                    break;
                count += cur_count;

                AUDLOGI("format = %d, cur_count = %d, type = %d, outputtype = %d, usr_cb = 0x%x\n", format, cur_count, play_elem->type, play_elem->outputtype, play_elem->usr_cb);
                Helios_Mutex_Lock(audio_montage_mutex, HELIOS_WAIT_FOREVER);
                ret = Helios_Audio_StreamPlayStart(format, elem_buf, cur_count, play_elem->type, play_elem->outputtype, play_elem->usr_cb);
                if (ret != 0)
                {
                    AUDLOGE("old play stream ret : %d\n", ret);
                    ret = Helios_Audio_StreamPlayStart(format, elem_buf, cur_count, play_elem->type, play_elem->outputtype, play_elem->usr_cb);
                    AUDLOGE("again play stream ret : %d\n", ret);
                }
                Helios_Mutex_Unlock(audio_montage_mutex);
				if(format == HELIOS_AUDIO_FORMAT_WAVPCM){
					Helios_msleep(5);
				} else {
					Helios_msleep(20);
				}
                
                if (montage_is_break || (0 != ret))
                {
                    AUDLOGI("montage_is_break : %d ret : %d\n", montage_is_break, ret);
                    if (montage_is_break == 0)
                        // audio_err_handle();

                        montage_is_break = 0;
                    FREE_BUFF(item);
                    goto err2;
                }
            }
            FREE_BUFF(item);
        }
    } while (list_head.next != NULL);

err2:
	if(format == HELIOS_AUDIO_FORMAT_WAVPCM) {
		Helios_msleep(250); // Replacing the bottom layer with 4K data takes approximately 250ms for WAV. //TODO Add PCM to complete playback interface
		helios_audio_montage_callback(NULL, 0, HELIOS_AUD_PLAYER_FINISHED);
	}

    if (fp)
    {
        Helios_Aud_fclose(fp);
        fp = NULL;
    }

    if (elem_buf)
    {
        free(elem_buf);
        elem_buf = NULL;
    }

    if (files_tmp)
    {
        free(files_tmp);
        files_tmp = NULL;
    }

    {
        audio_file_list_destroy(&list_head);
    }

    if (PLAY_MOREFILES_ERR_OK != result_ret)
    {
        AUDLOGI("result_ret : %d \n", result_ret);
        // audio_err_handle();
    }

    return result_ret;
}



static void Helios_Montage_play_task(void *argv)
{
    (void)argv;
    int ret = 0;
    montage_play_elem play_elem;

    while (1)
    {
        memset(&play_elem, 0, sizeof(montage_play_elem));

        ret = Helios_MsgQ_Get(montage_play_queue, (void *)&play_elem, sizeof(montage_play_elem), HELIOS_WAIT_FOREVER);
        if (ret != 0)
        {
            AUDLOGE("Splicing playback thread failed to get queue\n");
            continue;
        }
        if (0 != helios_audio_play_morefiles(&play_elem))
        {
            AUDLOGE("Failed to play montage audio\n");
        }
        FREE_BUFF(play_elem.montage_name);
        FREE_BUFF(play_elem.file_name);
    }
}

extern void helios_audio_pa_control(unsigned int onoff);

int helios_audio_montage_callback(char *p_data, int len, Helios_EnumAudPlayerState state)
 {
	 AUDLOGI("audio_play_callback:event = %d\n", state);
	 
	 unsigned int event = 0;

	 
	 switch (state)
	 {
		 case HELIOS_AUD_PLAYER_ERROR:
		 case HELIOS_AUD_PLAYER_PAUSE:
		 case HELIOS_AUD_PLAYER_FINISHED:
			 break;
		 case HELIOS_AUD_PLAYER_RESUME:
		 case HELIOS_AUD_PLAYER_START:
			 event = 1;
			 break;
		 default:
			 return -1;
	 }
 	
	 helios_audio_pa_control(event);
	 
	 if(play_user_cb != NULL) {
		 play_user_cb("File", len, state);
	 }
	 
	 return 0;
 }

int Helios_Audio_MontageFilePlayStart(char *montage_name, char *file_name, Helios_AudPlayerType type, helios_cb_on_player usr_cb, uint8_t device)
{

    Helios_ThreadAttr attr = {0};
    montage_play_elem play_elem;
    if (0 == montage_play_queue)
    {
        montage_play_queue = Helios_MsgQ_Create(2, sizeof(montage_play_elem));
        if (!montage_play_queue)
        {
            AUDLOGE("create montage queue failed, can not create msgQ.");
            goto err1;
        }
    }

    if (0 == montage_play_task_id)
    {
        attr.name = "audio_montage_play";
        attr.stack_size = 1024*2;
#if defined(PLAT_EIGEN) || defined(PLAT_EIGEN_718)
	attr.priority = 90;
#else
	attr.priority = 74;
#endif
        attr.entry = Helios_Montage_play_task;

        montage_play_task_id = Helios_Thread_Create(&attr);
        if (0 == montage_play_task_id)
        {
            AUDLOGE("create montage task failed\n");
            goto err1;
        }
    }
    memset(&play_elem, 0, sizeof(montage_play_elem));

    CALLOC_BUFF_SET(play_elem.montage_name, montage_name);
    CALLOC_BUFF_SET(play_elem.file_name, file_name);

    play_elem.outputtype = device;
    play_elem.type = type;
    play_elem.usr_cb = helios_audio_montage_callback;

	play_user_cb = usr_cb;

    if (montage_play_queue)
    {
        return Helios_MsgQ_Put(montage_play_queue, (const void *)&play_elem, sizeof(montage_play_elem), HELIOS_NO_WAIT);
    }

    return 0;
err1:
    if (montage_play_queue)
    {
        Helios_MsgQ_Delete(montage_play_queue);
        montage_play_queue = 0;
    }

    if (montage_play_task_id)
    {
        Helios_Thread_Delete(montage_play_task_id);
        montage_play_task_id = 0;
    }
    return -1;
}


