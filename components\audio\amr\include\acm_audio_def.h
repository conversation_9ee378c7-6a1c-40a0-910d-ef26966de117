/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2020 ASR Ltd. All Rights Reserved
-------------------------------------------------------------------------------------------------------------------*/

#pragma once
#ifdef __cplusplus
extern "C" {
#endif

    /** audio playback property in bitmap format, set bitfield and get the option field in int64 */
    typedef struct AUDIO_PLAY_OPTION {
        union {
            long long option;
            struct {
                /** bit0~bit1, dest play end,0->near,1->far,2->both */
                unsigned int dest_end : 2;
                /** bit2, override voice if any,0->false,1->true */
                unsigned int override : 1;
                /** bit3, near codec or vocoder,0->codec,1->vocoder */
                unsigned int active_point : 1;
                /** bit4, reserved */
                unsigned int : 1;
                /** bit5, play current file in cyclic mode,0->false,1->true */
                unsigned int cyclic : 1;
                /** bit6~bit7, mixer mode,0->combine,1->exclusive,2->preempt */
                unsigned int mode : 2;
                /** bit8~bit11, eq effect index */
                unsigned int eq : 4;
                /** bit12~bit20, speed factor in Q8, 0 for normal*/
                unsigned int speed : 9;
                /** bit21~bit23, write mode, 0 for asynchronous*/
                unsigned int write_mode : 3;
                /** bit24~bit27, pcm format*/
                unsigned int fmt : 4;
            };
        };
    }AUDIO_PLAY_OPTION;

    /** audio record property in bitmap format, set bitfield and get the option field in int64 */
    typedef struct AUDIO_RECORD_OPTION {
        union {
            long long option;
            struct {
                /** bit0~bit3, record mode */
                unsigned int mode : 4;
            };
        };
    }AUDIO_RECORD_OPTION;

#define AUDIO_PLAY_MASK_DEST_END        (3 << 0)
#define AUDIO_PLAY_MASK_OVERRIDE        (1 << 2)
#define AUDIO_PLAY_MASK_ACTIVE_POINT    (1 << 3)
#define AUDIO_PLAY_MASK_DRAIN           (1 << 4)
#define AUDIO_PLAY_MASK_CYCLIC          (1 << 5)
#define AUDIO_PLAY_MASK_MODE            (3 << 6)
#define AUDIO_PLAY_MASK_EQ              (0xf << 8)
#define AUDIO_PLAY_MASK_SPEED           (0x1ff << 12)

#define AUDIO_RECORD_MASK_MODE          (0xf << 0)

#define AUDIO_SERVICE_REVISION          0x01040000
#ifdef __cplusplus
}
#endif
