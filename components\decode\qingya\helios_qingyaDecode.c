#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "ql_api_decoder.h"

#if 1
#include "helios_debug.h"


#define QL_API_QR_CODE_LOG(msg, ...)      custom_log("decode_qingya", msg, ##__VA_ARGS__)

static int decoder_status_qing = DECODER_INITIAL_FAILED;
/*========================================================================

	FUNCTION:	ql_qr_decoder_init	
		
=========================================================================*/
/** @brief
		The function is used to QR decoder initial.
	
	@param[in][out]
		None 	N/A
	
	@return
		DECODER_INITIAL_FAILED -- Operate the QR decoder initial was unsuccessful.
		DECODER_INITIAL -- Operate the QR decoder initial was successful.	
*/
/*=======================================================================*/

ql_errcode_decoder_e ql_qr_decoder_init_qingya (void)
{
    // if(ql_dev_check_property())
    // {
    //     QL_API_QR_CODE_LOG("init failed");
    //     return QL_DECODER_INIT_ERR;
    // }
   
	if (DECODER_INITIAL != decoder_status_qing)
		decoder_status_qing = Initial_Decoder();
	else    
	{
        //QL_API_QR_CODE_LOG("has initialized");
        return QL_DECODER_INIT_ERR;
	}
	if (DECODER_INITIAL_FAILED == decoder_status_qing)
	{
		QL_API_QR_CODE_LOG("init failed");
        return QL_DECODER_INIT_ERR;
	}

	return QL_DECODER_SUCCESS;
}

/*========================================================================

	FUNCTION:	ql_qr_get_decoder_result	

=========================================================================*/
/** @brief
		The function is used to get the information that decode QR image .
	
	@param
		*type		[out]	the tpye of the code
        *result     [out]   the result of the code			 		
	
	@return
		DECODER_ERROR -- get the information of QR was unsuccessful.
		DECODER_ERROR_NONE -- get the information of QR was successful.	
*/
/*=======================================================================*/
ql_errcode_decoder_e ql_qr_get_decoder_result_qingya (ql_decoder_type_e* type, unsigned char* result)
{
	int iret = DECODER_ERROR;

    iret = GetDecoderResult(result);
	if (DECODER_SUCCESS == iret)
	{ 
        result[GetResultLength()] = '\0';
        *type = GetResultType();
	    return QL_DECODER_SUCCESS;
	}
    
    *type = QL_DECODER_TYPE_NONE;
    QL_API_QR_CODE_LOG("Get failed");
	return QL_DECODER_GET_RESULT_ERR;	
}

ql_errcode_decoder_e ql_destroy_decoder_qingya (void)
{
    if(decoder_status_qing == DECODER_INITIAL_FAILED)
    {
        QL_API_QR_CODE_LOG("destroy failed");
        return QL_DECODER_DESTROY_ERR;
    }
    
    Destroy_Decoder();
    decoder_status_qing = DECODER_INITIAL_FAILED;

	return QL_DECODER_SUCCESS;
}

ql_errcode_decoder_e ql_get_decoder_version_qingya(unsigned char* version)
{
    Get_Decoder_Version(version);

	return QL_DECODER_SUCCESS;
}
#endif