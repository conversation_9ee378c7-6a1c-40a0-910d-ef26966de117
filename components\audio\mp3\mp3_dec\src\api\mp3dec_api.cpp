/*
 * Copyright (C) 2014 The Android Open Source Project
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *  * Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *  * Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

#include <stdlib.h>
#include <stdio.h>
#include <assert.h>
#include <string.h>

#include "pvmp3decoder_api.h"
#include "mp3reader.h"
#include "helios_include.h"
//#include "AudioHAL.h"
#include "helios_audio_fs.h"

//#include "ql_audio.h"
//#define QUECTEL_MP3_RAM_FILE_DISABLE	//augsut add Unlock the macro to block the function(~83K ZI size)
#ifndef QUECTEL_MP3_RAM_FILE_DISABLE
#if 1//def QUECTEL_PROJECT_CUST
extern "C" {
//#include "ql_fs.h"
}
#endif
using namespace std;

enum {
    kInputBufferSize = 10 * 1024,
    kOutputBufferSize = 4608 * 2,
};
#if 1//def QUECTEL_PROJECT_CUST
enum {
    STREAM_MODE = 1,
    FILE_MODE ,
};
#endif
extern "C"
{







/*carola, 2020-01-04:add for quectel MP3 decode*/
static int dec_flag = 1;
static void *quec_decoder_buf = NULL;
int ql_decode_start(unsigned char *file_inputbuf, unsigned short *pcm_outputbuf, unsigned int bytes_read)
{
	int ret = 0;
	tPVMP3DecoderExternal config;
	unsigned int memRequirements = 0;
	char *file_inputbuf_rev = (char*)file_inputbuf;

	config.equalizerType = flat;
    config.crcEnabled = false;

	if(dec_flag)
	{	
		// Initialize the decoder.
		memRequirements = pvmp3_decoderMemRequirements();

		quec_decoder_buf = malloc(memRequirements);
		if(quec_decoder_buf == NULL) 
		{
			AUDLOGE("malloc size[%d] fail\n", memRequirements);
			return -1;
		}

		pvmp3_InitDecoder(&config, quec_decoder_buf);

		dec_flag = 0;
	}
	
	// Set the input config.
    config.inputBufferCurrentLength = bytes_read;
    config.inputBufferMaxLength = 0;
    config.inputBufferUsedLength = 0;
    config.pInputBuffer = (uint8*)file_inputbuf_rev;
    config.pOutputBuffer = (int16*)pcm_outputbuf;
    config.outputFrameSize = 4608*2 / sizeof(unsigned short);

    ret = pvmp3_framedecoder(&config, quec_decoder_buf);
    if (ret != 0) 
	{
        //DIAG_FILTER(AUDIO, MP3_DEC, framedecoder, DIAG_INFORMATION)
        AUDLOGE("pvmp3_framedecoder ret = %d", ret);
        return -1;
    }
	
	return ( config.outputFrameSize*sizeof(int16_t) );
}

void ql_release(void)
{
	if(quec_decoder_buf)
		free(quec_decoder_buf);

	if (dec_flag == 0) {
		dec_flag = 1;
	}
}
/*end add*/

} // end of extern C
#else

extern "C"
{
typedef void(*MP3_EVENT_CB)(int);
int mp3Start(const char *file_name, MP3_EVENT_CB cb){
	return -1;
}

int mp3Start2(const char *file_name, MP3_EVENT_CB cb, uint32_t offset){
	return -1;
}

int mp3Start3(const char *data, MP3_EVENT_CB cb, uint32_t size) {
	return -1;
}

int mp3Stop(void){
	return -1;
}

int ql_decode_start(unsigned char *file_inputbuf, unsigned short *pcm_outputbuf, unsigned int bytes_read){
	return -1;
}

void ql_release(void){
}
}// end of extern C
#endif//END OF QUECTEL_MP3_RAM_FILE
