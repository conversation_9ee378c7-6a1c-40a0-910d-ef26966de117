
#include "quec_decoder_config.h"
#include <string.h>     /* memmove */

#include <quec_decoder.h>
#include "decoder.h"
#ifdef ENABLE_I25
#ifdef DEBUG_I25
# define DEBUG_LEVEL (DEBUG_I25)
#endif
//#include "debug.h"
#include "i25.h"

static inline unsigned char i25_decode1 (unsigned char enc,
                                         unsigned e,
                                         unsigned s)
{
    unsigned char E = decode_e(e, s, 45);
    if(E > 7)
        return(0xff);
    enc <<= 1;
    if(E > 2)
        enc |= 1;
    return(enc);
}

static inline unsigned char i25_decode10 (quec_decoder_decoder_t *dcode,
                                          unsigned char offset)
{
    i25_decoder_t *dcode25 = &dcode->i25;
    //dprintf(2, " s=%d", dcode25->s10);
    if(dcode25->s10 < 10)
        return(0xff);

    /* threshold bar width ratios */
    unsigned char enc = 0, par = 0;
    signed char i;
    for(i = 8; i >= 0; i -= 2) {
        unsigned char j = offset + ((dcode25->direction) ? i : 8 - i);
        enc = i25_decode1(enc, get_width(dcode, j), dcode25->s10);
        if(enc == 0xff)
            return(0xff);
        if(enc & 1)
            par++;
    }

    //dprintf(2, " enc=%02x par=%x", enc, par);

    /* parity check */
    if(par != 2) {
        //dprintf(2, " [bad parity]");
        return(0xff);
    }

    /* decode binary weights */
    enc &= 0xf;
    if(enc & 8) {
        if(enc == 12)
            enc = 0;
        else if(--enc > 9) {
            //dprintf(2, " [invalid encoding]");
            return(0xff);
        }
    }

    //dprintf(2, " => %x", enc);
    return(enc);
}

static inline signed char i25_decode_start (quec_decoder_decoder_t *dcode)
{
    i25_decoder_t *dcode25 = &dcode->i25;
    if(dcode25->s10 < 10)
        return(QUEC_DECODER_NONE);

    unsigned char enc = 0;
    unsigned char i = 10;
    enc = i25_decode1(enc, get_width(dcode, i++), dcode25->s10);
    enc = i25_decode1(enc, get_width(dcode, i++), dcode25->s10);
    enc = i25_decode1(enc, get_width(dcode, i++), dcode25->s10);

    if((get_color(dcode) == QUEC_DECODER_BAR)
       ? enc != 4
       : (enc = i25_decode1(enc, get_width(dcode, i++), dcode25->s10))) {
        //dprintf(4, "      i25: s=%d enc=%x [invalid]\n", dcode25->s10, enc);
        return(QUEC_DECODER_NONE);
    }

    /* check leading quiet zone - spec is 10n(?)
     * we require 5.25n for w=2n to 6.75n for w=3n
     * (FIXME should really factor in w:n ratio)
     */
    unsigned quiet = get_width(dcode, i++);
    if(quiet && quiet < dcode25->s10 * 3 / 8) {
        //dprintf(3, "      i25: s=%d enc=%x q=%d [invalid qz]\n",
                //dcode25->s10, enc, quiet);
        return(QUEC_DECODER_NONE);
    }

    dcode25->direction = get_color(dcode);
    dcode25->element = 1;
    dcode25->character = 0;
    return(QUEC_DECODER_PARTIAL);
}

static inline signed char i25_decode_end (quec_decoder_decoder_t *dcode)
{
    i25_decoder_t *dcode25 = &dcode->i25;

    /* check trailing quiet zone */
    unsigned quiet = get_width(dcode, 0);
    if((quiet && quiet < dcode25->width * 3 / 8) ||
       decode_e(get_width(dcode, 1), dcode25->width, 45) > 2 ||
       decode_e(get_width(dcode, 2), dcode25->width, 45) > 2) {
        //dprintf(3, " s=%d q=%d [invalid qz]\n", dcode25->width, quiet);
        return(QUEC_DECODER_NONE);
    }

    /* check exit condition */
    unsigned char E = decode_e(get_width(dcode, 3), dcode25->width, 45);
    if((!dcode25->direction)
       ? E - 3 > 4
       : (E > 2 ||
          decode_e(get_width(dcode, 4), dcode25->width, 45) > 2))
        return(QUEC_DECODER_NONE);

    if(dcode25->direction) {
        /* reverse buffer */
        //dprintf(2, " (rev)");
        int i;
        for(i = 0; i < dcode25->character / 2; i++) {
            unsigned j = dcode25->character - 1 - i;
            char c = dcode->buf[i];
            dcode->buf[i] = dcode->buf[j];
            dcode->buf[j] = c;
        }
    }

    if(dcode25->character < CFG(*dcode25, QUEC_DECODER_CFG_MIN_LEN) ||
       (CFG(*dcode25, QUEC_DECODER_CFG_MAX_LEN) > 0 &&
        dcode25->character > CFG(*dcode25, QUEC_DECODER_CFG_MAX_LEN))) {
        //dprintf(2, " [invalid len]\n");
        dcode->lock = 0;
        dcode25->character = -1;
        return(QUEC_DECODER_NONE);
    }

    dcode->buflen = dcode25->character;
    dcode->buf[dcode25->character] = '\0';
    //dprintf(2, " [valid end]\n");
    dcode25->character = -1;
    return(QUEC_DECODER_I25);
}

quec_decoder_symbol_type_t _quec_decoder_decode_i25 (quec_decoder_decoder_t *dcode)
{
    i25_decoder_t *dcode25 = &dcode->i25;

    /* update latest character width */
    dcode25->s10 -= get_width(dcode, 10);
    dcode25->s10 += get_width(dcode, 0);

    if(dcode25->character < 0 &&
       !i25_decode_start(dcode))
        return(QUEC_DECODER_NONE);

    if(--dcode25->element == 6 - dcode25->direction)
        return(i25_decode_end(dcode));
    else if(dcode25->element)
        return(QUEC_DECODER_NONE);

    /* FIXME check current character width against previous */
    dcode25->width = dcode25->s10;

    //dprintf(2, "      i25[%c%02d+%x]",
            //(dcode25->direction) ? '<' : '>',
            //dcode25->character, dcode25->element);

    /* lock shared resources */
    if(!dcode25->character && get_lock(dcode, QUEC_DECODER_I25)) {
        dcode25->character = -1;
        //dprintf(2, " [locked %d]\n", dcode->lock);
        return(QUEC_DECODER_PARTIAL);
    }

    unsigned char c = i25_decode10(dcode, 1);
    //dprintf(2, " c=%x", c);

    if(c > 9 ||
       ((dcode25->character >= BUFFER_MIN) &&
        size_buf(dcode, dcode25->character + 2))) {
        //dprintf(2, (c > 9) ? " [aborted]\n" : " [overflow]\n");
        dcode->lock = 0;
        dcode25->character = -1;
        return(QUEC_DECODER_NONE);
    }
    dcode->buf[dcode25->character++] = c + '0';

    c = i25_decode10(dcode, 0);
    //dprintf(2, " c=%x", c);
    if(c > 9) {
        //dprintf(2, " [aborted]\n");
        dcode->lock = 0;
        dcode25->character = -1;
        return(QUEC_DECODER_NONE);
    }
    //else
        //dprintf(2, "\n");

    dcode->buf[dcode25->character++] = c + '0';
    dcode25->element = 10;
    return((dcode25->character == 2) ? QUEC_DECODER_PARTIAL : QUEC_DECODER_NONE);
}
#endif
