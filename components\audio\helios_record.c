#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>

#include "helios_record.h"
#include "helios_os.h"
#include "helios_audio_fs.h"
#include "helios_include.h"

#if CONFIG_AUDIO_RECORD_MP3
#include "layer3.h"
#endif

#if CONFIG_AUDIO_RECORD_AMRNB
#include "amrnb_enc_api.h"
#endif

#define ID_RIFF 	0x46464952
#define ID_WAVE		0x45564157
#define ID_FMT  	0x20746d66
#define ID_DATA 	0x61746164
#define ID_IOS		0x524c4c46//wav data id by IOS
#define FORMAT_PCM 	1

#define SEEK_SET 0
#define SEEK_CUR 1

#define HELIOS_PCM_BLOCK_FLAG (0x01)
#define HELIOS_PCM_NONBLOCK_FLAG (0x02)
#define HELIOS_PCM_READ_FLAG (0x04)
#define HELIOS_PCM_WRITE_FLAG (0x08)

#if CONFIG_AUDIO_RECORD_MP3
Helios_Mutex_t _mp3_mutex = 0;
#endif

typedef struct helios_aud_record_struct
{
	Helios_MsgQ_t msg;
	Helios_Thread_t task;
	bool init_status;
	PCM_HANDLE_T read_hd;
	RECORD_STATUS status;
}Helios_Aud_Record_S;


/**
 ******************************************************************************
 * @ audio record para    
 ******************************************************************************
 */

Helios_Aud_Record_S _aud_record = {0};

static int read_file_start = 1;

#define AUD_RECORD_BUF_LEN 960  //9600 short,20ms @8KHz mono nend two frame

#if CONFIG_AUDIO_RECORD_AMRNB
//add encode data api
amrnb_enc_config enc_config = { 0 };
amrnb_enc_handle enc_handle = 0;
static int amr_record_dtx_enable = 0;
#endif


typedef struct Helios_ring_buf_struct
{
	int capacity;
	int rpos;
	int wpos;
	Helios_Sem_t wcond;
	Helios_Sem_t rcond;
	unsigned char data[0];
} Helios_ring_buf_t;

static Helios_ring_buf_t *Helios_ring_buf = NULL;



#if CONFIG_AUDIO_RECORD_MP3
static int Helios_rb_init(Helios_ring_buf_t *rb)
{
	if (NULL == rb)
	{
		AUDLOGE("rb is NULL\n");
		return -1;
	}

	// rb->capacity = 1024;
	rb->rpos = 0;
	rb->wpos = 0;

	return 0;
}

static int Helios_rb_free_space(Helios_ring_buf_t *rb)
{
	int cur_data_len = 0;

	cur_data_len = (rb->wpos + rb->capacity + 1 - rb->rpos) % (rb->capacity + 1);

	return (rb->capacity - cur_data_len);
}

static int Helios_rb_data_space(Helios_ring_buf_t *rb)
{
	int cur_data_len = 0;

	cur_data_len = (rb->wpos + rb->capacity + 1 - rb->rpos) % (rb->capacity + 1);

	return cur_data_len;
}

static int Helios_rb_write(Helios_ring_buf_t *rb, unsigned char *data, int len)
{
	int i = 0;
	int cur_free_len = 0, write_len = 0;

	if (NULL == rb || NULL == data || 0 == len)
	{
		return -1;
	}

	cur_free_len = Helios_rb_free_space(rb);
	if (0 == cur_free_len)
	{
		return 0;
	}

	if (cur_free_len < len)
	{
		write_len = cur_free_len;
	}
	else
	{
		write_len = len;
	}

	for (i = 0; i < write_len; i++)
	{
		rb->data[rb->wpos] = data[i];
		rb->wpos = (rb->wpos + 1) % (rb->capacity + 1);
	}

	return write_len;
}

static int Helios_rb_read(Helios_ring_buf_t *rb, unsigned char *data, int len)
{
	int i = 0;
	int cur_data_len = 0, read_len = 0;

	if (NULL == rb || NULL == data || 0 == len)
	{
		return -1;
	}

	cur_data_len = Helios_rb_data_space(rb);
	if (0 == cur_data_len)
	{
		return 0;
	}

	if (cur_data_len < len)
	{
		read_len = cur_data_len;
	}
	else
	{
		read_len = len;
	}

	for (i = 0; i < read_len; i++)
	{
		data[i] = rb->data[rb->rpos];
		rb->rpos = (rb->rpos + 1) % (rb->capacity + 1);
	}

	return read_len;
}

#endif



/**
 ******************************************************************************
 * @ audio record API    
 ******************************************************************************
 */
static void _set_wavfile_size(HeliosAudFILE *FileID,unsigned int size)//20210129 august add set wav data size
{
	if(FileID){
		Helios_Aud_fseek(FileID, sizeof(struct wav_header)-4,0);
		Helios_Aud_fwrite(&size, 1, sizeof(int), FileID);
	}
}

static int _wav_init_header(HeliosAudFILE *fd, int ch, int rate) //format set only SE16bit here
{
    int ret;
    struct wav_header hdr;

    //set default wav header
    hdr.riff_id = ID_RIFF;              //chunk id String "RIFF"
    hdr.riff_fmt = ID_WAVE;             //format, String "WAVE" here
    hdr.fmt_id = ID_FMT;                //sub format, "fmt" or "data", "fmt" heare
    hdr.fmt_sz = 16;                    //format data length
    hdr.audio_format = FORMAT_PCM;      //audio format
    hdr.num_channels = ch;              //channels
    hdr.sample_rate = rate;             //sample rate
    hdr.bits_per_sample = 16;           //bit per sample
    hdr.byte_rate = (rate * ch * hdr.bits_per_sample) / 8; //byte rate
    hdr.block_align = ( hdr.bits_per_sample * ch ) / 8;    //block align
    hdr.data_id = ID_DATA;              //data sub chunk
    hdr.data_sz = 0;                    //data size, set 0 here
    hdr.riff_sz = hdr.data_sz + 44 - 8; //size with header, but no ChunkID and ChunkSize

    ret = Helios_Aud_fseek(fd, 0, SEEK_SET);
    ret = Helios_Aud_fwrite(&hdr, sizeof(struct wav_header), 1, fd);
    if (ret != sizeof(struct wav_header))
    {
        AUDLOGE("Write wav header failed\n");
        return -1;
    }

	AUDLOGD("hdr.num_channels = %d, hdr.sample_rate = %d\n", hdr.num_channels, hdr.sample_rate);
    return sizeof(struct wav_header);
}

static int _amr_init_header(HeliosAudFILE *fd) //format set only SE16bit here
{
    int ret = Helios_Aud_fseek(fd, 0, SEEK_SET);
    ret = Helios_Aud_fwrite("#!AMR\n", 6, 1, fd);
    if (ret != 6)
    {
        AUDLOGE("Write amr header failed\n");
        return -1;
    }
    return 6;
}


static int _mp3_init_header(HeliosAudFILE *fd) //format set only SE16bit here
{
    int ret = Helios_Aud_fseek(fd, 0, SEEK_SET);
	const char id3_header[10] = {'I', 'D', '3', 3, 0, 0, 0, 0, 0, 0};
	
    ret = Helios_Aud_fwrite((void*)id3_header, sizeof(id3_header), 1, fd);
    if (ret != sizeof(id3_header))
    {
        AUDLOGE("Write mp3 header failed\n");
        return -1;
    }
    return sizeof(id3_header);
}



static int _file_init_header(RECORD_FROMAT format, HeliosAudFILE *fd, int ch, int rate) {
	if(format == HELIOS_RECORD_WAV_FILE) {
		return _wav_init_header(fd, ch, rate);
	} else if(format == HELIOS_RECORD_AMRNB_FILE) {
		return _amr_init_header(fd);
	} else if(format == HELIOS_RECORD_MP3_FILE) {
		return _mp3_init_header(fd);
	}
	return 0;
}


#if CONFIG_AUDIO_RECORD_AMRNB
static int _amrencode_data_init(unsigned int mode, unsigned int format)
{
	int ret = 0;
	if(enc_handle){
		AUDLOGE("amrnb encode is opening !\n");
		return -1;
	}
	enc_config.mode = mode;
	enc_config.output_format = format;
	enc_config.dtx_mode = amr_record_dtx_enable;
	ret = amrnb_encode_open(&enc_config, &enc_handle);
	if(ret < 0){
		AUDLOGE("amrnb encode open failed !\n");
		return -1;
	}
	return 0;
}

static int _amrencode_data(unsigned char *input_buff, unsigned char *output_buff)
{
	int ret = 0, output_size = 0;
	
	if(enc_handle == 0){
		return -1;
	}
	ret = amrnb_encode_set(enc_handle, input_buff, 320);
	if(ret < 0){
		AUDLOGE("amrnb encode set error !\n");
		return -2;
	}
	ret = amrnb_encode_do(enc_handle);
	if (ret != 0){
		AUDLOGE("amrnb encode error \n");
		return -3;
	}
	ret = amrnb_encode_get(enc_handle, output_buff, &output_size);
	if(ret < 0){
		AUDLOGE("amrnb encode get error !\n");
		return -4;
	}

	return output_size;
}

static int _amrencode_data_deinit(void)
{
	int ret = 0;
	if(!enc_handle){
		AUDLOGE("amrnb encode is not open !\n");
		return -1;
	}
	ret = amrnb_encode_close(enc_handle);
	if(ret < 0){
		AUDLOGE("amrnb encode close failed !\n");
		return -1;
	}
	enc_handle = false;
	return 0;
}
#endif

#if CONFIG_AUDIO_RECORD_MP3

#define SAMPLES_PER_FRAME 1152  // Shine����ÿ֡��PCM������
#define PCM_INPUT_SIZE 320  // ����PCM������
#define CHANNELS 1  // ������������
#define MP3_SIZE (SAMPLES_PER_FRAME * 2)  // MP3�����������С


static shine_t shine_h = NULL;

/* Print some info about what we're going to encode */
static void check_config(shine_config_t *config) {
  static char *version_names[4] = {"2.5", "reserved", "II", "I"};
  static char *mode_names[4] = {"stereo", "joint-stereo", "dual-channel",
                                "mono"};
  static char *demp_names[4] = {"none", "50/15us", "", "CITT"};

  AUDLOGD("MPEG-%s layer III, %s  Psychoacoustic Model: Shine\n",
         version_names[shine_check_config(config->wave.samplerate,
                                          config->mpeg.bitr)],
         mode_names[config->mpeg.mode]);
  AUDLOGD("Bitrate: %d kbps  ", config->mpeg.bitr);
  AUDLOGD("De-emphasis: %s   %s %s\n", demp_names[config->mpeg.emph],
         ((config->mpeg.original) ? "Original" : ""),
         ((config->mpeg.copyright) ? "(C)" : ""));
//  AUDLOGD("Encoding \"%s\" to \"%s\"\n", infname, outfname);
}

int samples_per_pass = 0;
static int mpeg_bitr = 64;



static int _mp3encode_data_init(enum channels channle_n, int samplerate)
{
    // ��ʼ��Shine����������
	if(shine_h == NULL) {
		shine_config_t config;
		shine_set_config_mpeg_defaults(&config.mpeg);
		config.wave.channels = channle_n;
		config.wave.samplerate = samplerate;
		config.mpeg.bitr = mpeg_bitr;

		
		/* See if samplerate and bitrate are valid */
		if (shine_check_config(config.wave.samplerate, config.mpeg.bitr) < 0) {
			AUDLOGE("Unsupported samplerate/bitrate configuration.");
			return -1;
		}

		
		/* Set to stereo mode if wave data is stereo, mono otherwise. */
		if (config.wave.channels > 1)
		config.mpeg.mode = STEREO;
		else
		config.mpeg.mode = MONO;
		
		// ��ʼ��������
		shine_h = shine_initialise(&config);
		assert(shine_h != NULL);

		check_config(&config);

		samples_per_pass = shine_samples_per_pass(shine_h);
		AUDLOGD("samples_per_pass = %d\n",samples_per_pass);
	}
	
	return 0;
		
}

static int _mp3encode_data(unsigned char *input_buff, int input_len, unsigned char *output_buff, bool is_last_frame)
{
	static int16_t pcm_frame_buffer[SAMPLES_PER_FRAME * 2] = {0};
	int16_t buffer[2 * SHINE_MAX_SAMPLES];
	int mp3_bytes = 0;
	unsigned char *mp3_buffer = NULL;
	
	Helios_Mutex_Lock(_mp3_mutex, HELIOS_WAIT_FOREVER);
	int tmp_len = Helios_rb_data_space(Helios_ring_buf);
	Helios_Mutex_Unlock(_mp3_mutex);
	
	AUDLOGD("rb free size[%d]\n", tmp_len);

	if(tmp_len >= samples_per_pass * CHANNELS * 2) {
		
		Helios_Mutex_Lock(_mp3_mutex, HELIOS_WAIT_FOREVER);
		tmp_len = Helios_rb_read(Helios_ring_buf, buffer, samples_per_pass*2);
		Helios_Mutex_Unlock(_mp3_mutex);
		AUDLOGD("rb free size[%d]\n", tmp_len);
		if(tmp_len != samples_per_pass*2) {
			return 0;
		}
		mp3_buffer = shine_encode_buffer_interleaved(shine_h, buffer, &mp3_bytes);
		if(mp3_buffer == NULL) {
			AUDLOGE("interleaved fail\n");
		}
		AUDLOGD("mp3_bytes = %d\n",mp3_bytes);
		memcpy(output_buff, mp3_buffer, mp3_bytes);
	}
	
	if(is_last_frame == true && tmp_len > 0) {
        mp3_buffer = shine_flush(shine_h, &mp3_bytes);
		memcpy(output_buff, mp3_buffer, mp3_bytes);
	}
	
	return mp3_bytes;
}





static int _mp3encode_data_deinit(void)
{
    // ������������Դ
    if(shine_h) {
		shine_close(shine_h);
		shine_h = NULL;
	}
	return 0;
}

void Helios_mp3Record_set_bitr(int val)
{
	mpeg_bitr = val;
	_mp3encode_data_deinit();
	_mp3encode_data_init(1, 22050); // temp TODO

}

#endif


static int _audio_record_task_thread(void*argv)
{
	int ret = 0, i = 0;
	int read_ret = -1;
	int read_count = 0;
	//QL_PCM_CONFIG_T pcm_config = {1, 8000, 0};
	_Aud_Record_CONFIG recvMsg;
	unsigned int data_size = 0;
	unsigned char first_entry = 0;
	HeliosAudFILE *FileID = NULL;
//	int fs_free_size_count = Helios_fs_free_size('U');
	unsigned short record_buf[AUD_RECORD_BUF_LEN] = {0};

	unsigned char	amr_output_data[320] = {0};
#if CONFIG_AUDIO_RECORD_MP3
	unsigned char 	mp3_output_data[MP3_SIZE] = {0};
#endif
#if CONFIG_AUDIO_RECORD_AMRNB
	amrnb_enc_handle record_handle = 0;
#endif
	int             bytesRead = 0;
	int amr_enc_count = 0;

	while(1){
		data_size = 0;
		if(0 != first_entry){
			_aud_record.status = HELIOS_RECORD_STOP;
		}
		else{
			first_entry = !first_entry;
		}
		Helios_MsgQ_Get(_aud_record.msg,&recvMsg,sizeof(_Aud_Record_CONFIG),HELIOS_WAIT_FOREVER);
		AUDLOGE("rec file_name:%s format:%d,channels:%d, samplerate:%d,stauts[%d]\n",recvMsg.file_name, recvMsg.format, recvMsg.conf.channels, recvMsg.conf.samplerate,_aud_record.status);
		
		if(HELIOS_RECORD_RELEASE_MSG != _aud_record.status){
			_aud_record.status = HELIOS_RECORD_STOPPING;
			goto todo;
		}
		_aud_record.status = HELIOS_RECORD_RECORDING;
        
		if(recvMsg.format >= HELIOS_RECORD_FORMAT_MAX){
			goto todo;
		}
		
		_aud_record.read_hd = Helios_PCM_Open(recvMsg.conf.channels, recvMsg.conf.samplerate, HELIOS_PCM_READ_FLAG | HELIOS_PCM_BLOCK_FLAG);
		if(_aud_record.read_hd==NULL){
			AUDLOGE("pcm init failed pcm open failed\n");
			goto todo;
		}

		//fs_free_size_count = Helios_fs_free_size('U');
		//AUDLOGD("fs_free_size_count = %d\n",fs_free_size_count);
		
		if(recvMsg.format == HELIOS_RECORD_WAV_FILE || recvMsg.format == HELIOS_RECORD_WAV_STREAM) {
			if(recvMsg.format == HELIOS_RECORD_WAV_FILE) {
				FileID = Helios_Aud_fopen(recvMsg.file_name, "w");
				if(FileID == NULL) {
					AUDLOGE("open %s failed!\n",recvMsg.file_name);
					goto todo;
				}
				
				int seek_cnt = _file_init_header(HELIOS_RECORD_WAV_FILE, FileID, recvMsg.conf.channels, recvMsg.conf.samplerate);
				
				Helios_Aud_fseek(FileID, seek_cnt, 0);
			}
			read_file_start = 1;
			read_count = 0;
			if(recvMsg.cb) {
				recvMsg.cb(recvMsg.file_name, 0, AUD_RECORD_START);
			}
			while(read_file_start)
			{
				read_ret = Helios_PCM_Read(_aud_record.read_hd, record_buf, AUD_RECORD_BUF_LEN);
				if (read_ret > 0){
					
					if(recvMsg.format == HELIOS_RECORD_WAV_FILE) 
					{
						if(Helios_Aud_fwrite(record_buf, read_ret, 1, FileID) < 0){ 
						   AUDLOGE("no space to write!!!\n");
						   read_count = 0;
						   ret = AUD_RECORD_ERROR;
						   goto todo;
						
						} else {
						   read_count += read_ret;
						} 
						data_size += read_ret;
					} else if(recvMsg.format == HELIOS_RECORD_WAV_STREAM) {
						if(recvMsg.cb) {
							recvMsg.cb(record_buf, read_ret, AUD_RECORD_DATA);
						}
					}
				}
				ret = AUD_RECORD_FINISHED;
//				if(read_count > (fs_free_size_count*4/10)) {
//					ret = AUD_RECORD_DISK_FULL;
//					read_file_start = 0;
//				}
			}
			goto todo;
			
		}
#ifdef CONFIG_AUDIO_RECORD_AMRNB
		else if(recvMsg.format == HELIOS_RECORD_AMRNB_FILE || recvMsg.format == HELIOS_RECORD_AMRNB_STREAM) {
			_amrencode_data_init(0, 0);
			if(recvMsg.format == HELIOS_RECORD_AMRNB_FILE) 
			{
				FileID = Helios_Aud_fopen(recvMsg.file_name, "w");
				if(FileID == NULL) {
					AUDLOGE("open %s failed!\n",recvMsg.file_name);
					goto todo;
				}
				
				int seek_cnt = _file_init_header(HELIOS_RECORD_AMRNB_FILE, FileID, recvMsg.conf.channels, recvMsg.conf.samplerate);
				Helios_Aud_fseek(FileID, seek_cnt, 0);
			}
	
			read_file_start = 1;
			read_count = 0;
			if(recvMsg.cb) {
				recvMsg.cb(recvMsg.file_name, 0, AUD_RECORD_START);
			}
			while(read_file_start)
			{
				read_ret = Helios_PCM_Read(_aud_record.read_hd, record_buf, 320);
				if (read_ret != 320) {
					AUDLOGE("read_ret:%d \n", read_ret);
					goto todo;
				}

				amr_enc_count = _amrencode_data(record_buf, amr_output_data);
				if(amr_enc_count < 0) {
					AUDLOGE("amr_enc_count:%d \n", amr_enc_count);
					goto todo;
				}

//				AUDLOGD("pcm len[%d], amr len[%d]\n", read_ret, amr_enc_count);
		
				if(recvMsg.format == HELIOS_RECORD_AMRNB_FILE) 
				{
					if(Helios_Aud_fwrite(amr_output_data, amr_enc_count, 1, FileID) < 0){ 
					   AUDLOGE("no space to write!!!\n");
					   read_count = 0;
					   ret = AUD_RECORD_ERROR;
					   goto todo;
					
					} else {
					   read_count += read_ret;
					} 
					data_size += read_ret;
				} else if (recvMsg.format == HELIOS_RECORD_AMRNB_STREAM) {
					if(recvMsg.cb) {
						recvMsg.cb((char*)amr_output_data, amr_enc_count, AUD_RECORD_DATA);
					}
				}
				 
				ret = AUD_RECORD_FINISHED;
//				if(read_count > (fs_free_size_count*4/10)) {
//					ret = AUD_RECORD_DISK_FULL;
//					read_file_start = 0;
//				}
			}
			goto todo;
			
		}
#endif
#if CONFIG_AUDIO_RECORD_MP3
		else if(recvMsg.format == HELIOS_RECORD_MP3_FILE || recvMsg.format == HELIOS_RECORD_MP3_STREAM) {
			if(_mp3encode_data_init(recvMsg.conf.channels, recvMsg.conf.samplerate) != 0) {
				goto todo;
			}
			if(recvMsg.format == HELIOS_RECORD_MP3_FILE) 
			{
				FileID = Helios_Aud_fopen(recvMsg.file_name, "w");
				if(FileID == NULL) {
					AUDLOGE("open %s failed!\n",recvMsg.file_name);
					goto todo;
				}
				
				int seek_cnt = _file_init_header(HELIOS_RECORD_MP3_FILE, FileID, recvMsg.conf.channels, recvMsg.conf.samplerate);
				Helios_Aud_fseek(FileID, seek_cnt, 0);
			}
	
			read_file_start = 1;
			read_count = 0;
			if(recvMsg.cb) {
				recvMsg.cb(recvMsg.file_name, 0, AUD_RECORD_START);
			}
			while(read_file_start)
			{
				read_ret = Helios_PCM_Read(_aud_record.read_hd, record_buf, 882);
				if (read_ret != 882) {
					AUDLOGE("read_ret:%d \n", read_ret);
					ret = AUD_RECORD_ERROR;
					goto todo;
				}
				
				Helios_Mutex_Lock(_mp3_mutex, HELIOS_WAIT_FOREVER);
				Helios_rb_write(Helios_ring_buf, record_buf, 882);
				Helios_Mutex_Unlock(_mp3_mutex);
//				Helios_rb_write(Helios_ring_buf, record_buf, 320);

				amr_enc_count = _mp3encode_data(record_buf, 882, mp3_output_data, false);
				if(amr_enc_count < 0) {
					AUDLOGE("amr_enc_count:%d \n", amr_enc_count);
					ret = AUD_RECORD_ERROR;
					goto todo;
				}

				AUDLOGD("pcm len[%d], amr len[%d]\n", read_ret, amr_enc_count);
		
				if(recvMsg.format == HELIOS_RECORD_MP3_FILE && amr_enc_count > 0) 
				{
					if(Helios_Aud_fwrite(mp3_output_data, amr_enc_count, 1, FileID) < 0){ 
					   AUDLOGE("no space to write!!!\n");
					   read_count = 0;
					   ret = AUD_RECORD_DISK_FULL;
					   goto todo;
					
					} else {
					   read_count += amr_enc_count;
					} 
				} else if (recvMsg.format == HELIOS_RECORD_MP3_STREAM && amr_enc_count > 0) {
					if(recvMsg.cb) {
						recvMsg.cb((char*)mp3_output_data, amr_enc_count, AUD_RECORD_DATA);
					}
				}
				 
				ret = AUD_RECORD_FINISHED;
//				if(read_count > (fs_free_size_count*4/10)) {
//					ret = AUD_RECORD_DISK_FULL;
//					read_file_start = 0;
//				}
			}

			if(recvMsg.format == HELIOS_RECORD_MP3_FILE && amr_enc_count > 0) {
				amr_enc_count = _mp3encode_data(record_buf, 320, mp3_output_data, true);
				if(amr_enc_count < 0) {
					AUDLOGE("amr_enc_count:%d \n", amr_enc_count);
					goto todo;
				}
			}
			goto todo;
			
		}
#endif
		else {
			AUDLOGE("error format\n");
		}
		
	todo:
		
		if(_aud_record.read_hd) {
			Helios_PCM_Close(_aud_record.read_hd);
			_aud_record.read_hd = NULL;
		}
#ifdef CONFIG_AUDIO_RECORD_AMRNB
		_amrencode_data_deinit();
#endif

#if CONFIG_AUDIO_RECORD_MP3		
		_mp3encode_data_deinit();

		if(Helios_ring_buf) {
			Helios_rb_init(Helios_ring_buf);
		}
#endif


		if(FileID) {
			//AUDLOGE("fs_free_size_count = %d\nnow_use_size=%d\n",fs_free_size_count,read_count);
			_set_wavfile_size(FileID, data_size);
			Helios_Aud_fclose(FileID);
			FileID = NULL;
		}

		switch (recvMsg.format)
		{
		case HELIOS_RECORD_WAV_FILE:
			read_count+=44;
			AUDLOGI("read_count = %d\n",read_count);
			if(recvMsg.cb) {
				AUDLOGI("aud_file_name = %s\n",recvMsg.file_name);
				AUDLOGI("read_count = %d\n",read_count);
				recvMsg.cb(recvMsg.file_name, read_count, ret);
			}
			break;
		case HELIOS_RECORD_AMRNB_FILE:
			read_count+=6;
			AUDLOGI("read_count = %d\n",read_count);
			if(recvMsg.cb) {
				AUDLOGI("aud_file_name = %s\n",recvMsg.file_name);
				AUDLOGI("read_count = %d\n",read_count);
				recvMsg.cb(recvMsg.file_name, read_count, ret);
			}
			break;
		case HELIOS_RECORD_MP3_FILE:
			read_count+=10;
			AUDLOGI("read_count = %d\n",read_count);
			if(recvMsg.cb) {
				AUDLOGI("aud_file_name = %s\n",recvMsg.file_name);
				AUDLOGI("read_count = %d\n",read_count);
				recvMsg.cb(recvMsg.file_name, read_count, ret);
			}
			break;
		case HELIOS_RECORD_MP3_STREAM:
		case HELIOS_RECORD_AMRNB_STREAM:
		case HELIOS_RECORD_WAV_STREAM:
			if(recvMsg.cb) {
				recvMsg.cb("stream", 0, ret);
			}
			break;
		default:
			break;
		}

		
		if(recvMsg.file_name) {
			free(recvMsg.file_name);
			recvMsg.file_name = NULL;
		}
	}	


	return 0;
}

#if CONFIG_AUDIO_RECORD_MP3
#define MP3_RINGBUFF_LEN (6*1024)
#endif

static int Helios_Audio_record_init(void)
{
	int ret = 0;

#if CONFIG_AUDIO_RECORD_MP3	
    if(0 == _mp3_mutex) {
        _mp3_mutex = Helios_Mutex_Create();
    }

	
	if (Helios_ring_buf == NULL)
	{
		Helios_ring_buf = malloc(sizeof(Helios_ring_buf_t) + (MP3_RINGBUFF_LEN));
		if(!Helios_ring_buf) {
			AUDLOGE("malloc[%d] fail\n", sizeof(Helios_ring_buf_t) + (MP3_RINGBUFF_LEN));
			return -1;
		}
		Helios_rb_init(Helios_ring_buf);
		Helios_ring_buf->capacity = (MP3_RINGBUFF_LEN);

		
		Helios_ring_buf->wcond = Helios_Semaphore_Create(1, 0);

		assert(Helios_ring_buf->wcond != 0);
	}
#endif
	
	if(_aud_record.init_status != TRUE) {
		size_t record_stack_size = 16*1024;
		_aud_record.msg = Helios_MsgQ_Create(2, sizeof(_Aud_Record_CONFIG));
		assert(_aud_record.msg);
		
		
		AUDLOGD("audio record reqmsg create OK!\n");
		
		Helios_ThreadAttr attr = {0};
		attr.name = "audio_record";
		attr.stack_size = record_stack_size;
		attr.priority = 100;
		attr.entry = _audio_record_task_thread;
		
		_aud_record.task = Helios_Thread_Create(&attr);
		assert(_aud_record.task);
		
		_aud_record.init_status = TRUE;

		Helios_msleep(20);
	}

	return 0;
}



static int check_format(char *path) {
	if(path == NULL) return -1;
	
	if(strlen(path) < 1) {
		return -2;
	}
	
	char *st_old = NULL;
	char *st_now = NULL;
	
	int is_first = 1;
	while(1) {
		if(is_first == 1) {
			is_first = 0;
			st_now = strchr(path, '.');
		} else {
			st_now = strchr(st_now+1, '.');
		}
		if(st_now != NULL){
			st_old = st_now+1;
		} else {
			break;
		}
	}
	if(st_old == NULL) return FORMAT_AMR;
	
	if(strncmp(st_old, "wav", 3)==0){
		return FORMAT_WAV;
	}  else if(strncmp(st_old, "mp3", 3)==0) {
		return FORMAT_MP3;
	} else {
		return FORMAT_AMR;
	}
}

static int _Audio_RecordStart(RECORD_FROMAT format, char* name, Helios_AUDRecordInitStruct* record_para)
{
	Helios_Audio_record_init();
	
	_Aud_Record_CONFIG config_para = {0};
	
	config_para.conf.channels = (int) record_para->record_para->channels;
	config_para.conf.samplerate = (int) record_para->record_para->samplerate;

	if(name != NULL) {
		config_para.file_name = calloc(1, strlen(name)+1);
		memcpy(config_para.file_name, name, strlen(name));
	}

	config_para.cb = record_para->cb;

	_aud_record.status = HELIOS_RECORD_RELEASE_MSG;

	config_para.format = format;
	
	
	if(0 != Helios_MsgQ_Put(_aud_record.msg, &config_para, sizeof(_Aud_Record_CONFIG), 20))
	{
		AUDLOGE("send timeout\n");
		return -1;
	}
	
	return 0;
		
}


int Helios_Audio_FileRecordStart(char* name, Helios_AUDRecordInitStruct* record_para){
	int format_n = check_format(name);
	AUDLOGD("Rec format is:%d\n",format_n);
	RECORD_FROMAT format = HELIOS_RECORD_WAV_FILE;
	if(format_n == FORMAT_WAV) {		
		format = HELIOS_RECORD_WAV_FILE;
	} else if(format_n == FORMAT_AMR) {
		format = HELIOS_RECORD_AMRNB_FILE;
	} else if(format_n == FORMAT_MP3) {
		format = HELIOS_RECORD_MP3_FILE;
	}
	else {
		return -1;
	}

	
	return _Audio_RecordStart(format, name, record_para);
}

static int _Audio_RecordStop(void) {
	if(HELIOS_RECORD_STOP == _aud_record.status){
		return -1;
	}

	if(HELIOS_RECORD_RELEASE_MSG == _aud_record.status){
		_aud_record.status = HELIOS_RECORD_STOPPING;
	}

	read_file_start = 0;
	return 0;
	}

int Helios_Audio_FileRecordStop() 
{
	return _Audio_RecordStop();
}




/**
 * @brief:
 *      Audio record start
 *
 * @param:
 *      \record_para    [in]    - Audio parameter structure
 *      
 * @return:
 *      audio record start result, 0 for success,-1 for failure
 */
int Helios_Audio_StreamRecordStart(Helios_AUDRecordInitStruct* record_para) 
{
	
	RECORD_FROMAT format = HELIOS_RECORD_WAV_STREAM;
	if(record_para->format == HELIOS_AUDIO_FORMAT_WAVPCM) {
		format = HELIOS_RECORD_WAV_STREAM;
	} 
#ifdef CONFIG_AUDIO_RECORD_AMRNB
	else if(record_para->format == HELIOS_AUDIO_FORMAT_AMRNB) {
		format = HELIOS_RECORD_AMRNB_STREAM;
	} 
#endif
#ifdef CONFIG_AUDIO_RECORD_MP3
	else if(record_para->format == HELIOS_AUDIO_FORMAT_MP3) {
		if(record_para->record_para->samplerate != 22050) {
			record_para->record_para->samplerate = 22050;
		}
		format = HELIOS_RECORD_MP3_STREAM;
	} 
#endif
	else {
		return -1;
	}

	return _Audio_RecordStart(format, NULL, record_para);
}


int Helios_Audio_StreamRecordStop() 
{
	return _Audio_RecordStop();
}

