#include <stdio.h>
#include <stdlib.h>
#include <assert.h>

#include "helios_os.h"
#include "helios_audio_py.h"
#include "watermark.h"

#include "helios_include.h"
#include "helios_audio_fs.h"
#include "helios_audio_mp3.h"

extern int ql_decode_start(unsigned char *file_inputbuf, unsigned short *pcm_outputbuf, unsigned int bytes_read);
extern void ql_release(void);

static int Helios_aud_stop_play_flag = 1;
static int __jump_count = 3;
static int __jump_cnt = -1;

static PCM_HANDLE_T mp3_pcm_whdl = NULL;
static bool _audio_play_state = 0;
static bool _mp3_stream_change_rate = false;


#if (defined(PLAT_ASR_1803s) || defined(PLAT_ASR_1803sc) || defined(BOARD_EC600MCN_CC_EXT) || defined(BOARD_EC600MCN_LF_SLPOC) \
    ||defined(PLAT_Unisoc_8850) || defined(PLAT_Unisoc_8850_R02)|| defined(BOARD_EC600MCN_CC_EXT_TTS) || defined(BOARD_EC800MCN_CC_TTS) \
    || defined(BOARD_EC800KCN_CC_TTS) || defined(BOARD_EC600KCN_CC_TTS) || defined(PLAT_ASR_1609) || defined(BOARD_EC800MCN_LF_CCG)) \
	|| defined(BOARD_EC600MCN_LF_SPISD)

#define USR_HELIOS_AUDIO 1

#endif



typedef struct Helios_ring_buf_struct
{
	int capacity;
	int rpos;
	int wpos;
	Helios_Sem_t wcond;
	Helios_Sem_t rcond;
	unsigned char data[0];
} Helios_ring_buf_t;

Helios_ring_buf_t *Helios_ring_buf = NULL;
Helios_ring_buf_t *pcm_ring_buf = NULL;

static Helios_Mutex_t Helios_mp3_read_mutex = 0;
static int open_flag = 1;

static uint8_t STREAM_IsNot_SwitchOff_Actively = 0; // add by felix for ZJY customer
static uint8_t Stream_first_frame_skip = 0;			// add by felix for ZJY customer

Helios_aud_config Helios_config;
static Helios_Thread_t mp3_Decoder = 0;
static Helios_Sem_t Helios_mp3_stop_sema;
static int is_mp3_playing = 0;
static Helios_Sem_t Helios_mp3_Sema = 0;

#define HELIOS_PCM_BLOCK_FLAG (0x01)
#define HELIOS_PCM_NONBLOCK_FLAG (0x02)
#define HELIOS_PCM_READ_FLAG (0x04)
#define HELIOS_PCM_WRITE_FLAG (0x08)

static cb_on_player aud_play_cb = NULL;

static unsigned int const samplerate_table[3][3] =
	{
		{44100, 48000, 32000},
		{22050, 24000, 16000},
		{11025, 12000, 8000},
};

// layer I
static const int kBitrateV1[] = {
	32, 64, 96, 128, 160, 192, 224, 256,
	288, 320, 352, 384, 416, 448};

static const int kBitrateV2[] = {
	32, 48, 56, 64, 80, 96, 112, 128,
	144, 160, 176, 192, 224, 256};

// layer II or III
static const int kBitrateV1L2[] = {
	32, 48, 56, 64, 80, 96, 112, 128,
	160, 192, 224, 256, 320, 384};

static const int kBitrateV1L3[] = {
	32, 40, 48, 56, 64, 80, 96, 112,
	128, 160, 192, 224, 256, 320};

static const int kBitrateV3[] = {
	8, 16, 24, 32, 40, 48, 56, 64,
	80, 96, 112, 128, 144, 160};






#ifdef USR_HELIOS_AUDIO
static void __pcm_play_cb(void *handle, int event){
	AUDLOGD("__pcm_play_cb event[%d][%x][%x]\n",event,handle,mp3_pcm_whdl);
	
	if(event == HELIOS_AUD_PLAYER_NODATA_STRAT || event == HELIOS_AUD_PLAYER_FINISHED || event == HELIOS_AUD_PLAYER_CLOSE || event == HELIOS_AUD_PLAYER_REQURST_CLOSE) {
		if(mp3_pcm_whdl){
			Helios_PCM_Close(mp3_pcm_whdl);
			mp3_pcm_whdl = NULL;
		}
	}
	if(event == HELIOS_AUD_PLAYER_FINISHED || event == HELIOS_AUD_PLAYER_CLOSE) {
		_audio_play_state = 0;
	}
	
	if(aud_play_cb != NULL && _mp3_stream_change_rate == false) {
		aud_play_cb("stream", 0, event);
	}
	if(event == HELIOS_AUD_PLAYER_START)
	{
		_mp3_stream_change_rate = false;
	}
}
#endif

static int Helios_rb_init(Helios_ring_buf_t *rb)
{
	if (NULL == rb)
	{
		AUDLOGE("rb is NULL\n");
		return -1;
	}

	// rb->capacity = 1024;
	rb->rpos = 0;
	rb->wpos = 0;

	return 0;
}

static int Helios_rb_free_space(Helios_ring_buf_t *rb)
{
	int cur_data_len = 0;

	cur_data_len = (rb->wpos + rb->capacity + 1 - rb->rpos) % (rb->capacity + 1);

	return (rb->capacity - cur_data_len);
}

static int Helios_rb_data_space(Helios_ring_buf_t *rb)
{
	int cur_data_len = 0;

	cur_data_len = (rb->wpos + rb->capacity + 1 - rb->rpos) % (rb->capacity + 1);

	return cur_data_len;
}

static int Helios_rb_write(Helios_ring_buf_t *rb, unsigned char *data, int len)
{
	int i = 0;
	int cur_free_len = 0, write_len = 0;

	if (NULL == rb || NULL == data || 0 == len)
	{
		return -1;
	}

	cur_free_len = Helios_rb_free_space(rb);
	if (0 == cur_free_len)
	{
		return 0;
	}

	if (cur_free_len < len)
	{
		write_len = cur_free_len;
	}
	else
	{
		write_len = len;
	}

	for (i = 0; i < write_len; i++)
	{
		rb->data[rb->wpos] = data[i];
		rb->wpos = (rb->wpos + 1) % (rb->capacity + 1);
	}

	return write_len;
}

static int Helios_rb_read(Helios_ring_buf_t *rb, unsigned char *data, int len)
{
	int i = 0;
	int cur_data_len = 0, read_len = 0;

	if (NULL == rb || NULL == data || 0 == len)
	{
		return -1;
	}

	cur_data_len = Helios_rb_data_space(rb);
	if (0 == cur_data_len)
	{
		return 0;
	}

	if (cur_data_len < len)
	{
		read_len = cur_data_len;
	}
	else
	{
		read_len = len;
	}

	for (i = 0; i < read_len; i++)
	{
		data[i] = rb->data[rb->rpos];
		rb->rpos = (rb->rpos + 1) % (rb->capacity + 1);
	}

	return read_len;
}

static int helios_find_file(char *name)
{
	HeliosAudFILE *fileID = NULL;

	fileID = Helios_Aud_fopen(name, "r");
	if (fileID)
	{
		Helios_Aud_fclose(fileID);

		return 1;
	}
	return 0;
}

static unsigned int Helios_u32_to_int(const unsigned char *ptr)
{
	return ptr[0] << 24 | ptr[1] << 16 | ptr[2] << 8 | ptr[3];
}

int get_mp3_frame_para(unsigned char *mp3_buffer, Helios_aud_config *para)
{
	int ret;
	int mpeg = 0, layer, index, bitrate, samplerate, ch_bits, channels;
	char head[4] = {0};
	char *mp3_buffer_rev = (char *)mp3_buffer;
	int header = 0, version = 0;

	memcpy(head, mp3_buffer_rev, 4);

	// AUDLOGI("get_mp3_frame_para  %02X, %02X, %02X, %02X\n",head[0], head[1], head[2], head[3]);

	/* sampling_frequency */
	switch ((head[1] & 0x18) >> 3)
	{
	case 0x00:
		mpeg = 2;
		break;
	case 0x01:
		return -1;
		break;
	case 0x02:
		mpeg = 1;
		break;
	case 0x03:
		mpeg = 0;
		break;
	default:
		return -1;
		break;
	}

	index = (head[2] & 0x0c) >> 2;
	if (index == 3)
	{
		return -1;
	}

	samplerate = samplerate_table[mpeg][index];

	/* channels */
	ch_bits = (head[3] & 0xC0) >> 6;
	channels = ch_bits == 3 ? 1 : 2;

	para->channels = channels;
	para->samplerate = samplerate;

	// AUDLOGI("pcm: channels = %d, rate= %d\n", channels, samplerate);

	header = Helios_u32_to_int((const unsigned char *)head);
	if ((header & 0xffe00000) != 0xffe00000)
	{
		return -1;
	}

	unsigned padding = (header >> 9) & 1;
	unsigned bitrate_index = (header >> 12) & 0x0f;

	version = (header >> 19) & 3;
	if (version == 0x01)
	{
		return -1;
	}

	layer = (header >> 17) & 3;
	if (layer == 0x00)
	{
		return -1;
	}

	if (layer == 3)
	{
		bitrate = (version == 3 /* V1 */) ? kBitrateV1[bitrate_index - 1]
										  : kBitrateV2[bitrate_index - 1];

		para->len_size = (12000 * bitrate / samplerate + padding) * 4;
	}
	else
	{
		if (version == 3 /* V1 */)
		{
			bitrate = (layer == 2 /* L2 */) ? kBitrateV1L2[bitrate_index - 1]
											: kBitrateV1L3[bitrate_index - 1];
		}
		else
		{
			// V2 (or 2.5)
			bitrate = kBitrateV3[bitrate_index - 1];
		}

		if (version == 3 /* V1 */)
		{
			para->len_size = 144000 * bitrate / samplerate + padding;
		}
		else
		{
			// V2 or V2.5
			unsigned int tmp = (layer == 1 /* L3 */) ? 72000 : 144000;
			para->len_size = tmp * bitrate / samplerate + padding;
		}
	}

	// AUDLOGI("mp3: bitrate = %d, samplerate = %d, ch_bits = %d, channels = %d, para->len_size =%d\n",
	//		   bitrate, samplerate, ch_bits, channels, para->len_size);

	return 0;
}
static uint32_t U32_AT(const uint8_t *ptr)
{
	return ptr[0] << 24 | ptr[1] << 16 | ptr[2] << 8 | ptr[3];
}

static bool parseHeader(
	uint32_t header, size_t *frame_size,
	uint32_t *out_sampling_rate, uint32_t *out_channels,
	uint32_t *out_bitrate, uint32_t *out_num_samples)
{
	*frame_size = 0;

	if (out_sampling_rate)
	{
		*out_sampling_rate = 0;
	}

	if (out_channels)
	{
		*out_channels = 0;
	}

	if (out_bitrate)
	{
		*out_bitrate = 0;
	}

	if (out_num_samples)
	{
		*out_num_samples = 1152;
	}

	if ((header & 0xffe00000) != 0xffe00000)
	{
		return false;
	}

	unsigned version = (header >> 19) & 3;

	if (version == 0x01)
	{
		return false;
	}

	unsigned layer = (header >> 17) & 3;

	if (layer == 0x00)
	{
		return false;
	}

	unsigned bitrate_index = (header >> 12) & 0x0f;

	if (bitrate_index == 0 || bitrate_index == 0x0f)
	{
		// Disallow "free" bitrate.
		return false;
	}

	unsigned sampling_rate_index = (header >> 10) & 3;

	if (sampling_rate_index == 3)
	{
		return false;
	}

	static const int kSamplingRateV1[] = {44100, 48000, 32000};
	int sampling_rate = kSamplingRateV1[sampling_rate_index];
	if (version == 2 /* V2 */)
	{
		sampling_rate /= 2;
	}
	else if (version == 0 /* V2.5 */)
	{
		sampling_rate /= 4;
	}

	unsigned padding = (header >> 9) & 1;

	if (layer == 3)
	{
		// layer I

		static const int kBitrateV1[] = {
			32, 64, 96, 128, 160, 192, 224, 256,
			288, 320, 352, 384, 416, 448};

		static const int kBitrateV2[] = {
			32, 48, 56, 64, 80, 96, 112, 128,
			144, 160, 176, 192, 224, 256};

		int bitrate =
			(version == 3 /* V1 */)
				? kBitrateV1[bitrate_index - 1]
				: kBitrateV2[bitrate_index - 1];

		if (out_bitrate)
		{
			*out_bitrate = bitrate;
		}

		*frame_size = (12000 * bitrate / sampling_rate + padding) * 4;

		if (out_num_samples)
		{
			*out_num_samples = 384;
		}
	}
	else
	{
		// layer II or III

		static const int kBitrateV1L2[] = {
			32, 48, 56, 64, 80, 96, 112, 128,
			160, 192, 224, 256, 320, 384};

		static const int kBitrateV1L3[] = {
			32, 40, 48, 56, 64, 80, 96, 112,
			128, 160, 192, 224, 256, 320};

		static const int kBitrateV2[] = {
			8, 16, 24, 32, 40, 48, 56, 64,
			80, 96, 112, 128, 144, 160};

		int bitrate;
		if (version == 3 /* V1 */)
		{
			bitrate = (layer == 2 /* L2 */)
						  ? kBitrateV1L2[bitrate_index - 1]
						  : kBitrateV1L3[bitrate_index - 1];

			if (out_num_samples)
			{
				*out_num_samples = 1152;
			}
		}
		else
		{
			// V2 (or 2.5)

			bitrate = kBitrateV2[bitrate_index - 1];
			if (out_num_samples)
			{
				*out_num_samples = (layer == 1 /* L3 */) ? 576 : 1152;
			}
		}

		if (out_bitrate)
		{
			*out_bitrate = bitrate;
		}

		if (version == 3 /* V1 */)
		{
			*frame_size = 144000 * bitrate / sampling_rate + padding;
		}
		else
		{
			// V2 or V2.5
			size_t tmp = (layer == 1 /* L3 */) ? 72000 : 144000;
			*frame_size = tmp * bitrate / sampling_rate + padding;
		}
	}

	if (out_sampling_rate)
	{
		*out_sampling_rate = sampling_rate;
	}

	if (out_channels)
	{
		int channel_mode = (header >> 6) & 3;

		*out_channels = (channel_mode == 3) ? 1 : 2;
	}

	return true;
}
static uint32_t mp3_frame_size, mp3_sample_rate, mp3_num_channels;
static uint8_t _header_buf_pre[4] = {0};
static int get_mp3_frame(void *buffer, uint32_t *size)
{
	uint32 event;
	uint32 wm_left_size;
	uint8 buf[4];
	uint32 bytesRead;
	uint32_t header;
	int status = 0, ret = 0;

	while (Helios_aud_stop_play_flag == 0)
	{
		if (Helios_rb_data_space(Helios_ring_buf) < 4)
		{
			AUDLOGE("get_mp3_frame wait for more data, %d\n", __LINE__);
			// status = Helios_Semaphore_Acquire(Helios_ring_buf->rcond, HELIOS_WAIT_FOREVER);
			////assert(status == 0);
			return -2;
		}
		else
		{
			Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
			Helios_rb_read(Helios_ring_buf, buf, 4);
			Helios_Mutex_Unlock(Helios_mp3_read_mutex);

			AUDLOGD("header[%x][%x][%x][%x][%s]\n", buf[0],buf[1],buf[2],buf[3],(char*)buf);
			if(strncmp("JUMP", (const char*)buf, 4) == 0)
			{
				AUDLOGD("start jump, jump frame count is [%d]\n", __jump_count);
				__jump_cnt = __jump_count;
				
				Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
				Helios_rb_read(Helios_ring_buf, buf, 4);
				Helios_Mutex_Unlock(Helios_mp3_read_mutex);

				AUDLOGD("header[%x][%x][%x][%x]\n", buf[0],buf[1],buf[2],buf[3]);
			}
			


			if (Helios_rb_free_space(Helios_ring_buf) > 500)
			{
				Helios_Semaphore_Poll(Helios_ring_buf->wcond, &ret);
				if (ret == 0)
				{
					Helios_Semaphore_Release(Helios_ring_buf->wcond);
				}
			}
			header = U32_AT(buf);
			if (!parseHeader(header, &mp3_frame_size,
							 &mp3_sample_rate, &mp3_num_channels, 0, 0))
			{
				continue;
			}
			else
			{
				if (mp3_frame_size > 2 * 1000)
				{
					continue;
				}
				AUDLOGD("parseHeader frame_size:%d sample_rate:%d num_channels:%d\n", mp3_frame_size, mp3_sample_rate, mp3_num_channels);
				break;
			}
		}
	}
	uint8_t delay_time = 5;
	while (Helios_aud_stop_play_flag == 0)
	{
		if (Helios_rb_data_space(Helios_ring_buf) < mp3_frame_size - 4)
		{
			AUDLOGD("get_mp3_frame wait for more data, mp3_frame_size:%d, data space:%d\n", __LINE__, mp3_frame_size, Helios_rb_data_space(Helios_ring_buf));
			if (delay_time--)
			{
				Helios_msleep(20);
			}
			else
			{
				return -3;
			}
		}
		else
		{
			memcpy(buffer, buf, 4);
			Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
			Helios_rb_read(Helios_ring_buf, buffer + 4, mp3_frame_size - 4);
			Helios_Mutex_Unlock(Helios_mp3_read_mutex);
			*size = mp3_frame_size;
			break;
		}
	}

	if (Helios_aud_stop_play_flag == 1)
	{
		return -1;
	}
	memcpy(_header_buf_pre, buf, 4);
	return 0;
}

int Helios_mp3_Clear_Cache()
{
	if (0 != Helios_mp3_read_mutex)
		Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
	Helios_rb_init(Helios_ring_buf);
	if (0 != Helios_mp3_read_mutex)
		Helios_Mutex_Unlock(Helios_mp3_read_mutex);
	return 0;
}

int Helios_mp3_get_cache_size()
{
	if (0 != Helios_mp3_read_mutex)
		Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
	int ret = Helios_rb_data_space(Helios_ring_buf);
	if (0 != Helios_mp3_read_mutex)
		Helios_Mutex_Unlock(Helios_mp3_read_mutex);
	return ret;
}

#define Helios_PCM_TASK_CONTINUE (0x01)
#define Helios_PCM_STOP (0x02)
#define Helios_PCM_TASK_MASK (Helios_PCM_STOP | Helios_PCM_TASK_CONTINUE)

#define PCM_PINGPANG_BUF_LEN 4608
static Helios_OSFlag_t dec_pcm_flag_Ref;

void Helios_wait(void)
{
	unsigned int event = 0;

	Helios_Flag_Wait(dec_pcm_flag_Ref, Helios_PCM_TASK_MASK, Helios_FLAG_OR_CLEAR, &event, HELIOS_WAIT_FOREVER);

	AUDLOGD("Helios_wait_finish");

	if (Helios_PCM_STOP == (Helios_PCM_STOP & event))
	{
		AUDLOGD("Helios_pcm_stop");
	}
}



int Helios_play_stream_start(unsigned int rate, unsigned int ch, unsigned short *pcm_buf, unsigned int frameByteSize)
{
	int ret = 0;
	int _count = 100;
	static unsigned int rate_pre = 0; 
	static unsigned int ch_pre = 0;
	if(mp3_pcm_whdl && (rate_pre != rate || ch_pre != ch))
	{
		AUDLOGD("change rate\r\n");
		_mp3_stream_change_rate = true;
		
		while(_audio_play_state == 1 && _count-- > 1){
			AUDLOGD("audio1 is playing, waiting[%d]...\n",_count);
			Helios_msleep(20);
		}
	}
	rate_pre = rate;
	ch_pre = ch;
	_count = 100;
	
	if (mp3_pcm_whdl == NULL)
	{
#ifdef USR_HELIOS_AUDIO
		while(_audio_play_state == 1 && _count-- > 1){
			AUDLOGD("audio1 is playing, waiting[%d]...\n",_count);
			Helios_msleep(20);
		}
#endif
#ifdef CONFIG_AUDIO_PWM
		mp3_pcm_whdl = Helios_Aud_PWM_Open(CONFIG_AUDIO_PWM_PIN, 1, rate, HELIOS_PCM_WRITE_FLAG | HELIOS_PCM_BLOCK_FLAG);
#else
		mp3_pcm_whdl = Helios_PCM_Open(ch, rate, HELIOS_PCM_WRITE_FLAG | HELIOS_PCM_BLOCK_FLAG);
#ifdef USR_HELIOS_AUDIO
		Helios_PCM_Play_Cb(mp3_pcm_whdl, __pcm_play_cb);
		_audio_play_state = 1;

#endif
#endif

	}

	if (mp3_pcm_whdl == NULL)
	{
		return -1;
	}
	
#ifdef CONFIG_AUDIO_PWM
	ret = Helios_Aud_PWM_Write(mp3_pcm_whdl, pcm_buf, frameByteSize);
#else
	ret = Helios_PCM_Write(mp3_pcm_whdl, pcm_buf, frameByteSize);
#endif

	if (ret < 0)
	{
		AUDLOGE("kOutputBufferSize[%d] ret[%d]\n", frameByteSize, ret);
		return -1;
	}
	Helios_Flag_Release(dec_pcm_flag_Ref, Helios_PCM_TASK_CONTINUE, Helios_FLAG_OR);

	return 0;
}


int isSilenceFrame(const float* samples, int length) {
    int count = 0;
    for (int i = 0; i < length; i++) {
        if (samples[i] == 0.0f) {
            count++;
        }
    }
	if(count > mp3_frame_size/2 - 10)
    	return 1;
	else{
		return 0;
	}
}


int Helios_mp3_dec(void)
{
	int ret = 0, rc = 0;
	unsigned short *Helios_pcm_outbuf = NULL;
	unsigned char *buff = NULL;
	int i = 0;
	int length = 0;
	open_flag = 1;
	uint32 bytesRead = 0;
	char nodataStartCnt = 5;
	char is_resume = 0;
	int is_silence = 0;
	char is_mutex = 0;
	char _change_tmp = 1;
	int pcm_frame_len = 320;

	buff = (unsigned char *)malloc(1024*2);
	Helios_pcm_outbuf = (unsigned short *)malloc(PCM_PINGPANG_BUF_LEN*2);
	if(!buff || !Helios_pcm_outbuf){
		if(buff) free(buff);
		if(Helios_pcm_outbuf) free(Helios_pcm_outbuf);
		AUDLOGE("malloc fail\n");
		return -1;
	}

	if (aud_play_cb)
	{
#ifndef USR_HELIOS_AUDIO
		aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_START);
#endif
	}
	Helios_Semaphore_Acquire(Helios_ring_buf->rcond, HELIOS_WAIT_FOREVER);
	while (1)
	{
		ret = get_mp3_frame(buff, &bytesRead);
		AUDLOGD("get_mp3_frame ret[%d]\n", ret);
		if (ret != 0)
		{
			// break;
			AUDLOGE("get_mp3_frame ret[%d][%d]\n", ret,_change_tmp);
			Helios_msleep(20);
			_change_tmp ^= 1;
			if(_change_tmp == 0){
				continue;
			}
			
			if (STREAM_IsNot_SwitchOff_Actively)
			{
				memset(buff, 0x00, 2 * 1024);
				bytesRead = 2 * 1024;
				if(_header_buf_pre[0] == 0xff){
					memcpy(buff, _header_buf_pre, 4);
					bytesRead = mp3_frame_size;
				} else {
					buff[0] = 0xff;
					buff[1] = 0xfb;
					buff[2] = 0x70;
					buff[3] = 0xC0;
					bytesRead = 313;
				}
				AUDLOGD("header[%x][%x][%x][%x] size[%d]\n", buff[0],buff[1],buff[2],buff[3], mp3_frame_size);

				if (nodataStartCnt == 1)
				{
					is_resume = 1;
					if (aud_play_cb)
					{
						AUDLOGD("HELIOS_AUD_PLAYER_NODATA_STRAT\n");
						aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_NODATA_STRAT);
					}
				}
				if (nodataStartCnt > 0)
					nodataStartCnt--;
			}
			else
			{
				break;
			}
			is_mutex = 1;
		}
		else
		{
			Helios_config.samplerate = mp3_sample_rate;
			Helios_config.channels = mp3_num_channels;
			nodataStartCnt = 5;
			_change_tmp = 1;

			if (is_resume == 1)
			{
				is_resume = 0;
				if (aud_play_cb)
				{
					AUDLOGD("HELIOS_AUD_PLAYER_DATA_RESUME\n");
					aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_DATA_RESUME);
				}
			}
			is_mutex = 0;
		}

		Helios_Semaphore_Poll(Helios_ring_buf->wcond, &ret);
		if (ret == 0)
		{
			Helios_Semaphore_Release(Helios_ring_buf->wcond);
		}

		if(__jump_cnt != -1 && __jump_count != 0 && mp3_frame_size >= 4 && isSilenceFrame(buff+4, (mp3_frame_size-4)/2)){
			is_silence = 1;
		} else {
			is_silence = 0;
		}
		
		length = ql_decode_start(buff, Helios_pcm_outbuf, bytesRead);
		
		AUDLOGD("Helios_decode_start decode output length = %d\n", length);
		if (length <= 0)
		{
			AUDLOGE("Helios_decode_start failed length = %d\n", length);
			Helios_Semaphore_Poll(Helios_ring_buf->wcond, &ret);
			if (ret == 0)
			{
				Helios_Semaphore_Release(Helios_ring_buf->wcond);
			}

			continue;
		}

		if(is_silence && is_mutex == 0) {
			AUDLOGD("jis_silence\n");
			continue;
		} 

		if(__jump_cnt > 0){
			AUDLOGD("jump cnt = %d\n", __jump_cnt);
			__jump_cnt --;
			continue;
		}
		
		// 使用ringbuffer 缓存，保证pcm底层数据发送是完整一帧(320 or 640 or other)
		pcm_frame_len = mp3_sample_rate * sizeof(int16_t) * 20 / 1000;
		Helios_rb_write(pcm_ring_buf, Helios_pcm_outbuf, length);
		length = Helios_rb_data_space(pcm_ring_buf);
		AUDLOGD("pcm_ring_buf data len:%d !!!\n", length);
		length = length / pcm_frame_len * pcm_frame_len;
		length = Helios_rb_read(pcm_ring_buf, Helios_pcm_outbuf, length);
		AUDLOGD("Helios_rb_read len : %d\n", length);

		rc = Helios_play_stream_start(Helios_config.samplerate, Helios_config.channels, Helios_pcm_outbuf, length);
		
		if (rc != 0)
		{
			AUDLOGE("Helios_play_stream_start rc[%d]\n", rc);
			ret = -1;
			break;
		}
		Helios_wait();
		if (Helios_aud_stop_play_flag)
		{
#if defined(PLAT_Unisoc_8850) || defined(PLAT_Unisoc_8850_R02)
			Helios_PCM_buffer_Reset(mp3_pcm_whdl);
#endif

#if defined(PLAT_ASR_1609) || defined(PLAT_ASR_1606) || defined(PLAT_ASR_1602)
			Helios_PCM_Close(mp3_pcm_whdl); // pcm close提前调用，此时底层pcm有缓存数据，停止播音会进行缓降，降低pop音。
			mp3_pcm_whdl = NULL;
#endif
			ret = -1;
			break;
		}
	}

	if (mp3_pcm_whdl)
	{
#ifdef CONFIG_AUDIO_PWM
		Helios_Aud_PWM_Close(mp3_pcm_whdl);
		mp3_pcm_whdl = NULL;
#else
#ifndef USR_HELIOS_AUDIO
		Helios_PCM_Close(mp3_pcm_whdl);
		mp3_pcm_whdl = NULL;
#endif
#if defined(PLAT_Unisoc_8850) || defined(PLAT_Unisoc_8850_R02)
		void Helios_aud_data_done(void);
		Helios_aud_data_done();
#endif
#endif
		__jump_cnt = -1;
	}

	if (0 == rc)
	{
		if (aud_play_cb)
		{
#ifndef USR_HELIOS_AUDIO
			aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_FINISHED);
#endif
		}
	}

	free(buff);
	free(Helios_pcm_outbuf);
	// add by elian.wang @202240516 
	// 增加清理mp3解码缓存，解决当音频文件未播放完成stop，再次播放mp3文件会解码出错误pcm数据
	ql_release();

	if (ret == -1)
	{
		Helios_Semaphore_Poll(Helios_mp3_stop_sema, &ret);
		if (ret == 0)
		{
			Helios_Semaphore_Release(Helios_mp3_stop_sema);
		}
	}
	Helios_aud_stop_play_flag = 1;
	Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
	Helios_rb_init(Helios_ring_buf);
	Helios_rb_init(pcm_ring_buf);
	Helios_Mutex_Unlock(Helios_mp3_read_mutex);
	return ret;
}

int Helios_mp3_stream_play(void)
{
	int status;
	int ret = -1;
	int count = 0;

	while (1)
	{
		is_mp3_playing = 0;
		status = Helios_Semaphore_Acquire(Helios_mp3_Sema, HELIOS_WAIT_FOREVER);

		Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
		Helios_rb_init(Helios_ring_buf);
		Helios_Mutex_Unlock(Helios_mp3_read_mutex);

		if (STREAM_IsNot_SwitchOff_Actively)
		{
			Stream_first_frame_skip = 1;
		}

		is_mp3_playing = 1;
		ret = Helios_mp3_dec();
		if (ret != 0)
		{
			AUDLOGE("Helios_mp3_dec failed\n");
			// aud_cb_t(NULL, 0, HELIOS_AUD_PLAYER_ERROR);
		}
	}

	ql_release();
	Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
	if (Helios_ring_buf)
	{
		free(Helios_ring_buf);
		Helios_ring_buf = NULL;
	}
	Helios_Mutex_Unlock(Helios_mp3_read_mutex);

	if (pcm_ring_buf)
	{
		free(pcm_ring_buf);
		pcm_ring_buf = NULL;
	}
	return 0;
}

void Helios_mp3_stream_dec_task_init(void)
{
	static int inited = 0;
	size_t mp3_stackSize = 1024 * 8;
#if defined(PLAT_EIGEN) || defined(PLAT_EIGEN_718)
	int mp3_thread_priority = 95;
#else
	int mp3_thread_priority = 75;
#endif


	if (!inited)
	{
		dec_pcm_flag_Ref = Helios_Flag_Create();
		assert(dec_pcm_flag_Ref != 0);

		Helios_ThreadAttr attr = {0};
		attr.name = "Helios_mp3_stream_play";
		attr.stack_size = mp3_stackSize;
		attr.priority = mp3_thread_priority;
		attr.entry = Helios_mp3_stream_play;

		mp3_Decoder = Helios_Thread_Create(&attr);
		assert(mp3_Decoder != 0);

		inited = 1;

		Helios_msleep(200);
	}
}

#define MP3_RINGBUFF_LEN (6*1024)

int Helios_play_mp3_stream_start(void *mp3_buff, int mp3_size)
{
	unsigned char *mp3_buffer_rev = (unsigned char *)mp3_buff;
	int status = 0;
	int i = 0, ret = 0;
	int tmp_len = 0;
	int tatol_size = 0;
	int left_size = mp3_size;
	int stream_start = 0;
	int _count = 100;

	AUDLOGD("stream mp3 size[%d]\n", mp3_size);
#ifdef USR_HELIOS_AUDIO	
	while(_audio_play_state == 1 && mp3_pcm_whdl == NULL && _count-- > 1){
		AUDLOGE("audio is playing, waiting[%d]...\n",_count);
		Helios_msleep(20);
	}
#endif
	if (Helios_ring_buf == NULL)
	{
		Helios_config.len_size = MP3_RINGBUFF_LEN;
		Helios_ring_buf = malloc(sizeof(Helios_ring_buf_t) + (Helios_config.len_size));
		if(!Helios_ring_buf) {
			AUDLOGE("malloc[%d] fail\n", sizeof(Helios_ring_buf_t) + (Helios_config.len_size));
			return -1;
		}
		Helios_rb_init(Helios_ring_buf);
		Helios_ring_buf->capacity = (Helios_config.len_size);
		Helios_ring_buf->wcond = Helios_Semaphore_Create(1, 0);
		assert(Helios_ring_buf->wcond != 0);
		Helios_ring_buf->rcond = Helios_Semaphore_Create(1, 0);
		assert(Helios_ring_buf->rcond != 0);
	}

	if (pcm_ring_buf == NULL) {
		pcm_ring_buf = malloc(sizeof(Helios_ring_buf_t) + PCM_PINGPANG_BUF_LEN*4);
		if (!pcm_ring_buf) {
			AUDLOGE("malloc pcm_ring_buf failed !!!\n");
			return -1;
		}
		Helios_rb_init(pcm_ring_buf);
		pcm_ring_buf->capacity = PCM_PINGPANG_BUF_LEN*4;
	}	

	if (Helios_mp3_read_mutex == 0)
	{
		Helios_mp3_read_mutex = Helios_Mutex_Create();
		assert(Helios_mp3_read_mutex != 0);
	}

	if (mp3_Decoder == 0)
	{
		Helios_mp3_Sema = Helios_Semaphore_Create(1, 0);
		assert(Helios_mp3_Sema != 0);
		Helios_mp3_stop_sema = Helios_Semaphore_Create(1, 0);
		assert(Helios_mp3_stop_sema != 0);

		Helios_mp3_stream_dec_task_init();
	}
	if (is_mp3_playing == 0)
	{
		Helios_Semaphore_Poll(Helios_ring_buf->rcond, &ret);
		if (ret == 1)
		{
			Helios_Semaphore_Acquire(Helios_ring_buf->rcond, 100);
		}
		Helios_Semaphore_Poll(Helios_mp3_Sema, &ret);
		if (ret == 0)
		{
			Helios_Semaphore_Release(Helios_mp3_Sema);
		}
		Helios_msleep(20);
	}

	if (Helios_aud_stop_play_flag == 1)
	{
		Helios_aud_stop_play_flag = 0;
	}
	
	AUDLOGD("stream start mp3_buff:%x %x %x %x %x %x %x %x\n", mp3_buffer_rev[0], mp3_buffer_rev[1], mp3_buffer_rev[2], mp3_buffer_rev[3], mp3_buffer_rev[4], mp3_buffer_rev[5], mp3_buffer_rev[6], mp3_buffer_rev[7]);
	while (tatol_size != mp3_size && Helios_aud_stop_play_flag == 0)
	{
		tmp_len = Helios_rb_free_space(Helios_ring_buf);
		AUDLOGD("rb free size[%d]\n", tmp_len);
		if (tmp_len > 0)
		{
			Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
			AUDLOGD("stream tmp_len[%d], tatol_size[%d], left_size[%d]\n", tmp_len, tatol_size, left_size);
			tmp_len = Helios_rb_write(Helios_ring_buf, mp3_buffer_rev + tatol_size, left_size);

			AUDLOGD("Helios_rb_write len[%d]\n", tmp_len);
			Helios_Mutex_Unlock(Helios_mp3_read_mutex);
			if (tmp_len == 0)
			{
				if (aud_play_cb)
				{
					AUDLOGD("HELIOS_AUD_PLAYER_MOREDATA\n");
					aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_MOREDATA);
				}
			}

			tatol_size += tmp_len;
			left_size -= tmp_len;
			if (stream_start == 0)
			{
				Helios_Semaphore_Poll(Helios_ring_buf->rcond, &ret);
				if (ret == 0)
				{
					Helios_Semaphore_Release(Helios_ring_buf->rcond);
				}
				stream_start = 1;
			}
		}
		else
		{
			status = Helios_Semaphore_Acquire(Helios_ring_buf->wcond, 100);
			////assert(status == 0);
		}
	}

	return 0;
}
int Helios_play_mp3_stream_stop()
{
	int ret;

	AUDLOGD("mp3 stream paly stop\n");

	if (Helios_aud_stop_play_flag == 1)
	{
		return 0;
	}

	Helios_aud_stop_play_flag = 1;
	//	Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
	Helios_Semaphore_Poll(Helios_ring_buf->wcond, &ret);
	if (ret == 0)
	{
		Helios_Semaphore_Release(Helios_ring_buf->wcond);
	}
	Helios_Semaphore_Poll(Helios_ring_buf->rcond, &ret);
	if (ret == 0)
	{
		Helios_Semaphore_Release(Helios_ring_buf->rcond);
	}

	Helios_Semaphore_Acquire(Helios_mp3_stop_sema, 100);

	Helios_Mutex_Lock(Helios_mp3_read_mutex, HELIOS_WAIT_FOREVER);
	Helios_rb_init(Helios_ring_buf);
	Helios_Mutex_Unlock(Helios_mp3_read_mutex);


	return 0;
}
int Helios_play_mp3_stream_drain()
{
	unsigned char tmp[100] = {0};
	unsigned char i = 0;
	for (i = 0; i < 100; i++)
	{
		Helios_play_mp3_stream_start(tmp, sizeof(tmp));
	}
	return 0;
}

unsigned int Helios_source_readat(HeliosAudFILE *fp, unsigned int offset, void *data, unsigned int size)
{
	int retVal = 0;
	retVal = Helios_Aud_fseek(fp, offset, SEEK_SET);

	if (retVal < 0)
	{
		AUDLOGE("%s, ERROR here %ld %ld", __FUNCTION__, retVal, EXIT_SUCCESS);
		return 0;
	}
	else
	{
		retVal = Helios_Aud_fread(data, size, 1, fp);
	}

	AUDLOGD("%s, read size %ld\n", __FUNCTION__, retVal);

	return retVal;
}

static bool mp3_resync(
	HeliosAudFILE *fp, uint32_t match_header,
	uint32_t *inout_pos, uint32_t *out_header)
{

	uint32_t pos = *inout_pos;
	bool valid = false;

	const int32_t kMaxReadBytes = 1024;
	const int32_t kMaxBytesChecked = 128 * 1024;
	uint8_t buf[kMaxReadBytes];
	int bytesToRead = kMaxReadBytes;
	int totalBytesRead = 0;
	int remainingBytes = 0;
	bool reachEOS = false;
	uint8_t *tmp = buf;
	int j = 0;
	uint32_t kMask = 0xfffe0c00;

	do
	{
		if (pos >= *inout_pos + kMaxBytesChecked)
		{
			// Don't scan forever.
			break;
		}

		if (remainingBytes < 4)
		{
			if (reachEOS)
			{
				break;
			}
			else
			{
				memcpy(buf, tmp, remainingBytes);
				bytesToRead = kMaxReadBytes - remainingBytes;

				/*
				 * The next read position should start from the end of
				 * the last buffer, and thus should include the remaining
				 * bytes in the buffer.
				 */
				totalBytesRead = Helios_source_readat(fp, pos + remainingBytes,
													  buf + remainingBytes, bytesToRead);

				if (totalBytesRead <= 0)
				{
					break;
				}
				reachEOS = (totalBytesRead != bytesToRead);
				remainingBytes += totalBytesRead;
				tmp = buf;
				continue;
			}
		}

		uint32_t header = U32_AT(tmp);

		if (match_header != 0 && (header & kMask) != (match_header & kMask))
		{
			++pos;
			++tmp;
			--remainingBytes;
			continue;
		}

		size_t frame_size;
		uint32_t sample_rate, num_channels, bitrate;
		if (!parseHeader(
				header, &frame_size,
				&sample_rate, &num_channels, &bitrate, NULL))
		{
			++pos;
			++tmp;
			--remainingBytes;
			continue;
		}

		// We found what looks like a valid frame,
		// now find its successors.

		uint32_t test_pos = pos + frame_size;

		valid = true;
		const int FRAME_MATCH_REQUIRED = 3;
		for (j = 0; j < FRAME_MATCH_REQUIRED; ++j)
		{
			uint8_t tmp[4];
			int retval = Helios_source_readat(fp, test_pos, tmp, sizeof(tmp));
			if (retval < (int)sizeof(tmp))
			{
				valid = false;
				break;
			}

			uint32_t test_header = U32_AT(tmp);

			if ((test_header & kMask) != (header & kMask))
			{
				valid = false;
				break;
			}

			size_t test_frame_size;
			if (!parseHeader(test_header, &test_frame_size, NULL, NULL, NULL, NULL))
			{
				valid = false;
				break;
			}

			test_pos += test_frame_size;
		}

		if (valid)
		{
			*inout_pos = pos;

			if (out_header != NULL)
			{
				*out_header = header;
			}
		}

		++pos;
		++tmp;
		--remainingBytes;
	} while (!valid);

	return valid;
}
// int Helios_mp3_file_start(HeliosAudFILE *fd, int read_size)

void Helios_mp3_file_jump_frame(uint32_t cnt) {
	__jump_count = cnt;
}

int Helios_mp3_file_start(char *file_name, cb_on_player aud_cb)
{
	int ret = 0;
	unsigned char *mp3_buf = NULL;
	int i = 0;
	int frame_count = 0;
	uint32_t offset = 0;
	uint32_t frame_length, mp3_sample_rate1, mp3_num_channels1;

	if (!helios_find_file(file_name))
	{
		AUDLOGE("file[%s] is not find!\n", file_name);
		return -1;
	}

	if (aud_cb)
	{
		aud_play_cb = aud_cb;
	}
#ifdef USR_HELIOS_AUDIO
	Helios_aud_stop_play_flag = 0;
#endif

	HeliosAudFILE *fd = Helios_Aud_fopen(file_name, "r");
	if (fd == NULL)
	{
		AUDLOGE("file[%s] open failed\n", file_name);
		return -1;
	}

	// AUDLOGI("Helios_mp3_file_start entern\n");
	mp3_buf = (unsigned char *)malloc(10 * 1024);
	{
		// Sync to the first valid frame.
		uint32_t header1 = 0;
		uint32_t pos = 0;
		bool success = mp3_resync(fd, 0 /*match_header*/, &pos, &header1);
		if (success == false)
		{
			// Helios_fclose(fd);
			AUDLOGE("Sync to the first valid frame error\n");
			if (aud_play_cb)
			{
				AUDLOGD("HELIOS_AUD_PLAYER_ERROR\n");
				aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_ERROR);
			}
			free(mp3_buf);
			Helios_Aud_fclose(fd);
			return -1;
		}
		AUDLOGD("Helios_mp3_file_start, Sync to the first valid frame pos:%d\n", pos);
		ret = Helios_Aud_fseek(fd, pos, SEEK_SET);
		if (ret < 0)
		{
			// Helios_fclose(fd);
			AUDLOGE("fs seek failed\n");
			if (aud_play_cb)
			{
				AUDLOGD("HELIOS_AUD_PLAYER_ERROR\n");
				aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_ERROR);
			}
			free(mp3_buf);
			Helios_Aud_fclose(fd);
			return -1;
		}
		uint8_t header[4];
		uint32_t header_header;
		while (Helios_Aud_fread(header, 1, 4, fd) == 4) {
			AUDLOGD("header[%x][%x][%x][%x]\n", header[0],header[1],header[2],header[3]);
			header_header = U32_AT(header);
			if (parseHeader(header_header, &frame_length, &mp3_sample_rate1, &mp3_num_channels1, 0, 0))
			{
				AUDLOGD("start2 [%d] [%d] [%d]\n",frame_length, mp3_sample_rate1, mp3_num_channels1);
			} else {
				AUDLOGE("jump fail\n");
				break;
			}

	        //int frame_length = get_mp3_frame_length(header);
	        AUDLOGD("start2 frame_length[%d]\n",frame_length);

	        // Invalid frame or EOF
	        if (frame_length <= 0) {
	            break;
	        }

			offset += frame_length;

	        if (frame_count == __jump_count) {
	            break;
	        }
	        Helios_Aud_fseek(fd, frame_length - 4, SEEK_CUR); // 跳过帧数�?
	        Helios_Aud_fseek(fd, frame_length - 4, SEEK_CUR); // 跳过帧数�?
	        Helios_Aud_fseek(fd, frame_length - 4, SEEK_CUR); // 跳过帧数�??
	        frame_count++;
	    }
		AUDLOGD("jump size[%d],pos[%d],offset[%d]\n",pos + offset, pos, offset);
		ret = Helios_Aud_fseek(fd, pos+offset, SEEK_SET);
		if (ret < 0)
		{
			// Helios_fclose(fd);
			AUDLOGE("fs seek failed\n");
			if (aud_play_cb)
			{
				AUDLOGD("HELIOS_AUD_PLAYER_ERROR\n");
				aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_ERROR);
			}
			free(mp3_buf);
			Helios_Aud_fclose(fd);
			return -1;
		}

	}

	while (1)
	{
		// memset(mp3_buf, 0, read_size);
#ifdef USR_HELIOS_AUDIO
		if(Helios_aud_stop_play_flag) {
			break;
		}
#endif

		ret = Helios_Aud_fread(mp3_buf, 10 * 1024, 1, fd);
		if (ret <= 0)
		{
			AUDLOGE("fread read ret = %d\n", ret);
			break;
		}

		Helios_play_mp3_stream_start(mp3_buf, ret);
		AUDLOGD("play mp3 stream ret = %d\n", ret);
		// i += read_size;
	}
	// AUDLOGI("Helios_mp3_file_start end\n");
	free(mp3_buf);
	Helios_Aud_fclose(fd);
	// Helios_play_mp3_stream_drain();
	// Helios_play_mp3_stream_stop();

	return 0;
}

int Helios_mp3_file_stop()
{
	AUDLOGD("Helios_mp3_file_stop");

	int count = 0;
#if defined(PLAT_ASR_1803s) || defined(PLAT_ASR_1803sc)
	Helios_aud_stop_play_flag = 1;

	if(mp3_pcm_whdl) {
		AUDLOGE("Helios_mp3_file_stop\n");
		Helios_PCM_Close_Force(mp3_pcm_whdl);
		mp3_pcm_whdl = NULL;
	}
#endif
	
	if (!is_mp3_playing)
	{
		AUDLOGE("error, mp3 is not playing\n");
		return -1;
	}
#ifdef USR_HELIOS_AUDIO
	Helios_aud_stop_play_flag = 1;
#endif

	Helios_Flag_Release(dec_pcm_flag_Ref, Helios_PCM_STOP, Helios_FLAG_OR);
	while (is_mp3_playing && count < 100)
	{
		Helios_msleep(15);
		count++;
	}
#ifdef USR_HELIOS_AUDIO
	Helios_mp3_Clear_Cache();
#endif
	AUDLOGD("mp3Stop finish");
	return 0;
}

int helios_mp3_set_callback(cb_on_player aud_cb)
{
	aud_play_cb = aud_cb;
	return 0;
}

void Helios_mp3_stream_close_flag(unsigned char value) {
	STREAM_IsNot_SwitchOff_Actively = value;
}



struct mp3_file_list {
	int file_size;
	char *file_name;
	struct mp3_file_list *next;
};
static void mp3_file_list_push(struct mp3_file_list *head, struct mp3_file_list *item) {
	struct mp3_file_list *p = NULL;
	if (head == NULL) {
		return;
	}
	p = head;
	while (p->next != NULL) {
		p = p->next;
	}

	p->next = item;
}

static struct mp3_file_list * mp3_file_list_pop(struct mp3_file_list *head) {
	struct mp3_file_list *p = NULL;
	if (head == NULL) {
		return NULL;
	}

	if (head->next) {
		p = head->next;
		head->next = p->next;
		return p;
	} else {
		return NULL;
	}
}
 
 #define AUDIO_MP3_FREE(X) do{		\
	if(X) {							\
		free(X);					\
		X = NULL;					\
	}								\
 }while(0);

int helios_audio_mp3_play_morefiles(char *files, cb_on_player cb)
{
	struct mp3_file_list list_head = {.next = NULL};
	char *path_dir = NULL;
	char *p = NULL;
	int total_files_size = 0;
	int data_size = 0;
	char *data = NULL;
	char *files_tmp = NULL;
	char file_type = 0;
	int ret = 0;

	AUDLOGD("files[%s]\n", files);

	if (strncmp(files, "mp3files=", 9) == 0)
	{
		file_type = 1;

	}
	else
	{
		/* not continue play */
		ret = PLAY_MOREFILES_ERR_SINGLEFILE;
		goto error;
	}

	files_tmp = malloc(strlen(files)+1);
	memset(files_tmp, 0, strlen(files)+1);
	strcpy(files_tmp, files);

	/* parse files info */
	path_dir = strtok(files_tmp + 9, "+");
	if (path_dir != NULL) {
		p = strtok(NULL, "+");
	} else {
		AUDIO_MP3_FREE(files_tmp);
		ret = PLAY_MOREFILES_ERR_FILENOTEXIST;
		goto error;
	}
    int file_cnt =0;
	while (p != NULL) {
		struct mp3_file_list *item = NULL;
		char file_path[128] = {0};
		snprintf(file_path, sizeof(file_path), "%s/%s", path_dir, p);
		AUDLOGD("%s, push item file = %s\n", __func__, file_path);
		if (helios_find_file(file_path) == 1) {
			item = malloc(sizeof(struct mp3_file_list));
			item->file_size = 0;
			item->file_name = p;
			item->next = NULL;
			mp3_file_list_push(&list_head, item);
			file_cnt++;
			if(1 == file_cnt) {
			    strncpy(files, file_path, strlen(file_path)+1);
			}
		} else {
		    while (list_head.next != NULL) {
		        struct mp3_file_list *item = mp3_file_list_pop(&list_head);
		        if(item) {
		            AUDIO_MP3_FREE(item);
		        }
		    }
		    AUDIO_MP3_FREE(files_tmp);
			ret = PLAY_MOREFILES_ERR_FILENOTEXIST;
			goto error;
		}

		p = strtok(NULL, "+");
	}

	if(1 == file_cnt) {
	    while (list_head.next != NULL) {
	        struct mp3_file_list *item = mp3_file_list_pop(&list_head);
	        if(item) {
	            AUDIO_MP3_FREE(item);
	        }
	    }
	    AUDIO_MP3_FREE(files_tmp);
		ret = PLAY_MOREFILES_ERR_SINGLEFILE;
		goto error;
	}

	/* play files in once */
	while (list_head.next != NULL) {
		char file_path[128] = {0};
		struct mp3_file_list *item = mp3_file_list_pop(&list_head);

		if (item) {
			snprintf(file_path, sizeof(file_path), "%s/%s", path_dir, item->file_name);
			AUDLOGD("%s, pop item file = %s\n", __func__, file_path);
			if(Helios_mp3_file_start(file_path, cb) != 0) {
				ret = PLAY_MOREFILES_ERR_PLAY;
				goto error;
			}
			AUDIO_MP3_FREE(item);
		}
	}

	ret = PLAY_MOREFILES_ERR_OK;
error:
	while (list_head.next != NULL){
		struct mp3_file_list *item = mp3_file_list_pop(&list_head);
		if(item){
			AUDIO_MP3_FREE(item);
		}
	}
	AUDIO_MP3_FREE(data);
	AUDIO_MP3_FREE(files_tmp);

	if(cb && ret != PLAY_MOREFILES_ERR_OK) {
		cb(NULL, 0, HELIOS_AUD_PLAYER_ERROR);
	}
	return ret;
}
