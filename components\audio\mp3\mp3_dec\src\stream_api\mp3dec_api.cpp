/*
 * Copyright (C) 2014 The Android Open Source Project
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *  * Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *  * Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

#include <stdlib.h>
#include <stdio.h>
#include <assert.h>
#include <string.h>
#include "gbl_types.h"
#include "pvmp3decoder_api.h"
#include "mp3reader.h"
#include "helios_include.h"
#include "osa_old_api.h"
#include "mediaPlayer.h"
#include "mp3dec_api.h"
#include "mp3dec_config.h"
#include "audio_def.h"

extern "C" {
    extern unsigned int  FAT_fopen(const char *filename_ptr, const char *mode);
    extern size_t FAT_fread(void *buffer_ptr, size_t element_size, size_t count, unsigned int stream);
    extern int FAT_fseek(unsigned int stream, long offset, int wherefrom);
    extern int FAT_fclose(unsigned int stream);
}

#if MP3_STREAM_SUPPORT
//MSL message type
#include "msl_sal_stbc_type.h"
#include "msl_mem.h"
#endif

extern "C" {
#if (MP3_DEC_CALCULATE_TIME) || (FLASH_CACHE_WRITE_CALCULATE_TIME) || (FLASH_CACHE_READ_CALCULATE_TIME)
#include "helios_include.h"
    //Timer header files, used to calculate processing time
#include "timer.h"
#include "timer_hw.h"
#endif
}

enum {
    kInputBufferSize = 10 * 1024,
    kOutputBufferSize = 4608 * 2,
};

enum {
    MP3PLAY_ACTIVE_MODE_NONE = 0,
    MP3PLAY_ACTIVE_MODE_FILE,
    MP3PLAY_ACTIVE_MODE_STREAM,
    MP3PLAY_ACTIVE_MODE_STREAM_ENDING,
};

enum {
    MP3PLAY_CACHE_MODE_NONE = 0,
    MP3PLAY_CACHE_MODE_RAM,
    MP3PLAY_CACHE_MODE_FLASH,
};

enum {
    MP3PLAY_PAUSED_NONE = 0x0,
    MP3PLAY_PAUSED_FROM_USER = 0x1,
    MP3PLAY_PAUSED_FROM_AUTO = 0x2,
};

extern "C"
{
    static OSAFlagRef   dec_pcm_flag_Ref;
    static OSATaskRef 	mp3_Decoder = NULL;
    static void* 		mp3_stack_ptr = NULL;
    static char			mp3_file_name[256];

    uint8_t	is_mp3_playing = MP3PLAY_ACTIVE_MODE_NONE;
    static uint8_t	is_mp3_paused = MP3PLAY_PAUSED_NONE;

    static Mp3Reader 				mp3Reader;
    static tPVMP3DecoderExternal 	decoderConfig;
    static unsigned short 			pMp3_OutputBuf[kOutputBufferSize / 2];
    static void*					decoderBuf = NULL;
    static uint8_t *				inputBuf = NULL;
    static uint32_t				    bytesRead = 0;

    static int mp3_user_option = 0;
    static int mp3_user_skipped = 0;
    static void(*mp3_user_file_event_trigger)(Mp3FileEventType, int) = 0;
    static void(*mp3_read_compleletd)(void) = 0;
    static uint32_t mp3_fixed_header = 0;
    static uint32_t mp3_current_bitrate = 0;

#define MP3_DEC_PCM_FETCH				(0x01)
#define MP3_DEC_PCM_STOP				(0x02)
#define MP3_DEC_PCM_START				(0x04)
#define MP3_DEC_PCM_PAUSE				(0x08)
#define MP3_DEC_PCM_DRAIN_END			(0x10)
#define MP3_DEC_PCM_DECODE				(0x20)

#define MP3_STREAM_FRAME_IN				(0x40)
#define MP3_STREAM_FRAME_OUT			(0x80)
#define MP3_STREAM_CLEANUP				(0x100)
#define MP3_DEC_PCM_TASK_MASK       (MP3_DEC_PCM_START | MP3_DEC_PCM_STOP | MP3_DEC_PCM_FETCH | MP3_DEC_PCM_PAUSE | MP3_DEC_PCM_DRAIN_END | MP3_DEC_PCM_DECODE | MP3_STREAM_FRAME_IN | MP3_STREAM_FRAME_OUT | MP3_STREAM_CLEANUP)
    
    typedef void(*MP3_EVENT_CB)(int);
    static MP3_EVENT_CB mp3_callback = NULL;
    static void notify_callback(int event)
    {
        if (mp3_callback) {
            //DIAG_FILTER(AUDIO, MP3_DEC, notify_callback_before, DIAG_INFORMATION)
            AUDLOGI("notify_callback_before,  event:%d", event);
            mp3_callback(event);
            //DIAG_FILTER(AUDIO, MP3_DEC, notify_callback_after, DIAG_INFORMATION)
            AUDLOGI("notify_callback_after");
            mp3_callback = 0;
        }
    }

    static void on_trigger_file(int status) {
        //DIAG_FILTER(AUDIO, MP3_DEC, on_trigger_file, DIAG_INFORMATION)
        AUDLOGI("is_mp3_playing:%d, status:%d!", is_mp3_playing, status);
        if (status == media_play_event_underrun) {
            OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_FETCH, OSA_FLAG_OR);
        }

        if (status == media_play_event_drain_end) {
            OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_DRAIN_END, OSA_FLAG_OR);
        }

        if (status == media_play_event_end) {
            if (mp3_user_file_event_trigger)
                mp3_user_file_event_trigger(MP3_FILE_EVENT_STATUS, MP3_FILE_STATUS_ENDED);
            notify_callback(0);
        }
    }

    static int mp3_dec_deinit(bool force) {
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3Reader_deinit, DIAG_INFORMATION)
        AUDLOGI("is_mp3_playing:%d, force:%d", is_mp3_playing, force);
        if (is_mp3_playing) {
            // Close input reader and output writer.
            if (is_mp3_playing == MP3PLAY_ACTIVE_MODE_FILE)
                mp3Reader.close();

            // Free allocated memory.
            if (inputBuf)
                free(inputBuf);
            if (decoderBuf)
                free(decoderBuf);

            inputBuf = NULL;
            decoderBuf = NULL;
            if (!force && is_mp3_playing == MP3PLAY_ACTIVE_MODE_FILE && (mp3_user_option & MEDIA_PLAY_MASK_CYCLIC)) {
                OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_START, OSA_FLAG_OR);
            }
            else {
                is_mp3_playing = MP3PLAY_ACTIVE_MODE_NONE;
                mp3_user_option = 0;
                mp3_user_skipped = 0;
                on_media_play_stop();
            }
        }

        return 0;
    }

    static void update_file_config(void) {
        if (mp3_user_file_event_trigger) {
            uint32_t thisHeader = mp3Reader.getCurrentHeader();
            if (mp3_fixed_header != thisHeader) {
                uint32_t this_rate, this_ch, this_bitrate, this_samples;
                size_t this_frame_size;
                bool pasred = parseHeader(thisHeader, &this_frame_size, &this_rate, &this_ch, &this_bitrate, &this_samples);
                if (pasred) {
                    // report sample rate and channel once
                    if (mp3_fixed_header == 0) {
                        mp3_user_file_event_trigger(MP3_FILE_EVENT_SAMPLERATE, (int)this_rate);
                        mp3_user_file_event_trigger(MP3_FILE_EVENT_CHANNAL, (int)this_ch);
                    }
                    if (mp3_current_bitrate != this_bitrate) {
                        mp3_user_file_event_trigger(MP3_FILE_EVENT_BITRATE, (int)this_bitrate);
                        mp3_current_bitrate = this_bitrate;
                    }
                    mp3_fixed_header = thisHeader;// reuse fixed header as last header
                }
            }
        }
    }

    static int mp3_dec_decode(bool update);
    static int mp3_dec_init(const char* name, Mp3Reader* reader, tPVMP3DecoderExternal* config) {
        bool 					success = false;
        uint32_t 				memRequirements = 0;
        uint32_t				sampleRate = 0;
        uint32_t				numChannel = 0;

        //DIAG_FILTER(AUDIO, MP3_DEC, mp3_dec_init_enter, DIAG_INFORMATION)
        AUDLOGI("%s, file name %s", __FUNCTION__, name);

        if (!reader || !config)
            return -1;

        config->equalizerType = flat;
        config->crcEnabled = false;

        // Allocate the decoder memory.
        memRequirements = pvmp3_decoderMemRequirements();
        decoderBuf = malloc(memRequirements);
        ASSERT(decoderBuf != NULL);

        // Initialize the decoder.
        pvmp3_InitDecoder(config, decoderBuf);

        // Open the input file.
        success = reader->init(name, true);
        if (!success) {
            return -2;
        }
        else {
            if (mp3_user_file_event_trigger) {
                uint32_t offset = reader->getCurrentOffset();
                mp3_user_file_event_trigger(MP3_FILE_EVENT_ID3_OFFSET, (int)offset);
                update_file_config();
                //DIAG_FILTER(AUDIO, MP3_DEC, mp3Reader_init_info, DIAG_INFORMATION)
                AUDLOGI("header:0x%lx, offset:%ld", mp3_fixed_header, offset);
            }
        }
        // Allocate input buffer.
        inputBuf = static_cast<uint8_t*>(malloc(kInputBufferSize));
        ASSERT(inputBuf != NULL);

        sampleRate = reader->getSampleRate();
        numChannel = reader->getNumChannels();
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3Reader_init, DIAG_INFORMATION)
        AUDLOGI("mp3Reader.init:%d, sampleRate:%ld, numChannel:%ld", success, sampleRate, numChannel);

        on_media_play_start(sampleRate, numChannel, mp3_user_option, on_trigger_file);
        is_mp3_playing = MP3PLAY_ACTIVE_MODE_FILE;

        int skip_count = mp3_user_skipped;
        while (skip_count > 0) {
            reader->getFrame(inputBuf, &bytesRead);
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3Reader_skip, DIAG_INFORMATION)
            AUDLOGI("skip_count:%ld", skip_count);
            skip_count--;
            mp3_dec_decode(false);
        }
        return 0;
    }

    static int mp3_dec_pause(void) {
        return -1;
    }

    static int mp3_dec_read(void) {
        // Read input from the file.
        bool success = mp3Reader.getFrame(inputBuf, &bytesRead);
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3_dec_read, DIAG_INFORMATION)
        AUDLOGI("mp3_read_compleletd:0x%lx,success:%d,inputBuf:0x%lx,bytesRead:%d", mp3_read_compleletd, success, inputBuf, bytesRead);
        if (!success) {
            //DIAG_FILTER(AUDIO, MP3_DEC, frameReadError, DIAG_INFORMATION)
            AUDLOGI("frameReadError");
            return -1;
        }

        if (mp3_read_compleletd)
            mp3_read_compleletd();
        update_file_config();
        return 0;
    }

    static int mp3_dec_drainstop(void) {
        on_media_play_drainstop();
        return 0;
    }

    static int mp3_dec_decode(bool update) {
        ERROR_CODE 				decoderErr = NO_DECODING_ERROR;
#if MP3_DEC_CALCULATE_TIME
        UINT32 TSBefore, TSAfter, delayInUsec, delayInTick;
        TSBefore = timerCountRead(TS_TIMER_ID);
#endif	
        // Set the input config.
        decoderConfig.inputBufferCurrentLength = bytesRead;
        decoderConfig.inputBufferMaxLength = 0;
        decoderConfig.inputBufferUsedLength = 0;
        decoderConfig.pInputBuffer = inputBuf;
        decoderConfig.pOutputBuffer = (int16_t*)pMp3_OutputBuf;
        decoderConfig.outputFrameSize = kOutputBufferSize / sizeof(int16_t);

        decoderErr = pvmp3_framedecoder(&decoderConfig, decoderBuf);

#if MP3_DEC_CALCULATE_TIME
        TSAfter = timerCountRead(TS_TIMER_ID);

        /* calculate the delay base on the TS reading  */
        delayInTick = (TSAfter >= TSBefore) ? TSAfter - TSBefore : ((0xffffffff - TSBefore) + TSAfter);
        delayInUsec = (timerClockRateGet(TS_TIMER_ID) == FAST_TIMER_CLOCK_RATE_IN_HZ) ?
            CLOCK_TICKS_TO_MICRO_SEC(delayInTick) :
            CLOCK_TICKS_TO_MICRO_SEC_32K(delayInTick);

        //DIAG_FILTER(AUDIO, MP3_DEC, pvmp3_framedecoder, DIAG_INFORMATION)
        AUDLOGI("calculated processing time : %lu uSec, totalNumberOfBitsUsed : %ld", delayInUsec, decoderConfig.totalNumberOfBitsUsed);
#endif	

        if (decoderErr != NO_DECODING_ERROR) {
            //DIAG_FILTER(AUDIO, MP3_DEC, framedecoder_err, DIAG_INFORMATION)
            AUDLOGI("pvmp3_framedecoder %ld", decoderErr);

            //DIAG_FILTER(AUDIO, MP3_DEC, bad_frame, DIAG_INFORMATION)
            diagStructPrintf("mp3_bad_frame", inputBuf, bytesRead);
            //pvmp3_resetDecoder(decoderBuf);
            return -1;
        }

#ifdef SIMULATOR
        //DIAG_FILTER(AUDIO, MP3_DEC, frame_dump, DIAG_INFORMATION)
        diagStructPrintf("dec_mp3_frame", inputBuf, bytesRead);
#endif

        //DIAG_FILTER(AUDIO, MP3_DEC, dec_mp3_dump, DIAG_INFORMATION)
        diagStructPrintf("dec_mp3_dump", pMp3_OutputBuf, decoderConfig.outputFrameSize * sizeof(int16_t));

        if(update)
            on_media_play_update((const int16_t*)pMp3_OutputBuf, decoderConfig.outputFrameSize * sizeof(int16_t));

        return 0;
    }

    static int mp3_dec_fetch(void) {
        if (mp3_dec_read() == 0) {
            mp3_dec_decode(true);
            return 0;
        }

        return -1;
    }

    static int mp3_dec_stream_init(tPVMP3DecoderExternal* config);
    static void on_mp3_stream_serialize(void);
    static void on_mp3_stream_deserialize(void);
    static int on_mp3_stream_fetch(void);
    static void on_mp3_stream_cleanup(void);

    static void mp3_dec_main_loop(void* p)
    {
        unsigned int   			event = 0;
        while (1) {
            OSAFlagWait(dec_pcm_flag_Ref, MP3_DEC_PCM_TASK_MASK, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND);
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3_dec_event, DIAG_INFORMATION)
            AUDLOGI("mp3_dec_event:%x", event);

            if (MP3_DEC_PCM_FETCH & event) {
                if (is_mp3_playing == MP3PLAY_ACTIVE_MODE_FILE) {
                    if (mp3_dec_fetch() != 0)
                        mp3_dec_deinit(false);
                }

#if MP3_STREAM_SUPPORT == 1
                if (is_mp3_playing == MP3PLAY_ACTIVE_MODE_STREAM || is_mp3_playing == MP3PLAY_ACTIVE_MODE_STREAM_ENDING) {
                    if (is_mp3_playing == MP3PLAY_ACTIVE_MODE_STREAM_ENDING) {
                        on_mp3_stream_serialize();
                        on_mp3_stream_deserialize();
                    }
                    on_mp3_stream_fetch();
                }
#endif
            }

            if (MP3_DEC_PCM_DRAIN_END & event) {
                mp3_dec_drainstop();
            }

            if (MP3_DEC_PCM_DECODE & event) {
                mp3_dec_decode(true);
            }

            if (MP3_DEC_PCM_PAUSE & event) {
                mp3_dec_pause();
            }

            if (MP3_DEC_PCM_STOP & event) {
                mp3_dec_deinit(true);
            }

#if MP3_STREAM_SUPPORT == 1	&& MP3PLAY_STREAM_USE_SEPERATE_TASK == 0
            if (MP3_STREAM_FRAME_IN & event) {
                on_mp3_stream_serialize();
            }

            if (MP3_STREAM_FRAME_OUT & event) {
                on_mp3_stream_deserialize();
            }

            if (MP3_STREAM_CLEANUP & event) {
                on_mp3_stream_cleanup();
            }
#endif

            if (MP3_DEC_PCM_START & event) {
                if (is_mp3_playing == MP3PLAY_ACTIVE_MODE_FILE && mp3_dec_init(mp3_file_name, &mp3Reader, &decoderConfig) != 0) {
                    mp3_dec_deinit(true);
                }

#if MP3_STREAM_SUPPORT == 1		
                if (is_mp3_playing == MP3PLAY_ACTIVE_MODE_STREAM && mp3_dec_stream_init(&decoderConfig) != 0) {
                    mp3_dec_deinit(true);
                }
#endif
            }
        }
    }

    static void mp3dec_task_init(void)
    {
        OS_STATUS status;
        size_t mp3_stackSize = 1024 * 8;
        int mp3_thread_priority = 75;
        static bool inited = false;

        if (!inited) {
            status = OSAFlagCreate(&dec_pcm_flag_Ref);
            ASSERT(status == OS_SUCCESS);

            mp3_stack_ptr = malloc(mp3_stackSize);
            ASSERT(mp3_stack_ptr);
            status = OSATaskCreate(&mp3_Decoder, mp3_stack_ptr, mp3_stackSize, mp3_thread_priority, "mp3Play", mp3_dec_main_loop, NULL);
            ASSERT(status == OS_SUCCESS);

            inited = 1;
        }
    }

    static BOOL findFile(const char *name)
    {
        HeliosAudFILE*   fileID;
        //DIAG_FILTER(AUDIO, MP3_DEC, findFile, DIAG_INFORMATION)
        AUDLOGI("name %s", name);

        fileID = Helios_Aud_fopen(name, "rb");
        if (fileID) {
            Helios_Aud_fclose(fileID);
            return TRUE;
        }

#if INTERNAL_CONFIG_SUPPORT_SDCARD_FILE == 1
        UINT32 hdl = 0;
        hdl = FAT_fopen(name, "rb");

        //DIAG_FILTER(AUDIO, MP3_DEC, findFile_sd, DIAG_INFORMATION)
        AUDLOGI("hdl:0x%lx, name:%s", hdl, name);


        if (hdl) {
            FAT_fclose(hdl);
            return TRUE;
        }
#endif
        return FALSE;
    }

    static int mp3StartWithoutCB(const char *file_name)
    {
        if (!file_name) {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3Start_nameNULL, DIAG_INFORMATION)
            AUDLOGI("mp3Start_nameNULL");
            return -1;
        }

        //DIAG_FILTER(AUDIO, MP3_DEC, mp3Start_enter, DIAG_INFORMATION)
        AUDLOGI("mp3Start file_name %s", file_name);

        if (is_mp3_playing) {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3_running, DIAG_INFORMATION)
            AUDLOGI("error, mp3 is in playing4");
            return -2;
        }

        if ((0 == strlen(file_name))
            || (strlen(file_name) > 250)
            || (!findFile(file_name))) {
            //DIAG_FILTER(AUDIO, MP3_DEC, wrong_file_name, DIAG_INFORMATION)
            AUDLOGI("file_name error!!");
            return -1;
        }

        mp3_read_compleletd = 0;
        is_mp3_playing = MP3PLAY_ACTIVE_MODE_FILE;
        is_mp3_paused = MP3PLAY_PAUSED_NONE;
        mp3_fixed_header = 0;
        mp3_current_bitrate = 0;
        memcpy(mp3_file_name, file_name, strlen(file_name) + 1);
        if (mp3_Decoder == NULL) {
            mp3dec_task_init();
        }

        OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_START, OSA_FLAG_OR);
        return 0;
    }

    int mp3Start(const char *file_name) {
        mp3_callback = 0;
        mp3_user_option |= MEDIA_PLAY_MASK_DRAIN;
        return mp3StartWithoutCB(file_name);
    }

    int mp3Pause(void) {
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3Pause, DIAG_INFORMATION)
        AUDLOGI("%s,is_mp3_paused:0x%x", __FUNCTION__, is_mp3_paused);
        if (is_mp3_paused == MP3PLAY_PAUSED_NONE) {
            int16_t paused = on_media_play_suspend();
            if (paused == 1) {
                is_mp3_paused = MP3PLAY_PAUSED_FROM_USER;
                return 0;
            }
        }
        return -1;
    }

    int mp3Resume(void) {
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3Resume, DIAG_INFORMATION)
        AUDLOGI("%s,is_mp3_paused:0x%x", __FUNCTION__, is_mp3_paused);
        if (is_mp3_paused == MP3PLAY_PAUSED_FROM_USER) {
            int16_t paused = on_media_play_resume();
            if (paused == 0) {
                is_mp3_paused = MP3PLAY_PAUSED_NONE;
                return 0;
            }
        }
        return -1;
    }

    int mp3Stop(void)
    {
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3Stop, DIAG_INFORMATION)
        AUDLOGI("mp3Stop");
        if (!is_mp3_playing) {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3_not_running, DIAG_INFORMATION)
            AUDLOGI("error, mp3 is not playing");
            return -1;
        }

        OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_STOP, OSA_FLAG_OR);
        return 0;
    }

    int mp3PlayStart(const Mp3FileConfigInfo* configInfo) {
        ASSERT(configInfo);
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3PlayStart, DIAG_INFORMATION)
        AUDLOGI("option:0x%lx, trigger:0x%lx", configInfo->option, configInfo->trigger);
        mp3_user_file_event_trigger = configInfo->trigger;
        mp3_user_option = configInfo->option;
        mp3_user_skipped = configInfo->skip_frame;
        return mp3StartWithoutCB(configInfo->name);
    }

    int mp3PlayPause(void) {
        return mp3Pause();
    }

    int mp3PlayResume(void) {
        return mp3Resume();
    }

    int mp3PlayStop(int drain) {
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3PlayStop, DIAG_INFORMATION)
        AUDLOGI("drain:%d", drain);
        on_media_play_drain(drain);
        return mp3Stop();
    }

    //ICAT EXPORTED FUNCTION - Audio,Voice,mp3dec_test_sd
    int mp3dec_test_sd(void) {
        int rc = 0;

        rc = mp3StartWithoutCB("d:\\Music\\test.mp3");
        if (rc) {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test_sd_start_fail, DIAG_INFORMATION)
            AUDLOGI("rc:%d", rc);
        }
        else {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test_sd_start_success, DIAG_INFORMATION)
            AUDLOGI("rc:%d", rc);
        }
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,Voice,mp3dec_test
    int mp3dec_test(void) {
        int rc = 0;

        rc = mp3StartWithoutCB("test.mp3");
        if (rc) {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test_start_fail, DIAG_INFORMATION)
            AUDLOGI("rc:%d", rc);
        }
        else {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test_start_success, DIAG_INFORMATION)
            AUDLOGI("rc:%d", rc);
        }
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,Voice,mp3dec_test1
    int mp3dec_test1(void) {
        int rc = 0;
        rc = mp3Start("test.mp3");
        if (rc) {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test_start_fail, DIAG_INFORMATION)
            AUDLOGI("rc:%d", rc);
        }
        else {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test_start_success, DIAG_INFORMATION)
            AUDLOGI("rc:%d", rc);
        }
        return 0;
    }

    static void mp3dec_test2_callback(Mp3FileEventType event, int value) {
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test2_callback, DIAG_INFORMATION)
        AUDLOGI("event:%ld,value:%ld", event, value);
    }

    static int test_option = (0x14);
    //ICAT EXPORTED FUNCTION - Audio,Voice,mp3dec_test2
    int mp3dec_test2(void) {
        int rc = 0;
        Mp3FileConfigInfo config = { 0 };
        strcpy(config.name, "test.mp3");
        config.option = test_option;
        config.trigger = mp3dec_test2_callback;
        rc = mp3PlayStart(&config);
        if (rc) {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test_start_fail, DIAG_INFORMATION)
            AUDLOGI("rc:%d", rc);
        }
        else {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test_start_success, DIAG_INFORMATION)
            AUDLOGI("rc:%d", rc);
        }
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,Voice,mp3dec_test2_set_option
    void mp3dec_test2_set_option(int* p) {
        if (p)
            test_option = *p;

        //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test2_set_option, DIAG_INFORMATION)
        AUDLOGI("p:0x%lx,test_option:0x%lx", p, test_option);
    }

    //ICAT EXPORTED FUNCTION - Audio,Voice,mp3dec_test_str
    int mp3dec_test_str(char* p) {
        int rc = 0;
        rc = mp3StartWithoutCB(p);
        if (rc) {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test_str_start_fail, DIAG_INFORMATION)
            AUDLOGI("rc:%d", rc);
        }
        else {
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_test_str_success, DIAG_INFORMATION)
            AUDLOGI("rc:%d", rc);
        }
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,Voice,mp3dec_test_stop
    int mp3dec_test_stop(void) {
        mp3Stop();
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,Voice,mp3dec_test_pause
    int mp3dec_test_pause(void) {
        mp3Pause();
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,Voice,mp3dec_test_resume
    int mp3dec_test_resume(void) {
        mp3Resume();
        return 0;
    }


#if MP3_STREAM_SUPPORT == 1

#define MP3_SERIALIZE_TASK_MASK       	(MP3_STREAM_FRAME_IN | MP3_STREAM_FRAME_OUT | MP3_STREAM_CLEANUP )
#define MP3_STREAM_CONFIG_VALID(rate, channel)	(((rate) >= 8000) && ((rate) <= 48000) && ((channel) == 1 || (channel) == 2))

#if MP3PLAY_STREAM_USE_FLASH_CACHE == 1
    typedef struct Mp3StreamDataCache {
        char name[30];
        int size[MP3_FLASH_CACHE_FRAMES];
        int total_size;
    }Mp3StreamDataCache;
    static Mp3StreamDataInfo mp3_stream_cache_data[MP3_FLASH_CACHE_FRAMES] = { 0 };
#endif

    static OSAMsgQRef pcmStreamMsgQ = NULL;
    static uint8_t mp3_stream_cache_mode = MP3PLAY_CACHE_MODE_NONE;
    static uint32_t mp3_stream_rate = 0;
    static uint32_t mp3_stream_channel = 0;
    static int mp3_stream_option = 0;
    static int mp3_stream_cache_frames = 0;
    static uint32_t mp3_ram_frame_count = 0;
#if MP3PLAY_STREAM_USE_FLASH_CACHE == 1
    static uint32_t mp3_flash_subframe_index = 0;
    static uint32_t mp3_flash_index = 0;
#endif
    static OSAFlagRef   mp3_serialize_flag_Ref;
    static OSATaskRef 	mp3_serialize = NULL;
    static OSAMsgQRef   pcmSerializeMsgQ = NULL;
    static OSAMsgQRef   pcmDeserializeMsgQ = NULL;

#if MP3PLAY_STREAM_RAWDATA_SUPPORT == 1
    static uint8_t*	inputBufRaw = NULL;
    static uint32_t	bytesReadRaw = 0;
    static uint32_t	bytesSkipped = 0;
    static uint32_t	bytesToSkip = 0;
    static char* mp3_header_cache = 0;
    static int mp3_header_cache_size = 0;
#endif
    static size_t mp3_fixed_framesize = 0;
    static bool mp3_stream_synced = false;
    static void(*mp3_user_trigger)(int) = 0;

    static void update_stream_config(void);
    static void on_trigger_stream(int status) {
        //DIAG_FILTER(AUDIO, MP3_DEC, on_trigger_stream, DIAG_INFORMATION)
        AUDLOGI("%s, mp3_user_trigger:0x%lx, status:%ld", __FUNCTION__, mp3_user_trigger, status);
        if (status == media_play_event_underrun) {
            OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_FETCH, OSA_FLAG_OR);
        }

        if (status == media_play_event_drain_end) {
            OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_DRAIN_END, OSA_FLAG_OR);
        }

        // inform stream user
        if (status == media_play_event_end)
            status = MP3_STREAM_EVENT_ENDED;
        if (mp3_user_trigger && status >= MP3_STREAM_EVENT_STARTED)
            mp3_user_trigger(status);
    }

    static void on_mp3_stream_serialize(void) {
#if MP3PLAY_STREAM_USE_FLASH_CACHE == 1
        static Mp3StreamDataCache dataCache = { 0 };
#endif
        uint32_t count = 0;
        uint32_t count_streamq = 0;
        if (!pcmStreamMsgQ || !pcmSerializeMsgQ)
            return;

        while (1) {
            OSAMsgQPoll(pcmSerializeMsgQ, &count);
            OSAMsgQPoll(pcmStreamMsgQ, &count_streamq);
            if (count == 0 || count_streamq >= MP3_CACHE_MSGQ_SIZE)
                break;
            MslMessage mslStbcRecvMsg = { 0 };
            OSA_STATUS osaStatus;
            osaStatus = OSAMsgQRecv(pcmSerializeMsgQ, (UINT8*)&mslStbcRecvMsg, sizeof(MslMessage), OSA_NO_SUSPEND);
            if (osaStatus == OS_SUCCESS) {
                Mp3StreamDataInfo* stream = (Mp3StreamDataInfo*)mslStbcRecvMsg.pparm;
                ASSERT(mslStbcRecvMsg.msgId == MP3PLAY_CACHE_MODE_RAM);
#if MP3PLAY_STREAM_TRACE == 1			
                //DIAG_FILTER(AUDIO, stream_trace, on_mp3_stream_serialize, DIAG_INFORMATION)
                AUDLOGI("type:%ld,id:%ld,stream:0x%lx,stream->buf:0x%lx,stream->size:%ld,receive from:0x%lx", mslStbcRecvMsg.msgId, mslStbcRecvMsg.value, mslStbcRecvMsg.pparm, stream->buf, stream->size, pcmSerializeMsgQ);
#endif	
                if (stream && stream->buf && stream->size > 0) {
                    if (mp3_stream_cache_mode == MP3PLAY_CACHE_MODE_RAM) {
                        // bypass serialization
                        osaStatus = OSAMsgQSend(pcmStreamMsgQ, sizeof(MslMessage), (UINT8*)&mslStbcRecvMsg, OSA_NO_SUSPEND);
                        ASSERT(osaStatus == OS_SUCCESS);
#if MP3PLAY_STREAM_TRACE == 1					
                        //DIAG_FILTER(AUDIO, stream_trace, on_mp3_stream_serialize_rammode, DIAG_INFORMATION)
                        AUDLOGI("type:%ld,id:%ld,stream:0x%lx,stream->buf:0x%lx,stream->size:%ld,send to:0x%lx", mslStbcRecvMsg.msgId, mslStbcRecvMsg.value, mslStbcRecvMsg.pparm, stream->buf, stream->size, pcmStreamMsgQ);
#endif					
                        ++mp3_ram_frame_count;
#if MP3PLAY_STREAM_USE_FLASH_CACHE == 1
                        if (mp3_ram_frame_count >= MP3_FRAMES_RAM_CACHE_MAX) {
                            //DIAG_FILTER(AUDIO, MP3_SERIALIZE, on_mp3_stream_serialize_ram2flash, DIAG_INFORMATION)
                            AUDLOGI("mp3_stream_cache_mode change to flash");
                            mp3_stream_cache_mode = MP3PLAY_CACHE_MODE_FLASH;
                            mp3_flash_subframe_index = 0;
                            mp3_flash_index++;
                            memset(&dataCache, 0, sizeof(dataCache));
                        }
#endif
                    }
#if MP3PLAY_STREAM_USE_FLASH_CACHE == 1
                    else if (mp3_stream_cache_mode == MP3PLAY_CACHE_MODE_FLASH) {
                        if (mp3_flash_subframe_index == 0) {
                            char temp[20];
                            snprintf(temp, sizeof(temp), "%d", mp3_flash_index);
                            strcpy(dataCache.name, MP3_FLASH_CACHE_PREFIX);
                            strcat(dataCache.name, temp);
                        }
                        dataCache.size[mp3_flash_subframe_index] = stream->size;
                        dataCache.total_size += stream->size;

#if MP3PLAY_STREAM_TRACE == 1					
                        //DIAG_FILTER(AUDIO, stream_trace, on_mp3_stream_serialize_flashmode, DIAG_INFORMATION)
                        AUDLOGI("type:%ld,id:%ld,stream:0x%lx,stream->buf:0x%lx,stream->size:%ld,send to:0x%lx", mslStbcRecvMsg.msgId, mslStbcRecvMsg.value, mslStbcRecvMsg.pparm, stream->buf, stream->size, 0);
#endif					
                        if (mp3_stream_cache_data[mp3_flash_subframe_index].buf) {
                            free(mp3_stream_cache_data[mp3_flash_subframe_index].buf);
                            mp3_stream_cache_data[mp3_flash_subframe_index].buf = 0;
                        }
                        memcpy(&mp3_stream_cache_data[mp3_flash_subframe_index], stream, sizeof(Mp3StreamDataInfo));
                        free(stream);

                        if (++mp3_flash_subframe_index >= MP3_FLASH_CACHE_FRAMES) {
                            HeliosAudFILE* fp = 0;
                            int index = 0;
#if FLASH_CACHE_WRITE_CALCULATE_TIME
                            UINT32 TSBefore, TSAfter, delayInUsec, delayInTick;
                            TSBefore = timerCountRead(TS_TIMER_ID);
#endif	
                            fp = Helios_Aud_fopen(dataCache.name, "wb");
                            if (!fp) {
                                FDI_remove(dataCache.name);
                                fp = Helios_Aud_fopen(dataCache.name, "wb");
                                //DIAG_FILTER(AUDIO, MP3_SERIALIZE, on_mp3_stream_serialize_flash_overwrite, DIAG_INFORMATION)
                                AUDLOGI("index:%ld,name:%s,fp:0x%lx", index, dataCache.name, fp);
                            }
                            if (fp) {
                                for (index = 0; index < MP3_FLASH_CACHE_FRAMES; index++) {
                                    FDI_fwrite(mp3_stream_cache_data[index].buf, 1, mp3_stream_cache_data[index].size, fp);
                                    free(mp3_stream_cache_data[index].buf);
                                    memset(&mp3_stream_cache_data[index], 0, sizeof(Mp3StreamDataInfo));
                                }
                                Helios_Aud_fclose(fp);
                            }
                            else {
                                memset(dataCache.name, 0, sizeof(dataCache.name));
                            }
#if FLASH_CACHE_WRITE_CALCULATE_TIME
                            TSAfter = timerCountRead(TS_TIMER_ID);

                            /* calculate the delay base on the TS reading  */
                            delayInTick = (TSAfter >= TSBefore) ? TSAfter - TSBefore : ((0xffffffff - TSBefore) + TSAfter);
                            delayInUsec = (timerClockRateGet(TS_TIMER_ID) == FAST_TIMER_CLOCK_RATE_IN_HZ) ?
                                CLOCK_TICKS_TO_MICRO_SEC(delayInTick) :
                                CLOCK_TICKS_TO_MICRO_SEC_32K(delayInTick);

                            //DIAG_FILTER(AUDIO, MP3_DEC, FDI_fwrite, DIAG_INFORMATION)
                            AUDLOGI("size : %ld, total_size : %ld, calculated processing time : %lu uSec", stream->size, dataCache.total_size, delayInUsec);
#endif						
                            MslMessage mslMessage = { 0 };
                            Mp3StreamDataCache* data = (Mp3StreamDataCache*)malloc(sizeof(Mp3StreamDataCache));
                            if (data) {
                                memcpy(data, &dataCache, sizeof(Mp3StreamDataCache));
                                mslMessage.msgId = MP3PLAY_CACHE_MODE_FLASH;
                                mslMessage.value = mslStbcRecvMsg.value;
                                mslMessage.pparm = data;
                                osaStatus = OSAMsgQSend(pcmStreamMsgQ, sizeof(MslMessage), (UINT8*)&mslMessage, OSA_NO_SUSPEND);
                                ASSERT(osaStatus == OS_SUCCESS);
#if MP3PLAY_STREAM_TRACE == 1							
                                //DIAG_FILTER(AUDIO, stream_trace, on_mp3_stream_serialize_flashmode_send, DIAG_INFORMATION)
                                AUDLOGI("type:%ld,id:%ld,stream:0x%lx,name:%s,send to:0x%lx", mslMessage.msgId, mslMessage.value, mslMessage.pparm, data->name, pcmStreamMsgQ);
#endif
                            }

                            mp3_flash_subframe_index = 0;
                            mp3_flash_index++;
                            memset(&dataCache, 0, sizeof(dataCache));

#if MP3PLAY_STREAM_USE_RAM_CACHE == 1
                            if (mp3_ram_frame_count <= MP3_FRAMES_FLASH_CACHE_MIN) {
                                //DIAG_FILTER(AUDIO, MP3_SERIALIZE, on_mp3_stream_serialize_flash2ram, DIAG_INFORMATION)
                                AUDLOGI("mp3_stream_cache_mode change to ram, mp3_flash_subframe_index:%ld", mp3_flash_subframe_index);
                                mp3_stream_cache_mode = MP3PLAY_CACHE_MODE_RAM;
                            }
#endif						
                        }
                    }
#endif
                    else {
                        ASSERT(0);
                    }
                }
                else {
                    //DIAG_FILTER(AUDIO, MP3_SERIALIZE, on_mp3_stream_serialize_invalid, DIAG_INFORMATION)
                    AUDLOGI("stream:0x%lx", stream);
                }
            }
        }

        OSAFlagSet(mp3_serialize_flag_Ref, MP3_STREAM_FRAME_OUT, OSA_FLAG_OR);

        //DIAG_FILTER(AUDIO, MP3_SERIALIZE, on_mp3_stream_serialize_leave, DIAG_INFORMATION)
        AUDLOGI("pcmSerializeMsgQ msg count:%ld", count);
    }

    static void on_mp3_stream_deserialize(void) {
        uint32_t count = 0;
        uint32_t count_deserialize = 0;
        if (!pcmStreamMsgQ || !pcmDeserializeMsgQ)
            return;

        while (1) {
            OSAMsgQPoll(pcmStreamMsgQ, &count);
            OSAMsgQPoll(pcmDeserializeMsgQ, &count_deserialize);
#if MP3PLAY_STREAM_KEEP_FLASH_CACHE == 1
            if (count_deserialize >= (MP3_DESERIALIZE_MSGQ_SIZE - MP3_FLASH_CACHE_FRAMES) || count == 0)
#else
            if (count_deserialize >= (MP3_DESERIALIZE_MSGQ_SIZE) || count == 0)
#endif
                break;

            MslMessage mslStbcRecvMsg = { 0 };
            OSA_STATUS osaStatus = OSAMsgQRecv(pcmStreamMsgQ, (UINT8*)&mslStbcRecvMsg, sizeof(MslMessage), OSA_NO_SUSPEND);
            if (osaStatus == OS_SUCCESS) {
                if (mslStbcRecvMsg.msgId == MP3PLAY_CACHE_MODE_RAM) {
#if MP3PLAY_STREAM_TRACE == 1
                    Mp3StreamDataInfo* stream = (Mp3StreamDataInfo*)mslStbcRecvMsg.pparm;
                    //DIAG_FILTER(AUDIO, stream_trace, on_mp3_stream_deserialize_rammode, DIAG_INFORMATION)
                    AUDLOGI("type:%ld,id:%ld,stream:0x%lx,stream->buf:0x%lx,stream->size:%ld,redirect from 0x%lx to 0x%lx", mslStbcRecvMsg.msgId, mslStbcRecvMsg.value, mslStbcRecvMsg.pparm, stream->buf, stream->size, pcmStreamMsgQ, pcmDeserializeMsgQ);
#endif
                    osaStatus = OSAMsgQSend(pcmDeserializeMsgQ, sizeof(MslMessage), (UINT8*)&mslStbcRecvMsg, OSA_NO_SUSPEND);
                    ASSERT(osaStatus == OS_SUCCESS);
                    mp3_ram_frame_count--;
                }
#if MP3PLAY_STREAM_KEEP_FLASH_CACHE == 1
                else if (mslStbcRecvMsg.msgId == MP3PLAY_CACHE_MODE_FLASH) {
                    int sendSize = 0;
                    int index = 0;
                    Mp3StreamDataCache* dataCache = (Mp3StreamDataCache*)mslStbcRecvMsg.pparm;
                    ASSERT(dataCache);
#if FLASH_CACHE_READ_CALCULATE_TIME
                    UINT32 TSBefore, TSAfter, delayInUsec, delayInTick;
                    TSBefore = timerCountRead(TS_TIMER_ID);
#endif	
                    HeliosAudFILE* fp_read = Helios_Aud_fopen(dataCache->name, "rb");
                    if (fp_read) {
                        for (index = 0; index < MP3_FLASH_CACHE_FRAMES; index++) {
                            Mp3StreamDataInfo* stream = (Mp3StreamDataInfo*)malloc(sizeof(Mp3StreamDataInfo));
                            if (stream) {
                                stream->buf = malloc(dataCache->size[index]);
                                if (stream->buf) {
                                    Helios_Aud_fread(stream->buf, dataCache->size[index], 1, fp_read);
                                    stream->size = dataCache->size[index];
                                    MslMessage mslMsg = { 0 };
                                    mslMsg.msgId = MP3PLAY_CACHE_MODE_RAM;
                                    mslMsg.value = mslStbcRecvMsg.value;
                                    mslMsg.pparm = stream;
                                    OSAMsgQSend(pcmDeserializeMsgQ, sizeof(MslMessage), (UINT8*)&mslMsg, OSA_NO_SUSPEND);
#if MP3PLAY_STREAM_TRACE == 1
                                    //DIAG_FILTER(AUDIO, stream_trace, on_mp3_stream_deserialize_flashmode_send, DIAG_INFORMATION)
                                    AUDLOGI("type:%ld,id:%ld,stream:0x%lx,stream->buf:0x%x,stream->size:%ld,send to:0x%lx", mslStbcRecvMsg.msgId, mslStbcRecvMsg.value, stream, stream->buf, stream->size, pcmDeserializeMsgQ);
#endif
                                }
                                else {
                                    //DIAG_FILTER(AUDIO, MP3_DEC_SERIALIZE, on_mp3_stream_deserialize_flashmode_malloc_fail, DIAG_INFORMATION)
                                    AUDLOGI("stream:0x%lx,dataCache->name:%s,index:%ld,size:%ld,total_size:%ld", stream, dataCache->name, index, dataCache->size[index], dataCache->total_size);
                                    free(stream);
                                }
                            }

                            sendSize += dataCache->size[index];
                            if (sendSize >= dataCache->total_size)
                                break;
                        }

                        Helios_Aud_fclose(fp_read);
                        fp_read = NULL;

#if MP3PLAY_STREAM_KEEP_FLASH_CACHE == 0
                        FDI_remove(dataCache->name);
#endif
                    }
                    else {
                        //DIAG_FILTER(AUDIO, MP3_DEC_SERIALIZE, on_mp3_stream_deserialize_flashmode_open_fail, DIAG_INFORMATION)
                        AUDLOGI("dataCache->name:%s,dataCache->total_size:%ld", dataCache->name, dataCache->total_size);
                    }
#if FLASH_CACHE_READ_CALCULATE_TIME
                    TSAfter = timerCountRead(TS_TIMER_ID);

                    /* calculate the delay base on the TS reading  */
                    delayInTick = (TSAfter >= TSBefore) ? TSAfter - TSBefore : ((0xffffffff - TSBefore) + TSAfter);
                    delayInUsec = (timerClockRateGet(TS_TIMER_ID) == FAST_TIMER_CLOCK_RATE_IN_HZ) ?
                        CLOCK_TICKS_TO_MICRO_SEC(delayInTick) :
                        CLOCK_TICKS_TO_MICRO_SEC_32K(delayInTick);

                    //DIAG_FILTER(AUDIO, MP3_DEC_SERIALIZE, Helios_Aud_fread, DIAG_INFORMATION)
                    AUDLOGI("total_size:%ld, calculated processing time : %lu uSec", dataCache->total_size, delayInUsec);
#endif	
                    free(dataCache);
                    break; // read one file is enough?
                }
#endif
                else {
                    ASSERT(0);
                }
            }
        }

        if (count_deserialize < (MP3_DESERIALIZE_MSGQ_SIZE * 1 / 8))
            on_trigger_stream(MP3_STREAM_EVENT_FAST_UP);
        else if (count_deserialize > (MP3_DESERIALIZE_MSGQ_SIZE * 1 / 2))
            on_trigger_stream(MP3_STREAM_EVENT_SLOW_DOWN);

        if (count_deserialize >= mp3_stream_cache_frames)
            update_stream_config();


        //DIAG_FILTER(AUDIO, MP3_SERIALIZE, on_mp3_stream_deserialize_leave, DIAG_INFORMATION)
        AUDLOGI("pcmStreamMsgQ msg count:%ld, pcmDeserializeMsgQ msg count:%ld", count, count_deserialize);
    }

    static void cleanupMsgQ(OSAMsgQRef* pref) {
        OSAMsgQRef ref = 0;
        if (pref)
            ref = *pref;
        //DIAG_FILTER(AUDIO, MP3_SERIALIZE, cleanupMsgQ, DIAG_INFORMATION)
        AUDLOGI("msgQ ref:0x%lx", ref);
        if (ref) {
            while (1) {
                MslMessage mslStbcRecvMsg = { 0 };
                uint32_t count;
                OSA_STATUS osaStatus;
                OSAMsgQPoll(ref, &count);
                if (count == 0)
                    break;

                osaStatus = OSAMsgQRecv(ref, (UINT8*)&mslStbcRecvMsg, sizeof(MslMessage), OSA_NO_SUSPEND);
                if (osaStatus == OS_SUCCESS) {
                    if (mslStbcRecvMsg.msgId == MP3PLAY_CACHE_MODE_RAM) {
                        Mp3StreamDataInfo* stream = (Mp3StreamDataInfo*)mslStbcRecvMsg.pparm;
#if MP3PLAY_STREAM_TRACE == 1
                        //DIAG_FILTER(AUDIO, stream_trace, cleanupMsgQ_ram, DIAG_INFORMATION)
                        AUDLOGI("type:%ld,id:%ld,stream:0x%lx,stream->buf:0x%lx,stream->size:%ld,receive from:0x%lx", mslStbcRecvMsg.msgId, mslStbcRecvMsg.value, stream, stream->buf, stream->size, ref);
#endif	
                        if (stream) {
                            if (stream->buf)
                                free(stream->buf);
                            free(stream);
                        }
                    }
#if MP3PLAY_STREAM_KEEP_FLASH_CACHE == 1
                    else if (mslStbcRecvMsg.msgId == MP3PLAY_CACHE_MODE_FLASH) {
                        Mp3StreamDataCache* dataCache = (Mp3StreamDataCache*)mslStbcRecvMsg.pparm;
#if MP3PLAY_STREAM_TRACE == 1
                        //DIAG_FILTER(AUDIO, stream_trace, cleanupMsgQ_flash, DIAG_INFORMATION)
                        AUDLOGI("type:%ld,id:%ld,stream:0x%lx,name:%s,receive from:0x%lx", mslStbcRecvMsg.msgId, mslStbcRecvMsg.value, dataCache, dataCache->name, ref);
#endif
                        if (dataCache) {
#if MP3PLAY_STREAM_KEEP_FLASH_CACHE == 0
                            FDI_remove(dataCache->name);
#endif
                            free(dataCache);
                        }
                    }
#endif
                }
            }

            OSAMsgQDelete(ref);
            *pref = 0;
        }
    }

    static void on_mp3_stream_cleanup(void) {
        //DIAG_FILTER(AUDIO, MP3_SERIALIZE, on_mp3_stream_cleanup, DIAG_INFORMATION)
        AUDLOGI("mp3_stream_cache_mode:%ld", mp3_stream_cache_mode);
#if MP3PLAY_STREAM_KEEP_FLASH_CACHE == 1	
        if (mp3_flash_subframe_index > 0) {
            uint32_t index = 0;
            for (index = 0; index < mp3_flash_subframe_index; index++) {
                if (mp3_stream_cache_data[index].buf)
                    free(mp3_stream_cache_data[index].buf);
            }
            memset(&mp3_stream_cache_data[0], 0, sizeof(mp3_stream_cache_data));
        }
#endif

        if (pcmStreamMsgQ) {
            cleanupMsgQ(&pcmStreamMsgQ);
        }
        if (pcmSerializeMsgQ) {
            cleanupMsgQ(&pcmSerializeMsgQ);
        }
        if (pcmDeserializeMsgQ) {
            cleanupMsgQ(&pcmDeserializeMsgQ);
        }

#if MP3PLAY_STREAM_RAWDATA_SUPPORT == 1
        if (inputBufRaw) {
            free(inputBufRaw);
            inputBufRaw = NULL;
        }
        if (mp3_header_cache) {
            free(mp3_header_cache);
            mp3_header_cache = NULL;
        }
#endif
    }

#if MP3PLAY_STREAM_USE_SEPERATE_TASK == 1
    static void mp3_serialize_main_loop(void* p)
    {
        unsigned int   			event = 0;
        while (1) {
            OSAFlagWait(mp3_serialize_flag_Ref, MP3_SERIALIZE_TASK_MASK, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND);
            //DIAG_FILTER(AUDIO, MP3_SERIALIZE, mp3_serialize_event, DIAG_INFORMATION)
            AUDLOGI("mp3_serialize_event:%x", event);

            if (MP3_STREAM_FRAME_IN & event) {
                on_mp3_stream_serialize();
            }

            if (MP3_STREAM_FRAME_OUT & event) {
                on_mp3_stream_deserialize();
            }

            if (MP3_STREAM_CLEANUP & event) {
                on_mp3_stream_cleanup();
            }
        }
    }
#endif

    static void mp3serialize_task_init(void)
    {
        static bool inited = false;

        if (!inited) {
#if MP3PLAY_STREAM_USE_SEPERATE_TASK == 1
            OS_STATUS status;
            size_t stackSize = 1024 * 8;
            int thread_priority = 80;
            void* mp3_serialize_stack_ptr = 0;

            status = OSAFlagCreate(&mp3_serialize_flag_Ref);
            ASSERT(status == OS_SUCCESS);

            mp3_serialize_stack_ptr = malloc(stackSize);
            ASSERT(mp3_serialize_stack_ptr);
            status = OSATaskCreate(&mp3_serialize, mp3_serialize_stack_ptr, stackSize, thread_priority, "mp3Serialize", mp3_serialize_main_loop, NULL);
            ASSERT(status == OS_SUCCESS);
#else
            mp3_serialize_flag_Ref = dec_pcm_flag_Ref;
#endif
            inited = 1;
        }
    }

    static int mp3_dec_stream_init(tPVMP3DecoderExternal* config) {
        uint32_t 	memRequirements = 0;
        uint32_t	sampleRate = mp3_stream_rate;
        uint32_t 	numChannel = mp3_stream_channel;
        uint32_t 	option = mp3_stream_option;

        //DIAG_FILTER(AUDIO, MP3_DEC, mp3_stream_dec_init_enter, DIAG_INFORMATION)
        AUDLOGI("%s, sampleRate: %ld, numChannel: %ld", __FUNCTION__, sampleRate, numChannel);

        if (!config)
            return -1;

        config->equalizerType = flat;
        config->crcEnabled = false;

        // Allocate the decoder memory.
        memRequirements = pvmp3_decoderMemRequirements();
        if (decoderBuf)
            free(decoderBuf);
        decoderBuf = malloc(memRequirements);
        ASSERT(decoderBuf != NULL);

        // Initialize the decoder.
        pvmp3_InitDecoder(config, decoderBuf);

        // Allocate input buffer.
        if (inputBuf)
            free(inputBuf);
        inputBuf = static_cast<uint8_t*>(malloc(kInputBufferSize));
        ASSERT(inputBuf != NULL);

        //DIAG_FILTER(AUDIO, MP3_DEC, mp3_stream_dec_init_malloc, DIAG_INFORMATION)
        AUDLOGI("decoder malloc:%ld bytes", memRequirements + kInputBufferSize);

        if (MP3_STREAM_CONFIG_VALID(sampleRate, numChannel)) {
            on_media_play_start(sampleRate, numChannel, option, on_trigger_stream);
            on_trigger_stream(MP3_STREAM_EVENT_STARTED);
        }
        return 0;
    }

    static void update_stream_config(void) {
        if ((is_mp3_playing == MP3PLAY_ACTIVE_MODE_STREAM || is_mp3_playing == MP3PLAY_ACTIVE_MODE_STREAM_ENDING)
            && !MP3_STREAM_CONFIG_VALID(mp3_stream_rate, mp3_stream_channel)) {
            bool parsed = parseHeader(mp3_fixed_header, &mp3_fixed_framesize, &mp3_stream_rate, &mp3_stream_channel, 0, 0);
            if (parsed) {
                //DIAG_FILTER(AUDIO, MP3_DEC, update_stream_config, DIAG_INFORMATION)
                AUDLOGI("header:0x%lx,rate:%ld, ch:%ld", mp3_fixed_header, mp3_stream_rate, mp3_stream_channel);
                mp3_dec_stream_init(&decoderConfig);
            }
            else {
                OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_STOP, OSA_FLAG_OR);
            }
        }
    }

    static int on_mp3_stream_fetch(void) {
        MslMessage mslStbcRecvMsg = { 0 };
        OSA_STATUS osaStatus;
        uint32_t count;
        int ret = -1;
        if (!pcmDeserializeMsgQ)
            return -2;

        OSAFlagSet(mp3_serialize_flag_Ref, MP3_STREAM_FRAME_OUT, OSA_FLAG_OR);
        OSAMsgQPoll(pcmDeserializeMsgQ, &count);
        //DIAG_FILTER(AUDIO, MP3_DEC, on_mp3_stream_fetch, DIAG_INFORMATION)
        AUDLOGI("pcmDeserializeMsgQ msg count:%ld", count);
        if (count > 0) {
            osaStatus = OSAMsgQRecv(pcmDeserializeMsgQ, (UINT8*)&mslStbcRecvMsg, sizeof(MslMessage), OSA_NO_SUSPEND);
            if (osaStatus == OS_SUCCESS) {
                ASSERT(mslStbcRecvMsg.msgId == MP3PLAY_CACHE_MODE_RAM);
                Mp3StreamDataInfo* stream = (Mp3StreamDataInfo*)mslStbcRecvMsg.pparm;
#if MP3PLAY_STREAM_TRACE == 1			
                //DIAG_FILTER(AUDIO, stream_trace, on_mp3_stream_fetch, DIAG_INFORMATION)
                AUDLOGI("type:%ld,id:%ld,stream:0x%lx,stream->buf:0x%lx,stream->size:%ld,receive from:0x%lx", mslStbcRecvMsg.msgId, mslStbcRecvMsg.value, mslStbcRecvMsg.pparm, stream->buf, stream->size, pcmDeserializeMsgQ);
#endif
                if (stream && stream->buf && stream->size > 0) {
                    memcpy(inputBuf, stream->buf, stream->size);
                    bytesRead = stream->size;
#if 0				
                    OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_DECODE, OSA_FLAG_OR);
#else
                    mp3_dec_decode(true);
#endif
                    free(stream->buf);
                    free(stream);
                    ret = 0;
                }
                else {
                    ret = -3;
                }
            }
            else {
                ret = -4;
            }
        }
        else {
            if ((mp3_stream_option & MEDIA_PLAY_MASK_DRAIN) && is_mp3_playing == MP3PLAY_ACTIVE_MODE_STREAM_ENDING) {
                OSAFlagSet(mp3_serialize_flag_Ref, MP3_STREAM_CLEANUP, OSA_FLAG_OR);
                OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_STOP, OSA_FLAG_OR);
            }
        }


        return ret;
    }

#define mp3_header_mask (0xfffe0c00)
    int mp3StreamPlayFrame(const Mp3StreamDataInfo* info) {
        static uint32_t streamDataId = 0;
        int ret_code = -1;

#if MP3PLAY_STREAM_RESYNC == 1	
        if (info && info->buf && info->size > 4) {
            uint32_t rate, ch, bitrate, samples;
            size_t frame_size;
            uint32_t possible_header = U32_AT((const uint8_t*)info->buf);
            bool parsed = parseHeader(possible_header, &frame_size, &rate, &ch, &bitrate, &samples);
            if (parsed) {
                if (mp3_fixed_header == 0) {
                    mp3_fixed_header = possible_header;
                }
                else {
                    uint32_t fixed_rate, fixed_ch, fixed_bitrate, fixed_samples;
                    size_t fixed_frame_size;
                    parseHeader(mp3_fixed_header, &fixed_frame_size, &fixed_rate, &fixed_ch, &fixed_bitrate, &fixed_samples);
                    if (rate != fixed_rate
                        || ch != fixed_ch
                        || samples != fixed_samples
                        || (mp3_fixed_header & mp3_header_mask) != (possible_header & mp3_header_mask)) {
                        parsed = false;
                    }
                }
            }
            if (!parsed) {
                return -2;
            }
        }
        else {
            return -3;
        }
#endif

        //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamPlayFrame_dump, DIAG_INFORMATION)
        diagStructPrintf("mp3_stream_frame", info->buf, info->size);
        if (is_mp3_playing == MP3PLAY_ACTIVE_MODE_STREAM) {
            if (mp3_stream_cache_mode == MP3PLAY_CACHE_MODE_NONE) {
                memcpy(inputBuf, info->buf, info->size);
                bytesRead = info->size;
                OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_DECODE, OSA_FLAG_OR);
                return 0;
            }
            else if (pcmSerializeMsgQ) {
                uint32_t count = 0;
                OSAMsgQPoll(pcmSerializeMsgQ, &count);
                if (count < MP3_SERIALIZE_MSGQ_SIZE) {
                    OSA_STATUS osaStatus;
                    MslMessage	mslMessage = { 0 };
                    Mp3StreamDataInfo* stream = (Mp3StreamDataInfo*)malloc(sizeof(Mp3StreamDataInfo));
                    if (stream) {
                        mslMessage.msgId = MP3PLAY_CACHE_MODE_RAM;
                        mslMessage.value = streamDataId++;
                        stream->buf = malloc(info->size);
                        if (stream->buf) {
                            mslMessage.pparm = stream;
                            memcpy(stream->buf, info->buf, info->size);
                            stream->size = info->size;

#if MP3PLAY_STREAM_TRACE == 1					
                            //DIAG_FILTER(AUDIO, stream_trace, mp3StreamPlayFrame, DIAG_INFORMATION)
                            AUDLOGI("type:%ld,id:%ld,stream:0x%lx,stream->buf:0x%lx,stream->size:%ld,send to:0x%lx", mslMessage.msgId, mslMessage.value, stream, stream->buf, stream->size, pcmSerializeMsgQ);
#endif
                            osaStatus = OSAMsgQSend(pcmSerializeMsgQ, sizeof(MslMessage), (UINT8*)&mslMessage, OSA_NO_SUSPEND);
                            ASSERT(osaStatus == OS_SUCCESS);
                            ret_code = 0;
                        }
                        else {
                            free(stream);
                            stream = 0;
                        }
                    }
                }
                else {
                    on_trigger_stream(MP3_STREAM_EVENT_LOST);
                    //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamPlay_full, DIAG_INFORMATION)
                    AUDLOGI("count:%ld", count);
                }
                OSAFlagSet(mp3_serialize_flag_Ref, MP3_STREAM_FRAME_IN, OSA_FLAG_OR);
            }
        }

        return ret_code;
    }

#if MP3PLAY_STREAM_RAWDATA_SUPPORT == 1
    static int mp3StreamPlayBufferUnsynced(const Mp3StreamDataInfo* dataInfo);
    static int mp3StreamPlayBufferSynced(const Mp3StreamDataInfo* dataInfo) {
        if (inputBufRaw && mp3_fixed_framesize > 4 && dataInfo && dataInfo->buf && dataInfo->size > 0) {
            int parsedSize = 0;
            int readBlockSize = dataInfo->size;
            while (parsedSize < readBlockSize) {
                int copy_size = readBlockSize - parsedSize;
                if (copy_size > mp3_fixed_framesize - bytesReadRaw)
                    copy_size = mp3_fixed_framesize - bytesReadRaw;
                memcpy(&inputBufRaw[bytesReadRaw], (char*)dataInfo->buf + parsedSize, copy_size);
                bytesReadRaw += copy_size;
                parsedSize += copy_size;

                while (bytesReadRaw >= mp3_fixed_framesize) {
#if MP3PLAY_STREAM_RESYNC == 0
                    if (bytesReadRaw == mp3_fixed_framesize) {
                        uint32_t syncIndex = 0;
                        // lost sync
                        for (syncIndex = 0; syncIndex < mp3_fixed_framesize; syncIndex++) {
                            if (inputBufRaw[syncIndex] == (uint8_t)0xff)
                                break;
                        }
                        bytesReadRaw -= syncIndex;
                        if (bytesReadRaw > 0) {
                            if (syncIndex > 0) {
                                memmove(&inputBufRaw[0], &inputBufRaw[syncIndex], bytesReadRaw * sizeof(inputBufRaw[0]));
                            }
                            if (bytesReadRaw >= 4) {
                                uint32_t rate, ch, bitrate, samples;
                                size_t frame_size;
                                uint32_t possible_header = U32_AT(inputBufRaw);
                                bool parsed = parseHeader(possible_header, &frame_size, &rate, &ch, &bitrate, &samples);
                                if (parsed) {
                                    uint32_t fixed_rate, fixed_ch, fixed_bitrate, fixed_samples;
                                    size_t fixed_frame_size;
                                    parseHeader(mp3_fixed_header, &fixed_frame_size, &fixed_rate, &fixed_ch, &fixed_bitrate, &fixed_samples);
                                    if ((mp3_fixed_header & mp3_header_mask) != (possible_header & mp3_header_mask) || rate != fixed_rate || ch != fixed_ch || samples != fixed_samples) {
                                        parsed = false;
                                    }
                                    else {
                                        if (mp3_fixed_framesize != frame_size) {
                                            //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamPlayBufferSynced_frameSize, DIAG_INFORMATION)
                                            AUDLOGI("change from %ld to %ld", mp3_fixed_framesize, frame_size);
                                            mp3_fixed_framesize = frame_size;
                                        }
                                    }
                                }
                                if (!parsed) {
                                    memmove(&inputBufRaw[0], &inputBufRaw[1], (bytesReadRaw - 1) * sizeof(inputBufRaw[0]));
                                    bytesReadRaw -= 1;
                                }
                            }
                        }
                    }
#else
                    if (inputBufRaw[0] != (uint8_t)0xff) {
                        Mp3StreamDataInfo frame = { 0 };
                        if (dataInfo->size > parsedSize) {
                            memcpy(&inputBufRaw[bytesReadRaw], (char*)dataInfo->buf + parsedSize, dataInfo->size - parsedSize);
                            bytesReadRaw += (dataInfo->size - parsedSize);
                        }
                        frame.buf = inputBufRaw;
                        frame.size = bytesReadRaw;
                        bytesReadRaw = 0;
                        mp3_stream_synced = false;
                        return mp3StreamPlayBufferUnsynced(&frame);
                    }
#endif

                    if (bytesReadRaw >= mp3_fixed_framesize) {
                        Mp3StreamDataInfo frame = { 0 };
                        frame.buf = inputBufRaw;
                        frame.size = mp3_fixed_framesize;
                        mp3StreamPlayFrame(&frame);
                        bytesReadRaw -= mp3_fixed_framesize;
                        if (bytesReadRaw > 0) {
                            memmove(&inputBufRaw[0], &inputBufRaw[mp3_fixed_framesize], bytesReadRaw);
                        }
                    }
                }
            }
        }

        if (mp3_stream_synced && mp3_header_cache) {
            free(mp3_header_cache);
            mp3_header_cache = 0;
            mp3_header_cache_size = 0;
        }

        return 0;
    }

    static int mp3StreamPlayBufferUnsynced(const Mp3StreamDataInfo* dataInfo) {
        uint32_t pos = 0;
        uint32_t header = 0;
#if INTERNAL_CONFIG_SUPPORT_REALLOC == 1
        mp3_header_cache = (char*)realloc(mp3_header_cache, mp3_header_cache_size + dataInfo->size);
#else
        char* tmp = (char*)malloc(mp3_header_cache_size + dataInfo->size);
        if (tmp && mp3_header_cache && mp3_header_cache_size > 0)
            memcpy(tmp, mp3_header_cache, mp3_header_cache_size);
        if (mp3_header_cache) {
            free(mp3_header_cache);
        }
        mp3_header_cache = tmp;
#endif

        if (mp3_header_cache) {
            memcpy(&mp3_header_cache[mp3_header_cache_size], dataInfo->buf, dataInfo->size);
            mp3_header_cache_size += dataInfo->size;
#ifdef SIMULATOR
            //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamPlayBufferUnsynced_dump, DIAG_INFORMATION)
            diagStructPrintf("mp3_sync_buffer", mp3_header_cache, mp3_header_cache_size);
#endif
            if (mp3_fixed_header != 0) {
                pos = 1;
            }
            mp3_stream_synced = resyncStream(mp3_header_cache, &mp3_header_cache[mp3_header_cache_size], mp3_fixed_header, &pos, &header);
            if (mp3_stream_synced) {
                Mp3StreamDataInfo parsedDataInfo = { 0 };
                parsedDataInfo.buf = &mp3_header_cache[pos];
                parsedDataInfo.size = mp3_header_cache_size - pos;
                if (mp3_fixed_header == 0) {
                    mp3_fixed_header = header;
                    parseHeader(header, &mp3_fixed_framesize, 0, 0, 0, 0);
                }
                //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamPlayBufferUnsynced_success, DIAG_INFORMATION)
                AUDLOGI("mp3_fixed_header:0x%lx, header:0x%lx, pos:%ld, frame_size:%ld", mp3_fixed_header, header, pos, mp3_fixed_framesize);
                return mp3StreamPlayBufferSynced(&parsedDataInfo);
            }
        }

        return -1;
    }

    int mp3StreamPlayBuffer(const Mp3StreamDataInfo* dataInfo) {
        int err_code = -1;
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamPlayBuffer_dump, DIAG_INFORMATION)
        diagStructPrintf("mp3_stream_buffer", dataInfo->buf, dataInfo->size);
        if (!mp3_stream_synced) {
            if (bytesToSkip == 0) {
                uint32_t header = 0;
                resyncStream((char*)dataInfo->buf, (char*)dataInfo->buf + dataInfo->size, 0, &bytesToSkip, &header);
                if (bytesToSkip == 0) {
                    err_code = mp3StreamPlayBufferUnsynced(dataInfo);
                }
                else if (bytesToSkip < dataInfo->size) {
                    Mp3StreamDataInfo skippedDataInfo = { 0 };
                    skippedDataInfo.buf = (void*)((char*)dataInfo->buf + bytesToSkip);
                    skippedDataInfo.size = dataInfo->size - bytesToSkip;
                    err_code = mp3StreamPlayBufferUnsynced(&skippedDataInfo);
                }
            }
            else {
                bytesSkipped += (uint32_t)dataInfo->size;
                if (bytesSkipped < bytesToSkip) {
                    err_code = -1;
                }
                else {
                    err_code = mp3StreamPlayBufferUnsynced(dataInfo);
                }
            }
        }
        else {
            if (mp3_fixed_framesize == 0) {
                mp3_fixed_header = U32_AT((const uint8_t*)dataInfo->buf);
                update_stream_config();
            }
            err_code = mp3StreamPlayBufferSynced(dataInfo);
        }

        return err_code;
    }
#endif

    int mp3StreamStart(const Mp3StreamConfigInfo* configInfo) {
        OSA_STATUS osaStatus;
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamStart, DIAG_INFORMATION)
        AUDLOGI("configInfo:0x%lx,is_mp3_playing:%d", configInfo, is_mp3_playing);

        if (is_mp3_playing) {
            return -1;
        }

#if MP3PLAY_STREAM_RAWDATA_SUPPORT == 1
        mp3_fixed_framesize = 0;
        mp3_fixed_header = 0;
        bytesReadRaw = 0;
        bytesSkipped = 0;
        bytesToSkip = 0;
        inputBufRaw = (uint8_t*)malloc(kInputBufferSize);
        mp3_header_cache_size = 0;
#endif
        mp3_ram_frame_count = 0;
#if MP3PLAY_STREAM_USE_FLASH_CACHE == 1
        mp3_flash_subframe_index = 0;
        mp3_flash_index = 0;
#endif        
        if (configInfo) {
            mp3_stream_rate = configInfo->rate;
            mp3_stream_channel = configInfo->channel;
            mp3_stream_option = configInfo->option;
            mp3_user_trigger = configInfo->trigger;
            mp3_stream_cache_frames = configInfo->cache_frames;
            mp3_stream_synced = configInfo->header_parsed ? true : false;
        }
        else {
            mp3_stream_rate = 0;
            mp3_stream_channel = 0;
            mp3_stream_option = 0;
            mp3_user_trigger = 0;
            mp3_stream_cache_frames = 0;
            mp3_stream_synced = false;
        }

        if (mp3_stream_cache_frames <= 0) {
            mp3_stream_cache_frames = MP3_STREAM_CACHE_SIZE;
            if (mp3_stream_cache_frames > MP3_DESERIALIZE_MSGQ_SIZE)
                mp3_stream_cache_frames = MP3_DESERIALIZE_MSGQ_SIZE;
        }

        cleanupMsgQ(&pcmStreamMsgQ);
        osaStatus = OSAMsgQCreate(&pcmStreamMsgQ,
#ifdef OSA_QUEUE_NAMES
            "pcmStreamMsgQ",
#endif
            sizeof(MslMessage), MP3_CACHE_MSGQ_SIZE, OSA_PRIORITY);
        ASSERT(osaStatus == OS_SUCCESS);

        cleanupMsgQ(&pcmSerializeMsgQ);
        osaStatus = OSAMsgQCreate(&pcmSerializeMsgQ,
#ifdef OSA_QUEUE_NAMES
            "pcmSerializeMsgQ",
#endif
            sizeof(MslMessage), MP3_SERIALIZE_MSGQ_SIZE, OSA_PRIORITY);
        ASSERT(osaStatus == OS_SUCCESS);

        cleanupMsgQ(&pcmDeserializeMsgQ);
        osaStatus = OSAMsgQCreate(&pcmDeserializeMsgQ,
#ifdef OSA_QUEUE_NAMES
            "pcmDeserializeMsgQ",
#endif
            sizeof(MslMessage), MP3_DESERIALIZE_MSGQ_SIZE, OSA_PRIORITY);
        ASSERT(osaStatus == OS_SUCCESS);

        is_mp3_playing = MP3PLAY_ACTIVE_MODE_STREAM;
#if MP3PLAY_STREAM_USE_RAM_CACHE == 0 && MP3PLAY_STREAM_USE_FLASH_CACHE == 0
        mp3_stream_cache_mode = MP3PLAY_CACHE_MODE_NONE;
#else
#if MP3PLAY_STREAM_USE_RAM_CACHE == 1
        mp3_stream_cache_mode = MP3PLAY_CACHE_MODE_RAM;
#else
        mp3_stream_cache_mode = MP3PLAY_CACHE_MODE_FLASH;
#endif
#endif
        if (mp3_Decoder == NULL) {
            mp3dec_task_init();
        }
        if (mp3_serialize == NULL) {
            mp3serialize_task_init();
        }

        OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_START, OSA_FLAG_OR);

        //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamStart_info, DIAG_INFORMATION)
        AUDLOGI("rate:%ld,channel:%ld,option:0x%lx,cache_frames:%ld", mp3_stream_rate, mp3_stream_channel, mp3_stream_option, mp3_stream_cache_frames);
        return 0;
    }

    int mp3StreamPause(void) {
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamPause, DIAG_INFORMATION)
        AUDLOGI("is_mp3_paused:0x%lx,is_mp3_playing:%d", is_mp3_paused, is_mp3_playing);

        if ((is_mp3_paused & MP3PLAY_PAUSED_FROM_USER) == 0) {
            if (is_mp3_paused == MP3PLAY_PAUSED_NONE) {
                int paused = on_media_play_suspend();
                if (paused == 1) {
                    is_mp3_paused = MP3PLAY_PAUSED_FROM_USER;
                    return 0;
                }
            }
            else {
                is_mp3_paused |= MP3PLAY_PAUSED_FROM_USER;
                return 0;
            }
        }

        return -1;
    }

    int mp3StreamResume(void) {
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamResume, DIAG_INFORMATION)
        AUDLOGI("is_mp3_paused:0x%lx,is_mp3_playing:%d", is_mp3_paused, is_mp3_playing);

        if ((is_mp3_paused & MP3PLAY_PAUSED_FROM_USER)) {
            if (is_mp3_paused == MP3PLAY_PAUSED_FROM_USER) {
                int paused = on_media_play_resume();
                if (paused == 0) {
                    is_mp3_paused = MP3PLAY_PAUSED_NONE;
                    return 0;
                }
            }
            else {
                is_mp3_paused &= (~MP3PLAY_PAUSED_FROM_USER);
                return 0;
            }
        }

        return -1;
    }

    int mp3StreamStop(void) {
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3StreamStop, DIAG_INFORMATION)
        AUDLOGI("is_mp3_playing:%d,channel:%ld,option:0x%lx", is_mp3_playing, mp3_stream_channel, mp3_stream_option);

        if ((mp3_stream_option & MEDIA_PLAY_MASK_DRAIN) == 0) {
            OSAFlagSet(mp3_serialize_flag_Ref, MP3_STREAM_CLEANUP, OSA_FLAG_OR);
            OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_STOP, OSA_FLAG_OR);
        }
        else {
            is_mp3_playing = MP3PLAY_ACTIVE_MODE_STREAM_ENDING;
            on_mp3_stream_fetch();
            update_stream_config();
        }

        return 0;
    }

    int mp3StreamStopWithDrain(int drain) {
        if (drain)
            mp3_stream_option |= MEDIA_PLAY_MASK_DRAIN;
        else
            mp3_stream_option &= (~MEDIA_PLAY_MASK_DRAIN);
        on_media_play_drain(drain);
        return mp3StreamStop();
    }

#endif
} // end of extern C
