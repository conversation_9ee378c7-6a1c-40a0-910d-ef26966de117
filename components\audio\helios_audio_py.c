#include <stdio.h>
#include <string.h>
#include <assert.h>


#include "helios_audio_py.h"
#include "helios_audio_amr.h"
#include "helios_include.h"
#include "helios_audio_fs.h"
#include "helios_audio_mp3.h"
#include "helios_audio_wav.h"
#include "helios_audio_snd.h"
#include "helios_os.h"

static helios_cb_on_player play_user_cb = NULL;

static int is_play = 0;

#include "assert.h"

helios_audio_pa_cb_t pa_callback[HELIOS_OUTPUT_MAX] = {NULL};

static Helios_AudOutputType cur_AudType =  HELIOS_OUTPUT_RECEIVER;



#define    STRNICMP(strFirst,strSecond,nlen)    strncasecmp((char *)(strFirst),(char *)(strSecond), (nlen))
#define    STRICMP(strFirst,strSecond)    strcasecmp((char *)(strFirst), (char *)(strSecond))


#define HELIOS_PCM_BLOCK_FLAG (0x01)
#define HELIOS_PCM_NONBLOCK_FLAG (0x02)
#define HELIOS_PCM_READ_FLAG (0x04)
#define HELIOS_PCM_WRITE_FLAG (0x08)


#define PACKET_WRITE_MAX_SIZE 3 * 1024

HELIOS_STREAM_INIT_STATE helios_stream_init_flag = HELIOS_NOT_INITED;
static HELIOS_STREAM_FINISH_STATE helios_stream_send_flag = HELIOS_STREAM_SEND_FINISH;

static helios_cb_on_player play_stream_user_cb = NULL;

//static Helios_PCM_CONFIG_T wav_config;
static PCM_HANDLE_T write_hdl = NULL;

//static Helios_PCM_CONFIG_T pcm_config = {1, 8000, 0}; 


static Helios_AudStreamFormat stream_format = HELIOS_AUDIO_FORMAT_UNKNOWN;

int helios_audio_play_callback(char *p_data, int len, Helios_EnumAudPlayerState state);


static void __audio_pa_callback(unsigned int event){
	if(pa_callback[cur_AudType]) {
		pa_callback[cur_AudType](event);
	}

} 

 int Helios_Audio_SetPaCallback_py(Helios_AudOutputType type, helios_audio_pa_cb_t cb){
	cur_AudType = type;
	pa_callback[type] = cb;
	return 0;
 }



static int helios_find_file(char *name)
{
	HeliosAudFILE *fileID = NULL;

	fileID = Helios_Aud_fopen(name, "r");
	if (fileID)
	{
		Helios_Aud_fclose(fileID);

		return 1;
	}
	return 0;
}


/**
 * @brief:
 *      Start playing the audio stream.
 *
 * @param:
 *      \format        	[in]    - Audio format
 *      \buf            [in]    - audio buf
 *      \size           [in]    - buf size
 *      \type           [in]    - the audio play type 0-NONE,1-Local,2-Voice,3-POC,4-Max type
 *      \outputtype     [in]    - Audio channel 0:receiver 1:headphone 2:speaker
 *      \usr_cb         [in]    - play callback
 * @return:
 *      start play audio file result, 0 for success,-1 for failure
 */
#if defined(PLAT_ASR_1606) || defined(PLAT_ASR_1609) || defined(PLAT_ASR_1602)
typedef void(*helios_cb_pcm_play)(void*, int);

static void __pcm_play_cb(void *handle, int event){
	AUDLOGI("__pcm_play_cb event[%d]\n",event);
	helios_audio_play_callback("stream", 0, event);
}
#endif
int Helios_Audio_StreamPlayStart(Helios_AudStreamFormat format, const void *buf, size_t streamsize,
								 Helios_AudPlayerType type, Helios_AudOutputType outputtype, helios_cb_on_player usr_cb)
{
	int cnt = 0, total_cnt = 0, err = 0;
	play_user_cb = usr_cb;
#if CONFIG_AUDIO_MP3
	helios_mp3_set_callback(helios_audio_play_callback);
#endif
#if CONFIG_AUDIO_AMR
	helios_amr_set_callback(helios_audio_play_callback);
#endif
		
	if (helios_stream_init_flag == HELIOS_NOT_INITED)
	{
		switch (format)
		{
#if CONFIG_AUDIO_WAV
		case HELIOS_AUDIO_FORMAT_WAVPCM:
		{

			int samplerate = 0;
			HELIOS_WAV_TAG wav_tag;
			memset(&wav_tag,0,sizeof(HELIOS_WAV_TAG));
			memcpy(&wav_tag,buf,sizeof(HELIOS_WAV_TAG));
			samplerate = wav_tag.SampleRate;
			AUDLOGI("samplerate is %d\n",samplerate);
			if(samplerate!=6000 && samplerate!=8000 && samplerate!=11025 && samplerate!=16000 && samplerate!=22050
			 && samplerate!=32000 && samplerate!=44100 && samplerate!=48000)
			{	
				AUDLOGI("wav init failed samplerate not support check the stream\n");
				return(-1);
			}
//			memset(&wav_config, 0, sizeof(Helios_PCM_CONFIG_T));
//			wav_config.channels = wav_tag.NumChannels;
//			wav_config.samplerate = samplerate;
//			wav_config.periodcnt = 0;
//			write_hdl = Helios_PCM_Open(&wav_config, HELIOS_PCM_WRITE_FLAG|HELIOS_PCM_BLOCK_FLAG);
			if(!write_hdl) {
				write_hdl = Helios_PCM_Open(wav_tag.NumChannels, samplerate, HELIOS_PCM_WRITE_FLAG | HELIOS_PCM_BLOCK_FLAG);
			}
			if(write_hdl==NULL){
				AUDLOGI("wav init failed pcm open failed\n");
				return(-1);
			}
//			AUDLOGI("wav fomat init completed channels:%d,samplerate:%d\n",wav_config.channels,wav_config.samplerate);
			helios_stream_init_flag = HELIOS_INITED;
#if defined(PLAT_ASR_1606) || defined(PLAT_ASR_1609) || defined(PLAT_ASR_1602)
			Helios_PCM_Play_Cb(write_hdl, __pcm_play_cb);
#endif
		}
		break;
#endif
#if CONFIG_AUDIO_MP3
		case HELIOS_AUDIO_FORMAT_MP3:
		{
			AUDLOGI("mp3 init finished\n");
			helios_stream_init_flag = HELIOS_INITED;
			helios_mp3_set_callback(helios_audio_play_callback);
		}
		break;
#endif

		case HELIOS_AUDIO_FORMAT_AMRWB:
		{
		}
#if CONFIG_AUDIO_AMR
		case HELIOS_AUDIO_FORMAT_AMRNB:
		{
			char *amr_info = (char *)buf;
			if (amr_info[0] != 0x04 && amr_info[0] != 0x0C && amr_info[0] != 0x14 && amr_info[0] != 0x1C && amr_info[0] != 0x24 && amr_info[0] != 0x2C && amr_info[0] != 0x34 && amr_info[0] != 0x3C)
			{
				AUDLOGI("amr init failed stream speed not found\n");
				return (-1);
			}
			err = helios_amr_stream_open();
			if (err != 0)
			{
				AUDLOGI("amr init failed ql_amr_stream_open failed\n");
				return (-1);
			}
			AUDLOGI("amr init finished\n");
			helios_stream_init_flag = HELIOS_INITED;
			helios_amr_set_callback(helios_audio_play_callback);
		}
		break;
#endif
		case HELIOS_AUDIO_FORMAT_PCM:
		{ 
//			memset(&pcm_config, 0, sizeof(Helios_PCM_CONFIG_T));
//			pcm_config.channels=1;
//			pcm_config.samplerate=8000;
//			pcm_config.periodcnt=0;
//			write_hdl = Helios_PCM_Open(&pcm_config, HELIOS_PCM_WRITE_FLAG|HELIOS_PCM_BLOCK_FLAG);
#ifdef CONFIG_AUDIO_PWM
		write_hdl = Helios_Aud_PWM_Open(CONFIG_AUDIO_PWM_PIN, 1, 8000, HELIOS_PCM_WRITE_FLAG | HELIOS_PCM_BLOCK_FLAG);
#else
		write_hdl = Helios_PCM_Open(1, 8000, HELIOS_PCM_WRITE_FLAG | HELIOS_PCM_BLOCK_FLAG);
#endif
			if(write_hdl==NULL){
				AUDLOGI("pcm init failed pcm open failed\n");
				return(-1);
			}
			AUDLOGI("pcm init finished");
			helios_stream_init_flag = HELIOS_INITED;
		}
		break;
		default:
		{
			AUDLOGI("Audio Stream init failed format not supported\n");
			return (-1);
		}
		break;
		}
		AUDLOGI("Audio Stream inited ,now begin transmission\n");
		helios_stream_send_flag = HELIOS_STREAM_SEND_NOT_FINISH;
		stream_format = format;
	}

	while (total_cnt < streamsize)
	{
		if (streamsize - total_cnt > PACKET_WRITE_MAX_SIZE)
		{
			cnt = PACKET_WRITE_MAX_SIZE;
			switch (format)
			{
#if CONFIG_AUDIO_WAV
			case HELIOS_AUDIO_FORMAT_WAVPCM:
			{
				err = Helios_PCM_Write(write_hdl, (void*)buf+total_cnt, cnt);
			}
			break;
#endif
#if CONFIG_AUDIO_MP3
			case HELIOS_AUDIO_FORMAT_MP3:
			{	
				err = Helios_play_mp3_stream_start((void *)buf+total_cnt, cnt);
			}
			break;
#endif
#if CONFIG_AUDIO_AMR
			case HELIOS_AUDIO_FORMAT_AMRNB:
			{
				err = helios_amr_stream_write((void *)buf + total_cnt, cnt);
				AUDLOGI("stream more than 3k %d \n", cnt);
			}
			break;
#endif
			case HELIOS_AUDIO_FORMAT_PCM:
			{
#ifdef CONFIG_AUDIO_PWM
				err = Helios_Aud_PWM_Write(write_hdl, (void*)buf+total_cnt, cnt);
#else
				err = Helios_PCM_Write(write_hdl, (void*)buf+total_cnt, cnt);
#endif
			}
			break;

			default:
			{
				AUDLOGI("start failed,format is not supported\n");
			}
			break;
			}
		}
		else
		{
			cnt = streamsize - total_cnt;
			switch (format)
			{
			case HELIOS_AUDIO_FORMAT_WAVPCM:
			{
				
#ifdef CONFIG_AUDIO_PWM
				err = Helios_Aud_PWM_Write(write_hdl, (void*)buf+total_cnt, cnt);
#else
				err = Helios_PCM_Write(write_hdl, (void*)buf+total_cnt, cnt);
#endif
			}
			break;
#if CONFIG_AUDIO_MP3
			case HELIOS_AUDIO_FORMAT_MP3:
			{
				err = Helios_play_mp3_stream_start((void*)buf+total_cnt, cnt);
			}
			break;
#endif
#if CONFIG_AUDIO_AMR
			case HELIOS_AUDIO_FORMAT_AMRNB:
			{
				err = helios_amr_stream_write((void *)buf + total_cnt, cnt);
				AUDLOGI("stream less than 3k %d, error[%d]\n", cnt, err);
			}
			break;
#endif
			case HELIOS_AUDIO_FORMAT_PCM:
			{
#ifdef CONFIG_AUDIO_PWM
				err = Helios_Aud_PWM_Write(write_hdl, (void*)buf+total_cnt, cnt);
#else
				err = Helios_PCM_Write(write_hdl, (void*)buf+total_cnt, cnt);
#endif
			}
			break;

			default:
			{
				AUDLOGI("start failed,format is not supported");
			}
			break;
			}
		}
		if (err < 0)
		{
			AUDLOGI("stream trans failed now skip\n");
			total_cnt += cnt;
		}
		else
		{
			AUDLOGI("last sent %d bytes, already sent %d\n", cnt, total_cnt);
			total_cnt += cnt;
		}
		AUDLOGI("All sent buff length:%d , Stream length is %d\n", total_cnt, streamsize);
	}
	AUDLOGI("All buf trans finshed\n");
exit:
	if (err < 0)
		return -1;
	else
	{
		helios_stream_send_flag = HELIOS_STREAM_SEND_FINISH;
		AUDLOGI("finish stream");
		return 0;
	}
}

 int Helios_Audio_StreamPlayStop(void) 
{
	int ret = 0;
#if CONFIG_AUDIO_MP3
	Helios_play_mp3_stream_stop();
#endif

#if CONFIG_AUDIO_AMR
	helios_amr_stream_close();
#endif
#if CONFIG_AUDIO_WAV
	if(stream_format == HELIOS_AUDIO_FORMAT_WAVPCM)
	{
		ret = Helios_PCM_Close(write_hdl);
		AUDLOGI("wav close\n");
		write_hdl=NULL;
	}
#endif

	 if(stream_format == HELIOS_AUDIO_FORMAT_PCM){
#ifdef CONFIG_AUDIO_PWM
		ret = Helios_Aud_PWM_Close(write_hdl);
#else
		ret = Helios_PCM_Close(write_hdl);
#endif
		AUDLOGI("pcm close\n");
		write_hdl=NULL;
	} 
	helios_stream_init_flag = HELIOS_NOT_INITED;
	
	play_stream_user_cb = NULL;
	return ret;
 }

 typedef struct aud_input_para{
	 int channels;
	 int rate;
	 int format;
	 int hsize;
	 int type;
	 int file_size;
	 int fmt_size;
 }aud_input_para_t;

#define ID_RIFF 	0x46464952
#define ID_WAVE		0x45564157
#define ID_FMT  	0x20746d66
#define ID_DATA 	0x61746164
#define ID_IOS		0x524c4c46//wav data id by IOS
#define FORMAT_PCM 	1


 static int check_audio_format_byFileName(const char *fname)
 {
	 if (fname == NULL)
		 return -1;
 
	 char *dot = strrchr(fname, '.');
	 if (dot == NULL)
		 return -1;
 
	 if (strcasecmp(dot, ".pcm") == 0)
		 return AUD_STREAM_FORMAT_PCM;
 
	 if (strcasecmp(dot, ".mp3") == 0)
		 return AUD_STREAM_FORMAT_MP3;
 
	 if (strcasecmp(dot, ".amr") == 0)
		 return AUD_STREAM_FORMAT_AMR;
 
	 return -1;
 }


struct riff_chunk{
	unsigned int riff;
	unsigned int file_size;
	unsigned int wave;
};
struct format_chunk{
	unsigned int format;
	unsigned int format_size;
	unsigned short aud_fmt;
	unsigned short channel;
	unsigned int sample;
	unsigned int bytes;
	unsigned short block_align;
	unsigned short bits_sample;
	unsigned short others;
};
struct data_chunk{
	unsigned int data;
	unsigned int data_size;
};

 static bool is_support_samplerate(unsigned int rate)
 {
	 switch(rate) {
	 case 8000:
	 case 11025:
	 case 12000:
	 case 16000:
	 case 22050:
	 case 24000:
	 case 32000:
	 case 44100:
	 case 48000:
		 return true;
	 }
	 return false;
 }


 static int check_wav_file(HeliosAudFILE *fd, aud_input_para_t *para)
 {
	 int ret = 0,i = 0;
	 struct riff_chunk riff_hdr;
	 struct format_chunk format_hdr;
	 struct data_chunk data_hdr;
	 unsigned int chunk_id = 0,chunk_size = 0,chunk_len = 0;
	 unsigned int data = 0,data_size = 0;
	 
	 if(!para)
	 {
		 return -1;
	 }
 
	 ret = Helios_Aud_fseek(fd, 0, SEEK_SET);
	 if(ret < 0)
	 {
		 return -1;
	 }
	 ret = Helios_Aud_fread(&riff_hdr, sizeof(riff_hdr), 1, fd);						 //riff
	 if(ret < sizeof(riff_hdr))
	 {
		 Helios_Aud_fseek(fd, 0, SEEK_SET);
		 return -1;
	 }
	 if( (riff_hdr.riff != ID_RIFF) || (riff_hdr.wave!= ID_WAVE))
	 {
		 return -1;
	 }
 
	 for(i = 0;i < riff_hdr.file_size;){									 //format
		 Helios_Aud_fread(&chunk_id, sizeof(chunk_id), 1, fd);
		 if(chunk_id == ID_FMT){
			 Helios_Aud_fread(&format_hdr.format_size, sizeof(unsigned int), 1, fd);
			 format_hdr.format = chunk_id;
			 break;
		 }else{
			 Helios_Aud_fread(&chunk_size, sizeof(chunk_size), 1, fd);
			 i += (chunk_size + 8);
			 if(i > riff_hdr.file_size)
				 return -1;
			 ret = Helios_Aud_fseek(fd, chunk_size, 1);
			 if(ret < chunk_size)
				 return -1;
			 chunk_len = i;
		 }
				 
	 }
	 Helios_Aud_fread(&format_hdr.aud_fmt, format_hdr.format_size, 1, fd);
	 if(is_support_samplerate(format_hdr.sample) == false){
		 return -1;
	 }
 
 
	 for(i = 0;i < (riff_hdr.file_size-chunk_len-format_hdr.format_size);){ 		 //data
		 Helios_Aud_fread(&data, sizeof(data), 1, fd);
		 if(data == ID_DATA){
			 Helios_Aud_fread(&data_hdr.data_size, sizeof(unsigned int), 1, fd);
			 data_hdr.data = data;
			 break;
		 }else{
			 Helios_Aud_fread(&data_size, sizeof(data_size), 1, fd);
			 i += (data_size + 8);
			 if(i > riff_hdr.file_size-chunk_len-format_hdr.format_size)
				 return -1;
			 ret = Helios_Aud_fseek(fd, data_size, 1);
			 if(ret < data_size)
				 return -1;
		 }
	 }
 
	 switch (format_hdr.bits_sample)
	 {
		 case 8:
			 para->format = QUEC_PCM_FORMAT_S8;
			 break;
		 case 16:
			 para->format = QUEC_PCM_FORMAT_S16_LE;
			 break;
		 case 24:
			 para->format = QUEC_PCM_FORMAT_S24_LE;
			 break;
		 case 32:
			 para->format = QUEC_PCM_FORMAT_S32_LE;
			 break;
		 default:
			 return -1;
	 }
 
	 para->rate = format_hdr.sample;
	 para->channels = format_hdr.channel;
	 para->hsize = 4;
	 para->type = AUD_STREAM_FORMAT_PCM;
	 para->file_size = data_hdr.data_size;
 
	 return 0;	 
 }

 
 static int check_mp3_file(HeliosAudFILE *fd)
 {
	 int ret = 0;
	 char head[10] = {0};
 
	 ret = Helios_Aud_fseek(fd, 0, SEEK_SET);
	 if(ret < 0)
	 {
		 return -1;
	 }
 
	 ret = Helios_Aud_fread(head, sizeof(head), 1, fd);
	 if(ret < sizeof(head))
	 {
		 Helios_Aud_fseek(fd, 0, SEEK_SET);
		 return -1;
	 }
 
	 if(STRNICMP(head, "ID3", 3) == 0)
	 {
		 return 0;
	 }
	 else if( (head[0] == 0xFF) && (head[1] & 0xF0) == 0xF0 )
	 {
		 return 0;
	 }
	 
	 return 1;
 }
 
 static int check_amr_file(HeliosAudFILE *fd, aud_input_para_t *para)
 {
	 int bytesRead = 0;
	 int ret = 0;
	 char header[6] = {0};
	 ret = Helios_Aud_fseek(fd, 0, SEEK_SET);
	 if(ret < 0)
	 {
		 return -1;
	 }
	 // Validate the input AMR file
	 bytesRead = Helios_Aud_fread(header, 1, 6, fd);
	 if (bytesRead != 6 || memcmp(header, "#!AMR\n", 6)) {
		 AUDLOGE("check_file head error\n");
		 return -1;
	 }
 
	 return 0;
 }


 
 static int check_snd_file(HeliosAudFILE *fd, aud_input_para_t *para)
 {
	 int bytesRead = 0;
	 int ret = 0;
	 char header[4] = {0};
	 ret = Helios_Aud_fseek(fd, 0, SEEK_SET);
	 if(ret < 0)
	 {
		 return -1;
	 }
	 // Validate the input AMR file
	 bytesRead = Helios_Aud_fread(header, 1, 4, fd);
	 if (bytesRead != 4 || memcmp(header, ".snd", 4)) {
		 return -1;
	 }
	 return 0;
 }

 
 static int check_audio_format(const char *fname, HeliosAudFILE *fd, aud_input_para_t *para)
 {
	 int ret = check_audio_format_byFileName(fname);
	 if(-1 != ret) return ret;
 
	 if(!check_wav_file(fd, para))
	 {
		 AUDLOGI("Wav file: rate(%d), channels(%d), format(%d)\n",
					 para->rate, para->channels, para->format);
		 return AUD_STREAM_FORMAT_PCM;
	 }
	 else if(!check_mp3_file(fd))
	 {
		 return AUD_STREAM_FORMAT_MP3;
	 }
	 else if(!check_amr_file(fd, para))
	 {
		 return AUD_STREAM_FORMAT_AMR;
	 }
	 else if(!check_snd_file(fd, para)){
		return AUD_STREAM_FORMAT_SND;
	 }
	 else
		 return -1;
 }

 int helios_audio_play_callback(char *p_data, int len, Helios_EnumAudPlayerState state)
 {
	 AUDLOGI("audio_play_callback:event = %d\n", state);
	 Helios_EnumAudPlayerState audio_event = 0;
	 unsigned int event = 0;
	 char is_jump_pa = 0;
 
	 switch (state)
	 {
		 case HELIOS_AUD_PLAYER_ERROR:
			 break;
		 case HELIOS_AUD_PLAYER_START:
			 event = 1;
			 break;
		 case HELIOS_AUD_PLAYER_PAUSE:
			 break;
		 case HELIOS_AUD_PLAYER_RESUME:
			 event = 1;
			 break;
		 case HELIOS_AUD_PLAYER_FINISHED:
			 break;
		case HELIOS_AUD_PLAYER_NODATA_STRAT:
		case HELIOS_AUD_PLAYER_DATA_RESUME:
			is_jump_pa = 1;
		break;
		 default:
			 return -1;
	 }
	 
	 if(is_jump_pa == 0) {
#if !( defined(BOARD_EC600MCN_LF_SLPOC) )
		__audio_pa_callback(event);
#endif
	 }

	 if(play_user_cb != NULL) {
		 play_user_cb("File", len, state);
	 }
	 
	 return 0;
 }


 void helios_audio_pa_control(unsigned int onoff) {
	__audio_pa_callback(onoff);
 }

static Helios_Thread_t __audio_file_task = 0;
static Helios_Sem_t __audio_file_sem = 0;
static char __audio_play_file[256] = {0};
static int helios_audio_play_morefiles(char *files, cb_on_player cb){
	if (strncmp(files, "mp3files=", 9) == 0)
	{
		is_play = 1;
	#if CONFIG_AUDIO_MP3
		return helios_audio_mp3_play_morefiles(files, cb);
	#else
		return PLAY_MOREFILES_ERR_SINGLEFILE;
	#endif
	}
	else if (strncmp(files, "amrfiles=", 9) == 0)
	{
		is_play = 3;
		return 0;
	}
	else
	{
		/* not continue play */
		return PLAY_MOREFILES_ERR_SINGLEFILE;
	}

}


static int __Helios_file_play(void){
	HeliosAudFILE *fd = NULL;
	int ret = 0;
	
    aud_input_para_t para;
	while(1)
	{
		Helios_Semaphore_Acquire(__audio_file_sem, HELIOS_WAIT_FOREVER);
		AUDLOGI("file[%s] open play\n", __audio_play_file);

		ret = helios_audio_play_morefiles(__audio_play_file, helios_audio_play_callback);
		if (ret == PLAY_MOREFILES_ERR_OK) {
			continue;
		} else if(ret == PLAY_MOREFILES_ERR_FILENOTEXIST){
			continue;
		} else {
			//single file play
		}


        fd = Helios_Aud_fopen(__audio_play_file, "r");
        if(!fd)
        {
            AUDLOGE("Helios_Aud_fopen failed fd = %d\n", fd);
            return -1;
        }
		
		
		int ret = check_audio_format(__audio_play_file, fd, &para);
		Helios_Aud_fclose(fd);
	
		switch (ret)
		{
#if CONFIG_AUDIO_MP3
			case AUD_STREAM_FORMAT_MP3:
				is_play = 1;
				if (Helios_mp3_file_start(__audio_play_file, helios_audio_play_callback) != 0)
				{
					return -1;
				}
				break;
#endif
#if CONFIG_AUDIO_WAV
			case AUD_STREAM_FORMAT_PCM:
				is_play = 2;
				if (Helios_wav_file_start(__audio_play_file, helios_audio_play_callback) != 0)
				{
					return -1;
				}
				break;
#endif
#if CONFIG_AUDIO_AMR
			case AUD_STREAM_FORMAT_AMR:
				is_play = 3;
				if (helios_amr_file_start(__audio_play_file, helios_audio_play_callback) != 0)
				{
					return -1;
				}
				break;
#endif
#if CONFIG_AUDIO_SND
			case AUD_STREAM_FORMAT_SND:
				is_play = 4;
				if (Helios_snd_file_start(__audio_play_file, helios_audio_play_callback) != 0)
				{
					return -1;
				}
				break;
#endif
			default:
				return -1;
		}
	}
}
static void __Helios_audio_file_play_task_init(void)
 {
	 static int inited = 0;
	 size_t audio_stackSize = 1024*2;
#if defined(PLAT_EIGEN) || defined(PLAT_EIGEN_718)
	 int audio_thread_priority = 95;
#else
	 int audio_thread_priority = 74;
#endif
	 
 
	 if (!inited)
	 {
		 
		 __audio_file_sem = Helios_Semaphore_Create(1, 0);
		 assert(__audio_file_sem != 0);
	 
		 Helios_ThreadAttr attr = {0};
		 attr.name = "__Helios_file_play";
		 attr.stack_size = audio_stackSize;
		 attr.priority = audio_thread_priority;
		 attr.entry = __Helios_file_play;
 
		 __audio_file_task = Helios_Thread_Create(&attr);
		 
		 assert(__audio_file_task != 0);
 
		 inited = 1;
 
		 Helios_msleep(200);
	 }
 }


 int Helios_Audio_FilePlayStop(void);

static int Helios_Audio_Find_Files_by_name(char *file_name)
{
	int rc = 0;
	int index = 0;
	char* file_token = NULL;
	char* path_index = NULL;
	char folder_buff[256] = {0};
	char file_name_buff[256] = {0};
	char file_path_buff[256] = {0};
	int file_name_len = strlen(file_name);
	if(file_name_len < sizeof("mp3files="))
	{
		rc = helios_find_file(file_name);
		if(rc != 1)
		{
			AUDLOGE("not find %s\n", file_name);
			return -1;
		}
	}
	else
	{
		if(strncmp(file_name,"mp3files=",strlen("mp3files=")) == 0)
		{
			strcpy(file_name_buff,file_name);
			file_token = strtok(file_name_buff,"=+");
			file_token = strtok(NULL,"=+");
			//AUDLOGE("token: %s\n", file_token);
			path_index = strrchr(file_token,'/');
			if(NULL != path_index)
			{
				rc = helios_find_file(file_token);
				if(rc != 1)
				{
					AUDLOGE("not find %s\n", file_token);
					return -1;
				}
				index = path_index - file_token;
				memset(folder_buff,0,sizeof(folder_buff));
				strncpy(folder_buff,file_token,index+1);  //get folder path
				//AUDLOGE("folder_buff: %s\n", folder_buff);
			}
			while(NULL != file_token)
			{
				file_token = strtok(NULL,"=+");
				if(NULL != file_token)
				{
					sprintf(file_path_buff,"%s%s",folder_buff,file_token);  //get file path
					//AUDLOGE("file: %s\n", file_path_buff);  
					rc = helios_find_file(file_path_buff);
					if(rc != 1)
					{
						AUDLOGE("not find %s\n", file_path_buff);
						return -1;
					}					
				}
				else{
					break;
				}
			}
			
		}
		else
		{
			rc = helios_find_file(file_name);
			if(rc != 1)
			{
				AUDLOGE("not find %s\n", file_name);
				return -1;
			}
		}
	}
		
	return 0;
}

 int Helios_Audio_FilePlayStart(char *file_name, Helios_AudPlayerType type, helios_cb_on_player usr_cb)
 {
 	int ret = 0;
	 (void)type;
	 AUDLOGE("file[%s] release\n", file_name);
	 play_user_cb = usr_cb;
	 //ql_audio_play_init(helios_audio_play_callback);
	
	ret = Helios_Audio_Find_Files_by_name(file_name);
	if(0 != ret)
	{
		return -1;
	}
	
	memset(__audio_play_file, 0, sizeof(__audio_play_file));
	sprintf(__audio_play_file, "%s", file_name);

	__Helios_audio_file_play_task_init();

	Helios_Audio_FilePlayStop();

	
	
	Helios_Semaphore_Poll(__audio_file_sem, &ret);
	if (ret == 0)
	{
		AUDLOGE("file[%s] release\n", file_name);
		Helios_Semaphore_Release(__audio_file_sem);
	}
	
    return 0;
 }

/*
  * @brief:
  * 	 Stop playing the audio file.
  *
  * @param:
  * 	 NULL
  * @return:
  * 	 stop play audio file result, 0 for success,-1 for failure
  */
 int Helios_Audio_FilePlayStop(void) {
	  switch(is_play)
	  {
#if CONFIG_AUDIO_MP3
		  case 1:
			  Helios_mp3_file_stop();
			  Helios_play_mp3_stream_stop();
			  break;
#endif
#if CONFIG_AUDIO_WAV
		  case 2:
		  	Helios_wav_file_stop();
			  break;
#endif
#if CONFIG_AUDIO_AMR
		  case 3:
			  helios_amr_file_end();
			  helios_amr_stream_close();
			  break;
#endif
#if CONFIG_AUDIO_SND
		  case 4:
		  	Helios_snd_file_stop();
		  	break;
#endif
		  default:break;
	  
	  }
	 return 0;
 }

 
 /**
  * @brief:
  * 	 Audio init
  *
  * @param:
  * @return:
  * 	 audio init result, 0 for success,-1 for failure
  */
 int Helios_Audio_Init(void)
 {
	return 0;
 }



void Helios_file_jump_frame(Helios_AudStreamFormat format, uint32_t cnt) {
	switch (format)
	{
	case HELIOS_AUDIO_FORMAT_MP3:
		AUDLOGD("set mp3 jump frame[%d]\n", cnt);
	#if CONFIG_AUDIO_MP3
		Helios_mp3_file_jump_frame(cnt);
	#endif
		break;
	
	default:
		break;
	}
	
}

