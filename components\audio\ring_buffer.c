/**
 ******************************************************************************
 * @file    ring_buffer.c
 * <AUTHOR>
 * @version V1.0.0
 * @date    26-Sep-2018
 * @brief   This file contains the abstraction of quec_os.
 ******************************************************************************
 *
 *  The MIT License
 *  Copyright (c) 2014 QUECTEL Inc.
 *
 *  Permission is hereby granted, free of charge, to any person obtaining a copy
 *  of this software and associated documentation files (the "Software"), to deal
 *  in the Software without restriction, including without limitation the rights
 *  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the Software is furnished
 *  to do so, subject to the following conditions:
 *
 *  The above copyright notice and this permission notice shall be included in
 *  all copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 *  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 *  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 *  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *  WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR
 *  IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 ******************************************************************************
 */

#include "helios_include.h"
#include "ring_buffer.h"

static QuecOSStatus quec_ring_buffer_lock(ring_buffer_handler_t ring_buffer_handler)
{
	QuecOSStatus err = kNoErr;

	require_action_string(ring_buffer_handler, exit, err = kGeneralErr, "parm error");

	err = Helios_Mutex_Lock(ring_buffer_handler->mutex, (uint32)HELIOS_WAIT_FOREVER);
	require_noerr(err, exit);

exit:
	return err;
}

static QuecOSStatus quec_ring_buffer_unlock(ring_buffer_handler_t ring_buffer_handler)
{
	QuecOSStatus err = kNoErr;

	require_action_string(ring_buffer_handler, exit, err = kGeneralErr, "parm error");

	Helios_Mutex_Unlock(ring_buffer_handler->mutex);

exit:
	return err;
}

static QuecOSStatus quec_ring_buffer_lock_ex(ring_buffer_handler_t ring_buffer_handler)
{
	QuecOSStatus err = kNoErr;

	require_action_string(ring_buffer_handler, exit, err = kGeneralErr, "parm error");

	Helios_Mutex_Lock(ring_buffer_handler->mutex_ex, (uint32)HELIOS_WAIT_FOREVER);

exit:
	return err;
}

static QuecOSStatus quec_ring_buffer_unlock_ex(ring_buffer_handler_t ring_buffer_handler)
{
	QuecOSStatus err = kNoErr;

	require_action_string(ring_buffer_handler, exit, err = kGeneralErr, "parm error");

	Helios_Mutex_Unlock(ring_buffer_handler->mutex_ex);

exit:
	return err;
}

QuecOSStatus quec_ring_buffer_init(ring_buffer_handler_t *ring_buffer_handler_ptr, uint32 buffer_size)
{
	QuecOSStatus err = kNoErr;
	ring_buffer_handler_t ring_buffer_handler_tmp = NULL;
	uint8 *buffer_tmp = NULL;
	Helios_Mutex_t mutex = 0;
	Helios_Mutex_t mutex_ex = 0;

	AUDLOGI("1: \n");

	require_action_string(ring_buffer_handler_ptr && buffer_size, exit, err = kParamErr, "parm error");
	AUDLOGI("2: \n");

	ring_buffer_handler_tmp = quec_malloc(sizeof(ring_buffer_t));
	require_action_string(ring_buffer_handler_tmp != NULL, exit, err = kNoMemoryErr, "malloc fail");

	buffer_tmp = quec_malloc(buffer_size);
	require_action_string(buffer_tmp != NULL, exit, err = kNoMemoryErr, "malloc fail");
	AUDLOGI("3: \n");

	mutex = Helios_Mutex_Create();
	require_noerr(mutex == 0, exit);

	mutex_ex = Helios_Mutex_Create();
	require_noerr(mutex_ex == 0, exit);

	memset(ring_buffer_handler_tmp, 0, sizeof(ring_buffer_t));
	memset(buffer_tmp, 0, buffer_size);

	*ring_buffer_handler_ptr = ring_buffer_handler_tmp;

	ring_buffer_handler_tmp->buffer = buffer_tmp;
	ring_buffer_handler_tmp->in_pos = 0;
	ring_buffer_handler_tmp->out_pos = 0;
	ring_buffer_handler_tmp->total_size = buffer_size;
	ring_buffer_handler_tmp->used_size = 0;
	ring_buffer_handler_tmp->free_size = buffer_size;
	ring_buffer_handler_tmp->mutex = mutex;
	ring_buffer_handler_tmp->mutex_ex = mutex_ex;
	AUDLOGI("4: \n");

exit:

	if (err != kNoErr)
	{
		if (ring_buffer_handler_ptr != NULL)
			*ring_buffer_handler_ptr = NULL;

		if (ring_buffer_handler_tmp != NULL)
			quec_free(ring_buffer_handler_tmp);

		if (buffer_tmp != NULL)
			quec_free(buffer_tmp);

		if (mutex != 0)
			Helios_Mutex_Delete(mutex);

		if (mutex_ex != 0)
			Helios_Mutex_Delete(mutex_ex);
	}
	return err;
}

QuecOSStatus quec_ring_buffer_get_used_size(ring_buffer_handler_t ring_buffer_handler, uint32 *size_out)
{
	QuecOSStatus err = kNoErr;

	require_action_string(ring_buffer_handler && size_out, exit, err = kParamErr, "parm error");

	err = quec_ring_buffer_lock(ring_buffer_handler);
	require_noerr(err, exit);

	*size_out = ring_buffer_handler->used_size;

	err = quec_ring_buffer_unlock(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	return err;
}

QuecOSStatus quec_ring_buffer_get_free_size(ring_buffer_handler_t ring_buffer_handler, uint32 *size_out)
{
	QuecOSStatus err = kNoErr;

	require_action_string(ring_buffer_handler && size_out, exit, err = kParamErr, "parm error");

	err = quec_ring_buffer_lock(ring_buffer_handler);
	require_noerr(err, exit);

	*size_out = ring_buffer_handler->free_size;

	err = quec_ring_buffer_unlock(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	return err;
}

QuecOSStatus quec_ring_buffer_get_in_pos(ring_buffer_handler_t ring_buffer_handler, uint32 *in_pos_out)
{
	QuecOSStatus err = kNoErr;

	require_action_string(ring_buffer_handler && in_pos_out, exit, err = kParamErr, "parm error");

	err = quec_ring_buffer_lock(ring_buffer_handler);
	require_noerr(err, exit);

	*in_pos_out = ring_buffer_handler->in_pos;

	err = quec_ring_buffer_unlock(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	return err;
}

QuecOSStatus quec_ring_buffer_get_out_pos(ring_buffer_handler_t ring_buffer_handler, uint32 *out_pos_out)
{
	QuecOSStatus err = kNoErr;

	require_action_string(ring_buffer_handler && out_pos_out, exit, err = kParamErr, "parm error");

	err = quec_ring_buffer_lock(ring_buffer_handler);
	require_noerr(err, exit);

	*out_pos_out = ring_buffer_handler->out_pos;

	err = quec_ring_buffer_unlock(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	return err;
}

QuecOSStatus quec_ring_buffer_get_total_size(ring_buffer_handler_t ring_buffer_handler, uint32 *total_size_out)
{
	QuecOSStatus err = kNoErr;

	require_action_string(ring_buffer_handler && total_size_out, exit, err = kParamErr, "parm error");

	err = quec_ring_buffer_lock(ring_buffer_handler);
	require_noerr(err, exit);

	*total_size_out = ring_buffer_handler->total_size;

	err = quec_ring_buffer_unlock(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	return err;
}

QuecOSStatus quec_ring_buffer_write(ring_buffer_handler_t ring_buffer_handler, uint8 *data_ptr, uint32 data_size)
{
	QuecOSStatus err = kNoErr;
	uint8 *buffer = NULL;
	uint32 used_size = 0;
	uint32 free_size = 0;
	uint32 in_pos = 0;
	uint32 total_size = 0;
	uint32 to_be_copied_data_size = 0;
	uint32 alrdy_copied_data_size = 0;

	require_action_string(ring_buffer_handler && data_ptr, exit, err = kParamErr, "parm error");

	buffer = ring_buffer_handler->buffer;
	used_size = ring_buffer_handler->used_size;
	free_size = ring_buffer_handler->free_size;
	in_pos = ring_buffer_handler->in_pos;
	total_size = ring_buffer_handler->total_size;

	require_action_string(data_size <= free_size, exit, err = kNotWritableErr, "no enough space to save user data");

	err = quec_ring_buffer_lock(ring_buffer_handler);
	require_noerr(err, exit);

	/*> ring buffer memory copy mechanism begin */

	to_be_copied_data_size = ((in_pos + data_size) / total_size) * (total_size - in_pos);

	memcpy(buffer + in_pos, data_ptr + alrdy_copied_data_size, to_be_copied_data_size);

	alrdy_copied_data_size = to_be_copied_data_size;			 // refresh alrdy_copied_data_size
	in_pos = (in_pos + alrdy_copied_data_size) % total_size;	 // refresh in_pos
	to_be_copied_data_size = data_size - alrdy_copied_data_size; // refresh to_be_copied_data_size

	memcpy(buffer + in_pos, data_ptr + alrdy_copied_data_size, to_be_copied_data_size);

	alrdy_copied_data_size = to_be_copied_data_size;		 // refresh alrdy_copied_data_size
	in_pos = (in_pos + alrdy_copied_data_size) % total_size; // refresh in_pos
	used_size += data_size;									 // refresh used_size
	free_size = total_size - used_size;						 // refresh free_size

	/*> ring buffer memory copy mechanism end */

	/*> refresh info to ring buffer context */
	ring_buffer_handler->in_pos = in_pos;
	ring_buffer_handler->used_size = used_size;
	ring_buffer_handler->free_size = free_size;

	err = quec_ring_buffer_unlock(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	return err;
}

QuecOSStatus quec_ring_buffer_read(ring_buffer_handler_t ring_buffer_handler, uint8 *data_ptr, uint32 data_size)
{
	QuecOSStatus err = kNoErr;
	uint8 *buffer = NULL;
	uint32 used_size = 0;
	uint32 free_size = 0;
	uint32 out_pos = 0;
	uint32 total_size = 0;
	uint32 to_be_copied_data_size = 0;
	uint32 alrdy_copied_data_size = 0;

	require_action_string(ring_buffer_handler && data_ptr, exit, err = kParamErr, "parm error");

	buffer = ring_buffer_handler->buffer;
	used_size = ring_buffer_handler->used_size;
	free_size = ring_buffer_handler->free_size;
	out_pos = ring_buffer_handler->out_pos;
	total_size = ring_buffer_handler->total_size;

	require_action_string(data_size <= used_size, exit, err = kNotReadableErr, "no enough user data to be read");

	err = quec_ring_buffer_lock(ring_buffer_handler);
	require_noerr(err, exit);

	/*> ring buffer memory copy mechanism begin */

	to_be_copied_data_size = ((out_pos + data_size) / total_size) * (total_size - out_pos);

	memcpy(data_ptr + alrdy_copied_data_size, buffer + out_pos, to_be_copied_data_size);

	alrdy_copied_data_size = to_be_copied_data_size;			 // refresh alrdy_copied_data_size
	out_pos = (out_pos + alrdy_copied_data_size) % total_size;	 // refresh out_pos
	to_be_copied_data_size = data_size - alrdy_copied_data_size; // refresh to_be_copied_data_size

	memcpy(data_ptr + alrdy_copied_data_size, buffer + out_pos, to_be_copied_data_size);

	alrdy_copied_data_size = to_be_copied_data_size;		   // refresh alrdy_copied_data_size
	out_pos = (out_pos + alrdy_copied_data_size) % total_size; // refresh out_pos
	free_size += data_size;									   // refresh free_size
	used_size = total_size - free_size;						   // refresh used_size

	/*> ring buffer memory copy mechanism end */

	/*> refresh info of ring buffer context */
	ring_buffer_handler->out_pos = out_pos;
	ring_buffer_handler->used_size = used_size;
	ring_buffer_handler->free_size = free_size;

	err = quec_ring_buffer_unlock(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	return err;
}

QuecOSStatus quec_ring_buffer_write_ex(ring_buffer_handler_t ring_buffer_handler, uint8 *data_ptr, uint32 data_size, uint32 *actually_write_size_out)
{
	QuecOSStatus err = kNoErr;
	uint32 free_size = 0;
	uint32 actually_write_size = 0;
	bool is_locked = FALSE;

	require_action_string(ring_buffer_handler && data_ptr, exit, err = kParamErr, "parm error");

	err = quec_ring_buffer_lock_ex(ring_buffer_handler);
	require_noerr(err, exit);

	is_locked = TRUE;

	err = quec_ring_buffer_get_free_size(ring_buffer_handler, &free_size);
	require_noerr(err, exit);

	actually_write_size = (data_size <= free_size) ? data_size : free_size;

	err = quec_ring_buffer_write(ring_buffer_handler, data_ptr, actually_write_size);
	require_noerr(err, exit);

	if (actually_write_size_out)
		*actually_write_size_out = actually_write_size;

	err = quec_ring_buffer_unlock_ex(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	if (err != kNoErr)
	{
		if (is_locked == TRUE)
			quec_ring_buffer_unlock_ex(ring_buffer_handler);
	}
	return err;
}

QuecOSStatus quec_ring_buffer_read_ex(ring_buffer_handler_t ring_buffer_handler, uint8 *data_ptr, uint32 data_size, uint32 *actually_read_size_out)
{
	QuecOSStatus err = kNoErr;
	uint32 used_size = 0;
	uint32 actually_read_size = 0;
	bool is_locked = FALSE;

	require_action_string(ring_buffer_handler && data_ptr, exit, err = kParamErr, "parm error");

	err = quec_ring_buffer_lock_ex(ring_buffer_handler);
	require_noerr(err, exit);

	is_locked = TRUE;

	err = quec_ring_buffer_get_used_size(ring_buffer_handler, &used_size);
	require_noerr(err, exit);

	actually_read_size = (data_size <= used_size) ? data_size : used_size;

	err = quec_ring_buffer_read(ring_buffer_handler, data_ptr, actually_read_size);
	require_noerr(err, exit);

	if (actually_read_size_out)
		*actually_read_size_out = actually_read_size;

	err = quec_ring_buffer_unlock_ex(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	if (err != kNoErr)
	{
		if (is_locked == TRUE)
			quec_ring_buffer_unlock_ex(ring_buffer_handler);
	}
	return err;
}

QuecOSStatus quec_ring_buffer_reset(ring_buffer_handler_t ring_buffer_handler)
{
	QuecOSStatus err = kNoErr;
	bool is_locked = FALSE;

	require_action_string(ring_buffer_handler, exit, err = kParamErr, "parm error");

	err = quec_ring_buffer_lock(ring_buffer_handler);
	require_noerr(err, exit);

	is_locked = TRUE;

	memset(ring_buffer_handler->buffer, 0, ring_buffer_handler->total_size);

	ring_buffer_handler->in_pos = 0;
	ring_buffer_handler->out_pos = 0;
	ring_buffer_handler->used_size = 0;
	ring_buffer_handler->free_size = ring_buffer_handler->total_size;

	err = quec_ring_buffer_unlock(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	if (err != kNoErr)
	{
		if (is_locked == TRUE)
			quec_ring_buffer_unlock(ring_buffer_handler);
	}
	return err;
}

QuecOSStatus quec_ring_buffer_deinit(ring_buffer_handler_t *ring_buffer_handler_ptr)
{
	QuecOSStatus err = kNoErr;
	ring_buffer_handler_t ring_buffer_handler = NULL;

	require_action_string(ring_buffer_handler_ptr && *ring_buffer_handler_ptr, exit, err = kParamErr, "parm error");

	ring_buffer_handler = *ring_buffer_handler_ptr;

	if (ring_buffer_handler->buffer != NULL)
		quec_free(ring_buffer_handler->buffer);

	if (ring_buffer_handler->mutex != 0)
		Helios_Mutex_Delete(ring_buffer_handler->mutex);

	if (ring_buffer_handler->mutex_ex != 0)
		Helios_Mutex_Delete(ring_buffer_handler->mutex_ex);

	memset(ring_buffer_handler, 0, sizeof(ring_buffer_t));
	quec_free(ring_buffer_handler);
	*ring_buffer_handler_ptr = NULL;

exit:
	return err;
}

QuecOSStatus quec_ring_buffer_jump(ring_buffer_handler_t ring_buffer_handler, uint32 jump_size)
{
	QuecOSStatus err = kNoErr;
	uint8 *buffer = NULL;
	uint32 used_size = 0;
	uint32 free_size = 0;
	uint32 out_pos = 0;
	uint32 total_size = 0;
	uint32 to_be_copied_data_size = 0;
	uint32 alrdy_copied_data_size = 0;

	buffer = ring_buffer_handler->buffer;
	used_size = ring_buffer_handler->used_size;
	free_size = ring_buffer_handler->free_size;
	out_pos = ring_buffer_handler->out_pos;
	total_size = ring_buffer_handler->total_size;

	err = quec_ring_buffer_lock(ring_buffer_handler);
	require_noerr(err, exit);

	/*> ring buffer memory copy mechanism begin */

	to_be_copied_data_size = ((out_pos + jump_size) / total_size) * (total_size - out_pos);

	alrdy_copied_data_size = to_be_copied_data_size;			 // refresh alrdy_copied_data_size
	out_pos = (out_pos + alrdy_copied_data_size) % total_size;	 // refresh out_pos
	to_be_copied_data_size = jump_size - alrdy_copied_data_size; // refresh to_be_copied_data_size

	alrdy_copied_data_size = to_be_copied_data_size;		   // refresh alrdy_copied_data_size
	out_pos = (out_pos + alrdy_copied_data_size) % total_size; // refresh out_pos
	free_size += jump_size;									   // refresh free_size
	used_size = total_size - free_size;						   // refresh used_size

	/*> ring buffer memory copy mechanism end */

	/*> refresh info of ring buffer context */
	ring_buffer_handler->out_pos = out_pos;
	ring_buffer_handler->used_size = used_size;
	ring_buffer_handler->free_size = free_size;

	err = quec_ring_buffer_unlock(ring_buffer_handler);
	require_noerr(err, exit);

exit:
	return err;
}
