
#include "quec_decoder_config.h"
#include <stdlib.h>     /* malloc, free, abs */
#include <string.h>     /* memset */

#include <quec_decoder.h>
#include "ql_zo_malloc.h" 

#ifdef DEBUG_SCANNER
#define DEBUG_LEVEL (DEBUG_SCANNER)
#endif
//#include "debug.h"

#ifndef QUEC_DECODER_FIXED
# define QUEC_DECODER_FIXED 5
#endif
#define ROUND (1 << (QUEC_DECODER_FIXED - 1))

/* FIXME add runtime config API for these */
#ifndef QUEC_DECODER_SCANNER_THRESH_MIN
# define QUEC_DECODER_SCANNER_THRESH_MIN  4
#endif

#ifndef QUEC_DECODER_SCANNER_THRESH_INIT_WEIGHT
# define QUEC_DECODER_SCANNER_THRESH_INIT_WEIGHT .3
#endif
#define THRESH_INIT ((unsigned)((QUEC_DECODER_SCANNER_THRESH_INIT_WEIGHT       \
                                 * (1 << (QUEC_DECODER_FIXED + 1)) + 1) / 2))

#ifndef QUEC_DECODER_SCANNER_THRESH_FADE
# define QUEC_DECODER_SCANNER_THRESH_FADE 8
#endif

#ifndef QUEC_DECODER_SCANNER_EWMA_WEIGHT
# define QUEC_DECODER_SCANNER_EWMA_WEIGHT .78
#endif
#define EWMA_WEIGHT ((unsigned)((QUEC_DECODER_SCANNER_EWMA_WEIGHT              \
                                 * (1 << (QUEC_DECODER_FIXED + 1)) + 1) / 2))

/* scanner state */
struct quec_decoder_scanner_s {
    quec_decoder_decoder_t *decoder; /* associated bar width decoder */
    unsigned y1_min_thresh; /* minimum threshold */

    unsigned x;             /* relative scan position of next sample */
    int y0[4];              /* short circular buffer of average intensities */

    int y1_sign;            /* slope at last crossing */
    unsigned y1_thresh;     /* current slope threshold */

    unsigned cur_edge;      /* interpolated position of tracking edge */
    unsigned last_edge;     /* interpolated position of last located edge */
    unsigned width;         /* last element width */
};

quec_decoder_scanner_t *quec_decoder_scanner_create (quec_decoder_decoder_t *dcode)
{
    quec_decoder_scanner_t *scn = malloc(sizeof(quec_decoder_scanner_t));
    //quec_decoder_scanner_t *scn = zo_mymalloc(sizeof(quec_decoder_scanner_t));//mymalloc_test
    
    scn->decoder = dcode;
    scn->y1_min_thresh = QUEC_DECODER_SCANNER_THRESH_MIN;
    quec_decoder_scanner_reset(scn);
    return(scn);
}

void quec_decoder_scanner_destroy (quec_decoder_scanner_t *scn)
{
    free(scn);
    //zo_myfree(scn);//mymalloc_test
}

quec_decoder_symbol_type_t quec_decoder_scanner_reset (quec_decoder_scanner_t *scn)
{
    memset(&scn->x, 0, sizeof(quec_decoder_scanner_t) + (void*)scn - (void*)&scn->x);
    scn->y1_thresh = scn->y1_min_thresh;
    if(scn->decoder)
        quec_decoder_decoder_reset(scn->decoder);
    return(QUEC_DECODER_NONE);
}

unsigned quec_decoder_scanner_get_width (const quec_decoder_scanner_t *scn)
{
    return(scn->width);
}

unsigned quec_decoder_scanner_get_edge (const quec_decoder_scanner_t *scn,
                                unsigned offset,
                                int prec)
{
    unsigned edge = scn->last_edge - offset - (1 << QUEC_DECODER_FIXED) - ROUND;
    prec = QUEC_DECODER_FIXED - prec;
    if(prec > 0)
        return(edge >> prec);
    else if(!prec)
        return(edge);
    else
        return(edge << -prec);
}

quec_decoder_color_t quec_decoder_scanner_get_color (const quec_decoder_scanner_t *scn)
{
    return((scn->y1_sign <= 0) ? QUEC_DECODER_SPACE : QUEC_DECODER_BAR);
}

static inline unsigned calc_thresh (quec_decoder_scanner_t *scn)
{
    /* threshold 1st to improve noise rejection */
    unsigned thresh = scn->y1_thresh;
    if((thresh <= scn->y1_min_thresh) || !scn->width) {
        //dprintf(1, " tmin=%d", scn->y1_min_thresh);
        return(scn->y1_min_thresh);
    }
    /* slowly return threshold to min */
    unsigned dx = (scn->x << QUEC_DECODER_FIXED) - scn->last_edge;
    unsigned long t = thresh * dx;
    t /= scn->width;
    t /= QUEC_DECODER_SCANNER_THRESH_FADE;
    //dprintf(1, " thr=%d t=%ld x=%d last=%d.%d (%d)",
            //thresh, t, scn->x, scn->last_edge >> QUEC_DECODER_FIXED,
            //scn->last_edge & ((1 << QUEC_DECODER_FIXED) - 1), dx);
    if(thresh > t) {
        thresh -= t;
        if(thresh > scn->y1_min_thresh)
            return(thresh);
    }
    scn->y1_thresh = scn->y1_min_thresh;
    return(scn->y1_min_thresh);
}

static inline quec_decoder_symbol_type_t process_edge (quec_decoder_scanner_t *scn,
                                               int y1, unsigned char flag)
{
    if(!scn->y1_sign)
        scn->last_edge = scn->cur_edge = (1 << QUEC_DECODER_FIXED) + ROUND;
    else if(!scn->last_edge)
        scn->last_edge = scn->cur_edge;

    scn->width = scn->cur_edge - scn->last_edge;
    scn->last_edge = scn->cur_edge;

    /* pass to decoder */
    if(scn->decoder)
        return(quec_decoder_decode_width(scn->decoder, scn->width, flag));
    return(QUEC_DECODER_PARTIAL);
}

inline quec_decoder_symbol_type_t quec_decoder_scanner_flush(quec_decoder_scanner_t *scn, unsigned char flag)
{
    if(!scn->y1_sign)
        return(QUEC_DECODER_NONE);

    unsigned x = (scn->x << QUEC_DECODER_FIXED) + ROUND;

    if(scn->cur_edge != x || scn->y1_sign > 0) {
        //dprintf(1, "flush0:");
        quec_decoder_symbol_type_t edge = process_edge(scn, -scn->y1_sign, flag);
        scn->cur_edge = x;
        scn->y1_sign = -scn->y1_sign;
        return(edge);
    }

    scn->y1_sign = scn->width = 0;
    if(scn->decoder)
        return(quec_decoder_decode_width(scn->decoder, 0, flag));
    return(QUEC_DECODER_PARTIAL);
}

quec_decoder_symbol_type_t quec_decoder_scanner_new_scan(quec_decoder_scanner_t *scn, unsigned char flag)
{
    quec_decoder_symbol_type_t edge = QUEC_DECODER_NONE;
    while(scn->y1_sign) {
		//RTI_LOG("scan test");//chaosadd
        quec_decoder_symbol_type_t tmp = quec_decoder_scanner_flush(scn, flag);
        if(tmp < 0 || tmp > edge)
            edge = tmp;
    }

    /* reset scanner and associated decoder */
    memset(&scn->x, 0, sizeof(quec_decoder_scanner_t) + (void*)scn - (void*)&scn->x);
    scn->y1_thresh = scn->y1_min_thresh;
    if(scn->decoder)
    {
    	quec_decoder_decoder_new_scan(scn->decoder);
    }
    
    return(edge);
}

quec_decoder_symbol_type_t quec_decoder_scan_y (quec_decoder_scanner_t *scn,
                                int y, unsigned char flag)
{
    /* FIXME calc and clip to max y range... */
    /* retrieve short value history */
    register int x = scn->x;
    register int y0_1 = scn->y0[(x - 1) & 3];
    register int y0_0 = y0_1;
    if(x) {
        /* update weighted moving average */
        y0_0 += ((int)((y - y0_1) * EWMA_WEIGHT)) >> QUEC_DECODER_FIXED;
        //y0_0 += ((int)((y - y0_1)) * 0.8);
        //y0_0 = y;
        scn->y0[x & 3] = y0_0;
    }
    else
        y0_0 = y0_1 = scn->y0[0] = scn->y0[1] = scn->y0[2] = scn->y0[3] = y;
    register int y0_2 = scn->y0[(x - 2) & 3];
    register int y0_3 = scn->y0[(x - 3) & 3];
    /* 1st differential @ x-1 */
    register int y1_1 = y0_1 - y0_2;
    {
        register int y1_2 = y0_2 - y0_3;
        if((abs(y1_1) < abs(y1_2)) &&
           ((y1_1 >= 0) == (y1_2 >= 0)))
            y1_1 = y1_2;
    }

    /* 2nd differentials @ x-1 & x-2 */
    register int y2_1 = y0_0 - (y0_1 * 2) + y0_2;
    register int y2_2 = y0_1 - (y0_2 * 2) + y0_3;

    //dprintf(1, "scan: x=%d y=%d y0=%d y1=%d y2=%d",
            //x, y, y0_1, y1_1, y2_1);

    quec_decoder_symbol_type_t edge = QUEC_DECODER_NONE;
    /* 2nd zero-crossing is 1st local min/max - could be edge */
    if((!y2_1 ||
        ((y2_1 > 0) ? y2_2 < 0 : y2_2 > 0)) &&
       (calc_thresh(scn) <= abs(y1_1)))
    {
        /* check for 1st sign change */
        char y1_rev = (scn->y1_sign > 0) ? y1_1 < 0 : y1_1 > 0;
        if(y1_rev)
            /* intensity change reversal - finalize previous edge */
            edge = process_edge(scn, y1_1, flag);

        if(y1_rev || (abs(scn->y1_sign) < abs(y1_1))) {
            scn->y1_sign = y1_1;

            /* adaptive thresholding */
            /* start at multiple of new min/max */
            scn->y1_thresh = (abs(y1_1) * THRESH_INIT + ROUND) >> QUEC_DECODER_FIXED;
            //dprintf(1, "\tthr=%d", scn->y1_thresh);
            if(scn->y1_thresh < scn->y1_min_thresh)
                scn->y1_thresh = scn->y1_min_thresh;

            /* update current edge */
            int d = y2_1 - y2_2;
            scn->cur_edge = 1 << QUEC_DECODER_FIXED;
            if(!d)
                scn->cur_edge >>= 1;
            else if(y2_1)
                /* interpolate zero crossing */
                scn->cur_edge -= ((y2_1 << QUEC_DECODER_FIXED) + 1) / d;
            scn->cur_edge += x << QUEC_DECODER_FIXED;
            //dprintf(1, "\n");
        }
    }
    //else
        //dprintf(1, "\n");
    /* FIXME add fall-thru pass to decoder after heuristic "idle" period
       (eg, 6-8 * last width) */
    scn->x = x + 1;
    return(edge);
}

/* undocumented API for drawing cutesy debug graphics */
void quec_decoder_scanner_get_state (const quec_decoder_scanner_t *scn,
                             unsigned *x,
                             unsigned *cur_edge,
                             unsigned *last_edge,
                             int *y0,
                             int *y1,
                             int *y2,
                             int *y1_thresh)
{
    register int y0_0 = scn->y0[(scn->x - 1) & 3];
    register int y0_1 = scn->y0[(scn->x - 2) & 3];
    register int y0_2 = scn->y0[(scn->x - 3) & 3];
    if(x) *x = scn->x - 1;
    if(cur_edge) *cur_edge = scn->cur_edge;
    if(last_edge) *last_edge = scn->last_edge;
    if(y0) *y0 = y0_1;
    if(y1) *y1 = y0_1 - y0_2;
    if(y2) *y2 = y0_0 - (y0_1 * 2) + y0_2;
    /* NB not quite accurate (uses updated x) */
    quec_decoder_scanner_t *mut_scn = (quec_decoder_scanner_t*)scn;
    if(y1_thresh) *y1_thresh = calc_thresh(mut_scn);
    //dprintf(1, "\n");
}
