#include <stdio.h>
#include <stdlib.h>

#include "helios_os.h"
#include "helios_audio_py.h"
#include "watermark.h"

#include "helios_include.h"
#include "helios_audio_fs.h"
#include "amrdecode.h"
#include "frame_type_3gpp.h"
#include "sp_dec.h"
#include "helios_audio_amr.h"

#if defined(CONFIG_POC_QS_R07)
#include "amrnb_enc_api.h"

amrnb_enc_config amr_enc_config = { 0 };
amrnb_enc_handle amr_enc_handle = 0;
static int helios_amr_record_dtx_enable = 0;
#endif
/*==========================================================*/
/*                  ql amr interfaces start                 */
/*==========================================================*/
// #include "gsmamr_dec.h"
//  Constants for AMR-NB

enum
{
	kInputBufferSize = 64,
	kSamplesPerFrame = 160,
	kBitsPerSample = 16,
	kOutputBufferSize = kSamplesPerFrame * kBitsPerSample / 8,
	kSampleRate = 8000,
	kChannels = 1,
	kFileHeaderSize = 6
};
static const uint32_t kFrameSizes[] = {12, 13, 15, 17, 19, 20, 26, 31};
#define AMR_STREAM_WM_BUF_SIZE (1024*4)

#define AMR_DEC_CONTINUE (0x01)
#define AMR_WRITE_CONTINUE (0x02)
#define AMR_DEC_STOP (0x04)
#define AMR_FLAG_MASK (AMR_DEC_CONTINUE | AMR_WRITE_CONTINUE | AMR_DEC_STOP)

static int is_amr = 0;

cb_on_player aud_play_cb = NULL;

static bool _audio_play_state = 0;


#if (defined(PLAT_ASR_1803s) || defined(PLAT_ASR_1803sc) || defined(BOARD_EC600MCN_CC_EXT) || defined(BOARD_EC600MCN_LF_SLPOC) \
    ||defined(PLAT_Unisoc_8850) || defined(PLAT_Unisoc_8850_R02)|| defined(BOARD_EC600MCN_CC_EXT_TTS) || defined(BOARD_EC800MCN_CC_TTS) \
    || defined(BOARD_EC800KCN_CC_TTS) || defined(BOARD_EC600KCN_CC_TTS) || defined(PLAT_ASR_1609) || defined(BOARD_EC800MCN_LF_CCG)) \
	|| defined(BOARD_EC600MCN_LF_SPISD)

#define USR_HELIOS_AUDIO 1

#endif


struct helios_arm_stream_cxt
{
	wm_handle_t wm;
	Helios_OSFlag_t flag_ref;
	void *stack_ptr;
	Helios_Thread_t task_ref;
	void* pcm_whdl;
	void *amr_dec_hdl;
	bool is_playing;
	bool is_write_wait;
	bool is_dec_start;
	Helios_Mutex_t playflag_mux;
};

static struct helios_arm_stream_cxt g_arm_stream_cxt = {0};

int helios_find_file(char *name)
{
	HeliosAudFILE *fileID = NULL;

	fileID = Helios_Aud_fopen(name, "r");
	if (fileID)
	{
		Helios_Aud_fclose(fileID);

		return 1;
	}
	return 0;
}

static int amr_vm_init(void)
{
	QuecOSStatus status;

	status = quec_wm_init(&g_arm_stream_cxt.wm,
						  AMR_STREAM_WM_BUF_SIZE,
						  AMR_STREAM_WM_BUF_SIZE,
						  AMR_STREAM_WM_BUF_SIZE,
						  NULL,
						  NULL,
						  NULL);

	if (status != kNoErr)
	{
		AUDLOGE("amr_vm_init, error, watermark init failed, status: %d\n", status);
		return -1;
		;
	}

	return 0;
}

static int amr_vm_deinit(void)
{
	quec_wm_deinit(&g_arm_stream_cxt.wm);
	return 0;
}


#ifdef USR_HELIOS_AUDIO
static void __pcm_play_cb(void *handle, int event){
	AUDLOGE("__pcm_play_cb event[%d]\n",event);

	
	if(event == HELIOS_AUD_PLAYER_NODATA_STRAT 
	|| event == HELIOS_AUD_PLAYER_FINISHED 
	|| event == HELIOS_AUD_PLAYER_CLOSE 
#if 0//ifdef BOARD_EC800MCN_LF_CCG
	|| ((event == HELIOS_AUD_PLAYER_REQURST_CLOSE) && (helios_stream_init_flag == HELIOS_NOT_INITED))) // elian.wang 2024.02.23 博大项目流播音频时音频数据播放结束之后不关闭codec 
#else 
	|| event == HELIOS_AUD_PLAYER_REQURST_CLOSE)
#endif
	{
		if(g_arm_stream_cxt.pcm_whdl) {
			Helios_PCM_Close(g_arm_stream_cxt.pcm_whdl);
			g_arm_stream_cxt.pcm_whdl = NULL;
		}
	}

	if(event == HELIOS_AUD_PLAYER_FINISHED || event == HELIOS_AUD_PLAYER_CLOSE) {
		_audio_play_state = 0;
	}
	
	if(aud_play_cb != NULL) {
		aud_play_cb("stream", 0, event);
	}
}
#endif


static void amr_dec_task(void *arg)
{
	int ret = 0;
	QuecOSStatus status;

	void *inputBuf = NULL;
	void *outputBuf = NULL;
	unsigned int event = 0;
	unsigned int bytesRead = 0;
	uint32_t retVal = 0, wm_left_size = 0;
	uint8_t mode = 0;
	enum Frame_Type_3GPP frameType;
	int32_t frameSize = 0;
	unsigned char pAmrInputBuf[kInputBufferSize];
	unsigned char pAmrOutputBuf[kOutputBufferSize];

	inputBuf = pAmrInputBuf;
	outputBuf = pAmrOutputBuf;
	memset(pAmrOutputBuf, 0, kOutputBufferSize);
	int _count = 100;
	while (1)
	{
		g_arm_stream_cxt.is_dec_start = false;
		_count = 100;

		AUDLOGD("amr dec start\n");
		Helios_Flag_Wait(g_arm_stream_cxt.flag_ref, AMR_DEC_CONTINUE, Helios_FLAG_OR_CLEAR, &event, HELIOS_WAIT_FOREVER);
		AUDLOGD("amr dec start samaphore get\n");

		g_arm_stream_cxt.is_dec_start = true;

		if(g_arm_stream_cxt.pcm_whdl == NULL) {

#ifdef USR_HELIOS_AUDIO
			while(_audio_play_state == 1 && _count-- > 1){
				AUDLOGE("audio1 is playing, waiting[%d]...\n",_count);
				Helios_msleep(20);
			}
#endif
		
#ifdef CONFIG_AUDIO_PWM
			g_arm_stream_cxt.pcm_whdl = Helios_Aud_PWM_Open(CONFIG_AUDIO_PWM_PIN, 1, 8000, QL_PCM_WRITE_FLAG | QL_PCM_BLOCK_FLAG);
#else
			g_arm_stream_cxt.pcm_whdl = Helios_PCM_Open(1, 8000, QL_PCM_WRITE_FLAG | QL_PCM_BLOCK_FLAG);
#ifdef USR_HELIOS_AUDIO
			Helios_PCM_Play_Cb(g_arm_stream_cxt.pcm_whdl, __pcm_play_cb);
			_audio_play_state = 1;
#endif
#endif
		}


		AUDLOGD("pcm_whdl[%x]\n", g_arm_stream_cxt.pcm_whdl);

		//Helios_sleep(1);

		if (g_arm_stream_cxt.pcm_whdl == NULL)
		{
			AUDLOGE("pcm_whdl is NULL");
			continue;
		}
		Speech_Decode_Frame_reset(g_arm_stream_cxt.amr_dec_hdl);

		if (is_amr == 0)
		{
			if (aud_play_cb)
			{
#ifndef USR_HELIOS_AUDIO
				aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_START);
#endif
			}
		}
		while (g_arm_stream_cxt.is_playing)
		{
		start:
			quec_wm_cnt(&g_arm_stream_cxt.wm, &wm_left_size);

			AUDLOGD("==wm_left_size[%d]\n", wm_left_size);
			if (wm_left_size < 13)
			{
				break;
			}

			// Read mode
			status = quec_wm_read(&g_arm_stream_cxt.wm, &mode, 1, &bytesRead, NULL);

			// AUDLOGI("==bytesRead[%d] status[%d]\n", bytesRead, status);
			if (bytesRead != 1)
			{
				AUDLOGE("amr_dec_task \n", bytesRead);
				break;
			}
			if (status != kNoErr)
			{
				AUDLOGE("status[%d] \n", status);
				break;
			}

#if 0
				uart_printf("TEST print mode frameType:%d\n", mode);    //igni test
#endif

			// Find frame type
			frameType = (enum Frame_Type_3GPP)((mode >> 3) & 0x0f);
			if (frameType >= AMR_SID)
			{
				// fprintf(stderr, "Frame type %d not supported\n",frameType)
				AUDLOGE("amr_dec_task amrnb_dec_frameType_error frameType:%d\n", frameType);
				goto start;
			}
			// Find frame type
			frameSize = kFrameSizes[frameType];

			quec_wm_cnt(&g_arm_stream_cxt.wm, &wm_left_size);
			if (wm_left_size < frameSize)
			{
				AUDLOGE("==wm_left_size[%d] frameSize[%d]\n", wm_left_size, frameSize);
				break;
			}

			status = quec_wm_read(&g_arm_stream_cxt.wm, inputBuf, frameSize, &bytesRead, NULL);
			if (bytesRead != frameSize)
			{
				AUDLOGE("==bytesRead[%d] frameSize[%d] status[%d]\n", bytesRead, frameSize, status);
				break;
			}
			if (status != kNoErr)
			{
				AUDLOGI("status[%d]\n", status);
				break;
			}
			if (g_arm_stream_cxt.is_write_wait)
			{
				AUDLOGD("sem release 1\n");
				Helios_Flag_Release(g_arm_stream_cxt.flag_ref, AMR_WRITE_CONTINUE, Helios_FLAG_OR);
			}

			// Decode frame
			int32_t decodeStatus;
			decodeStatus = AMRDecode(g_arm_stream_cxt.amr_dec_hdl, frameType, (uint8_t *)inputBuf,
									 (int16_t *)outputBuf, MIME_IETF);

			if (decodeStatus == -1)
			{
				AUDLOGW("amrnb_dec_decode_error amrnb_dec_decode_error\n");
				break;
			}
#ifdef CONFIG_AUDIO_PWM
			ret = Helios_Aud_PWM_Write(g_arm_stream_cxt.pcm_whdl, outputBuf, kOutputBufferSize);
#else
			ret = Helios_PCM_Write(g_arm_stream_cxt.pcm_whdl, outputBuf, kOutputBufferSize);
#endif
			if (ret < 0)
			{
				AUDLOGE("kOutputBufferSize[%d] ret[%d]\n", kOutputBufferSize, ret);
				break;
			}
		}
		AUDLOGI("amr_dec_task stop\n");
#if defined(PLAT_Unisoc_8850) || defined(PLAT_Unisoc_8850_R02)
		if (g_arm_stream_cxt.is_playing == 0) {
			Helios_PCM_buffer_Reset(g_arm_stream_cxt.pcm_whdl);
		}
#endif
		Helios_Mutex_Lock(g_arm_stream_cxt.playflag_mux, HELIOS_WAIT_FOREVER);
		Helios_msleep(32);
#ifdef CONFIG_AUDIO_PWM
		Helios_Aud_PWM_Close(g_arm_stream_cxt.pcm_whdl);
#else
#if defined(PLAT_Unisoc_8850) || defined(PLAT_Unisoc_8850_R02)
		void Helios_aud_data_done(void);
		Helios_aud_data_done();
#endif
#ifndef USR_HELIOS_AUDIO
		Helios_PCM_Close(g_arm_stream_cxt.pcm_whdl);
		g_arm_stream_cxt.pcm_whdl = NULL;
#endif
#endif
		quec_wm_reset(&g_arm_stream_cxt.wm);
		g_arm_stream_cxt.is_playing = false;
		if (g_arm_stream_cxt.is_write_wait)
		{
			Helios_Flag_Release(g_arm_stream_cxt.flag_ref, AMR_DEC_STOP, Helios_FLAG_OR);
		}
		if (is_amr == 0)
		{
			// avoid twice audio callback
			is_amr = -1;
			if (aud_play_cb)
			{
#ifndef USR_HELIOS_AUDIO
				aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_FINISHED);
#endif
			}
		}
		else
			is_amr = 0;

		Helios_Mutex_Unlock(g_arm_stream_cxt.playflag_mux);
	}
}

static int arm_dec_task_init()
{
	OSA_STATUS status;
	QuecOSStatus quecstatus;
	int retval = -1;

	g_arm_stream_cxt.flag_ref = Helios_Flag_Create();
	if (g_arm_stream_cxt.flag_ref == 0)
	{
		AUDLOGE("arm_dec_task_init, error, create flag_ref failed\n");
		return -1;
	}

	// Create AMR-NB decoder instance
	int err = GSMInitDecode(&g_arm_stream_cxt.amr_dec_hdl, (int8_t *)"AMRNBDecoder");
	if (err != 0)
	{
		AUDLOGE("arm_dec_task_init amrnb_dec_error3 err:%d\n", err);
		retval = -1;
		goto GSMInitDecode_fail;
	}

	g_arm_stream_cxt.stack_ptr = malloc(2); //(1024 * 6);
	if (g_arm_stream_cxt.stack_ptr == NULL)
	{
		AUDLOGE("arm_dec_task_init stack malloc error!!\n");
		retval = -3;
		goto stack_malloc_fail;
	}

	g_arm_stream_cxt.playflag_mux = Helios_Mutex_Create();
	if (g_arm_stream_cxt.playflag_mux == 0)
	{
		AUDLOGE("arm_dec_task_init Helios_Mutex_Create error!!\n");
		retval = -5;
		goto OSATaskCreate_fail;
	}

	Helios_ThreadAttr attr = {0};
	attr.name = "amr_dec_task";
	attr.stack_size = 1024 * 6;
#if defined(PLAT_EIGEN) || defined(PLAT_EIGEN_718)
	attr.priority = 90;
#else
	attr.priority = 75;
#endif
	attr.entry = amr_dec_task;

	g_arm_stream_cxt.task_ref = Helios_Thread_Create(&attr);
	if (g_arm_stream_cxt.task_ref == 0)
	{
		AUDLOGE("arm_dec_task_init Helios_Thread_Create error!!\n");
		retval = -4;
		goto OSATaskCreate_fail;
	}
	return 0;

OSATaskCreate_fail:
	free(g_arm_stream_cxt.stack_ptr);
	g_arm_stream_cxt.stack_ptr = NULL;

OSAMutexCreate_fail:
	if (g_arm_stream_cxt.playflag_mux)
	{
		Helios_Mutex_Delete(g_arm_stream_cxt.playflag_mux);
		g_arm_stream_cxt.playflag_mux = 0;
	}

stack_malloc_fail:
	g_arm_stream_cxt.pcm_whdl = NULL;
	GSMDecodeFrameExit(&g_arm_stream_cxt.amr_dec_hdl);
	g_arm_stream_cxt.amr_dec_hdl = NULL;

GSMInitDecode_fail:
	Helios_Flag_delete(g_arm_stream_cxt.flag_ref);

	return retval;
}

static void arm_dec_task_deinit()
{
	Helios_Flag_Release(g_arm_stream_cxt.flag_ref, AMR_DEC_STOP, Helios_FLAG_OR);
	Helios_Thread_Delete(g_arm_stream_cxt.task_ref);
#ifdef CONFIG_AUDIO_PWM
	Helios_Aud_PWM_Close(g_arm_stream_cxt.pcm_whdl);
#else
	Helios_PCM_Close(g_arm_stream_cxt.pcm_whdl);
#endif
	GSMDecodeFrameExit(&g_arm_stream_cxt.amr_dec_hdl);
	Helios_Flag_delete(g_arm_stream_cxt.flag_ref);
	free(g_arm_stream_cxt.stack_ptr);

	if (g_arm_stream_cxt.playflag_mux)
	{
		Helios_Mutex_Delete(g_arm_stream_cxt.playflag_mux);
		g_arm_stream_cxt.playflag_mux = 0;
	}
}

int helios_amr_stream_open()
{
	return 0;
}

int helios_amr_stream_write(void *data, unsigned int count)
{
	unsigned int event = 0;
	QuecOSStatus status;
	unsigned int actual_write_count = 0;
	unsigned int tmp_write_count = 0;
	unsigned int left_count = count;
	uint8_t msg = 1;
	int ret = -1;
	static int amr_stream_open_start = 1;
	int _count = 100;
	if (amr_stream_open_start)
	{

		ret = amr_vm_init();
		if (ret != 0)
			return ret;

		ret = arm_dec_task_init();
		if (ret != 0)
		{
			amr_vm_deinit();
			return ret;
		}
		amr_stream_open_start = 0;
	}

#ifdef USR_HELIOS_AUDIO	
	while(_audio_play_state == 1 && g_arm_stream_cxt.pcm_whdl == NULL && _count-- > 1) {
		AUDLOGE("audio is playing, waiting[%d]...\n",_count);
		Helios_msleep(20);
	}
#endif

	Helios_Mutex_Lock(g_arm_stream_cxt.playflag_mux, HELIOS_WAIT_FOREVER);
	g_arm_stream_cxt.is_playing = true;
	Helios_Mutex_Unlock(g_arm_stream_cxt.playflag_mux);

	while (g_arm_stream_cxt.is_playing)
	{
		status = quec_wm_write(&g_arm_stream_cxt.wm, data + actual_write_count, left_count, &tmp_write_count, NULL);

		AUDLOGD("status %d=======================\n", status);
		if (status != kNoErr)
		{
			return -1;
		}
		if (g_arm_stream_cxt.is_dec_start == false)
		{
			
			AUDLOGD("sem release 3\n");
			Helios_Flag_Release(g_arm_stream_cxt.flag_ref,  AMR_DEC_CONTINUE, Helios_FLAG_OR);
		}
		actual_write_count += tmp_write_count;
		left_count -= tmp_write_count;
		if (actual_write_count < count)
		{
			event = 0;
			g_arm_stream_cxt.is_write_wait = true;
			
			Helios_Flag_Wait(g_arm_stream_cxt.flag_ref, AMR_WRITE_CONTINUE|AMR_DEC_STOP, Helios_FLAG_OR_CLEAR, &event, 400);

			g_arm_stream_cxt.is_write_wait = false;
			if (event & AMR_DEC_STOP)
			{
				return actual_write_count;
			}
			else
			{
				continue;
			}
		}
		return actual_write_count;
	}
	return actual_write_count;
}
int helios_amr_stream_close()
{
	AUDLOGD("helios_amr_stream_close !!!\n");
	if (!g_arm_stream_cxt.is_playing)
	{
		AUDLOGE("helios_amr_stream_close error, is not playing\n");
		return -1;
	}
	g_arm_stream_cxt.is_playing = false;
	return 0;
}

void helios_amr_stream_drain(void)
{
	unsigned char amr_buffer[960] = {0};
	helios_amr_stream_write(amr_buffer, sizeof(amr_buffer));
	Helios_msleep(16);
}
////////////////////////////////////////////////////
/* amr file interfaces */
////////////////////////////////////////////////////

struct ql_amr_file_cxt
{
	cb_on_player aud_play_cb;
	// void* 		stack_ptr;
	// OSATaskRef 	task_ref;
	HeliosAudFILE *fd;
	Helios_OSFlag_t flag_ref;
	bool is_playing;
	bool is_pause;
};
static struct ql_amr_file_cxt g_amr_file_cxt;

#define AMR_FILE_CONTINUE (0x01)
#define AMR_FILE_PAUSE (0x02)
#define AMR_FILE_EXIT (0x04)
#define AMR_FILE_MASK (AMR_FILE_CONTINUE | AMR_FILE_PAUSE | AMR_FILE_EXIT)

#define AMR_FILE_BUFF 1024
static unsigned char tmp[AMR_FILE_BUFF] = {0};
int helios_amr_file_start(char *file_name, cb_on_player aud_cb)
{
	int bytesRead = 0, bytesWrite = 0;
	char header[kFileHeaderSize];
	unsigned int event = 0;
	
	QuecOSStatus quecstatus;
	memset(tmp, 0, sizeof(tmp));

	if (g_amr_file_cxt.is_playing)
	{
		AUDLOGE("ql_amr_file_start is playing!\n");
		return -1;
	}

	if (!helios_find_file(file_name))
	{
		AUDLOGE("ql_amr_file_start file name error!\n");
		return -1;
	}

	if (aud_cb)
	{
		g_amr_file_cxt.aud_play_cb = aud_cb;
	}

	aud_play_cb = aud_cb;

	g_amr_file_cxt.fd = Helios_Aud_fopen(file_name, "r");
	if (g_amr_file_cxt.fd == NULL)
	{
		AUDLOGE("ql_amr_file_start Helios_Aud_fopen failed\n");
		return -1;
	}
	// Validate the input AMR file
	bytesRead = Helios_Aud_fread(header, kFileHeaderSize, 1, g_amr_file_cxt.fd);
	AUDLOGI("bytesRead[%d] header[%s] headersize[%d]!\n", bytesRead, header, kFileHeaderSize);
	if (bytesRead != kFileHeaderSize || memcmp(header, "#!AMR\n", kFileHeaderSize))
	{
		AUDLOGE("ql_amr_file_start amrnb_dec_readHeader_error bytesRead:%d\n", bytesRead);
		Helios_Aud_fclose(g_amr_file_cxt.fd);
		return -1;
	}

	g_amr_file_cxt.flag_ref = Helios_Flag_Create();
	if (g_amr_file_cxt.flag_ref == 0)
	{
		AUDLOGE("arm_file_task_init, error, create flag_ref failed, status: %d\n");
		Helios_Aud_fclose(g_amr_file_cxt.fd);
		return -1;
	}
	g_amr_file_cxt.is_playing = true;

	helios_amr_stream_open();

	while (g_amr_file_cxt.is_playing)
	{
		bytesRead = Helios_Aud_fread(tmp, AMR_FILE_BUFF, 1, g_amr_file_cxt.fd);
		if (bytesRead <= 0)
		{
			//if (g_amr_file_cxt.aud_play_cb)
			//	g_amr_file_cxt.aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_FINISHED);
			break;
		}
		bytesWrite = helios_amr_stream_write(tmp, bytesRead);
		if (bytesWrite < 0)
		{
			if (g_amr_file_cxt.aud_play_cb)
				g_amr_file_cxt.aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_ERROR);
			break;
		}
		if (g_amr_file_cxt.is_pause)
		{
			event = 0;
			Helios_Flag_Wait(g_amr_file_cxt.flag_ref, AMR_FILE_CONTINUE, Helios_FLAG_OR_CLEAR, &event, HELIOS_WAIT_FOREVER);
		}
	}
	Helios_Aud_fclose(g_amr_file_cxt.fd);
	g_amr_file_cxt.fd = NULL;
	// helios_amr_stream_close();
	g_amr_file_cxt.aud_play_cb = NULL;
	Helios_Flag_delete(g_amr_file_cxt.flag_ref);
	g_amr_file_cxt.is_playing = false;

	return 0;
}

int helios_amr_file_pause(bool pause)
{
	if (pause)
	{
		g_amr_file_cxt.is_pause = true;
	}
	else
	{
		g_amr_file_cxt.is_pause = false;
		
		Helios_Flag_Release(g_amr_file_cxt.flag_ref, AMR_FILE_CONTINUE, Helios_FLAG_OR);
	}
	return 0;
}
int helios_amr_file_end()
{
	if (!g_amr_file_cxt.is_playing)
	{
		return -1;
	}
	if (g_amr_file_cxt.is_pause)
	{
		helios_amr_file_pause(0);
	}
	g_amr_file_cxt.is_playing = false;

	return 0;
}

int helios_amr_set_callback(cb_on_player aud_cb)
{
	aud_play_cb = aud_cb;
	return 0;
}

#if defined(CONFIG_POC_QS_R07)
int helios_amrnbenc_init(unsigned int mode, unsigned int format)
{
	int ret = 0;
	if(amr_enc_handle){
		AUDLOGE("amrnb encode is opening !\n");
		return -1;
	}
	amr_enc_config.mode = mode;
	amr_enc_config.output_format = format;
	amr_enc_config.dtx_mode = helios_amr_record_dtx_enable;
	ret = amrnb_encode_open(&amr_enc_config, &amr_enc_handle);
	if(ret < 0){
		AUDLOGE("amrnb encode open failed !\n");
		return -1;
	}
	return 0;
}

int helios_amrnbenc_do(unsigned char *input_buff, unsigned char *output_buff)
{
	int ret = 0, output_size = 0;
	
	if(amr_enc_handle == 0){
		return -1;
	}
	ret = amrnb_encode_set(amr_enc_handle, input_buff, 320);
	if(ret < 0){
		AUDLOGE("amrnb encode set error !\n");
		return -2;
	}
	ret = amrnb_encode_do(amr_enc_handle);
	if (ret != 0){
		AUDLOGE("amrnb encode error \n");
		return -3;
	}
	ret = amrnb_encode_get(amr_enc_handle, output_buff, &output_size);
	if(ret < 0){
		AUDLOGE("amrnb encode get error !\n");
		return -4;
	}

	return output_size;
}

int helios_amrnbenc_deinit(void)
{
	int ret = 0;
	if(!amr_enc_handle){
		AUDLOGE("amrnb encode is not open !\n");
		return -1;
	}
	ret = amrnb_encode_close(amr_enc_handle);
	if(ret < 0){
		AUDLOGE("amrnb encode close failed !\n");
		return -1;
	}
	amr_enc_handle = 0;
	return 0;
}
#endif

