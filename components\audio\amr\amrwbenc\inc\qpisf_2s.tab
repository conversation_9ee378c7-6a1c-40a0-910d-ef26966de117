/*
 ** Copyright 2003-2010, VisualOn, Inc.
 **
 ** Licensed under the Apache License, Version 2.0 (the "License");
 ** you may not use this file except in compliance with the License.
 ** You may obtain a copy of the License at
 **
 **     http://www.apache.org/licenses/LICENSE-2.0
 **
 ** Unless required by applicable law or agreed to in writing, software
 ** distributed under the License is distributed on an "AS IS" BASIS,
 ** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ** See the License for the specific language governing permissions and
 ** limitations under the License.
 */


/*-------------------------------------------------------------------*
 *                         qpisf_2s.h
 *-------------------------------------------------------------------*
 * Quantization tables for two-stage of ISFs (split by 2 in 1st stage)
 * Version whith prediction MU = 0.25
 *-------------------------------------------------------------------*/

#define ORDER   16            /* order of linear prediction filter */
#define ISF_GAP 128           /* 50 Hz */
#define N_SURV  4

#define SIZE_BK1  256
#define SIZE_BK2  256
#define SIZE_BK21 64
#define SIZE_BK22 128
#define SIZE_BK23 128
#define SIZE_BK24 32
#define SIZE_BK25 32

#define SIZE_BK21_36b 128
#define SIZE_BK22_36b 128
#define SIZE_BK23_36b 64

/* means of ISFs */
static Word16 mean_isf[ORDER] = {

   738,  1326,  2336,  3578,  4596,  5662,  6711,  7730,
  8750,  9753, 10705, 11728, 12833, 13971, 15043,  4037};

/* 46 bits */
/*-------------------------------------------------------------------*
 *  isf codebooks:  two-stage VQ with split-by-5 in 2nd stage        *
 *                                                                   *
 *  codebook   vector dimension    number of vectors                 *
 *  ~~~~~~~~   ~~~~~~~~~~~~~~~~    ~~~~~~~~~~~~~~~~~                 *
 *     1_1            9                  256                         *
 *     1_2            7                  256                         *
 *     2_1            3                  64                          *
 *     2_2            3                  128                         *
 *     2_3            3                  128                         *
 *     2_4            3                  32                          *
 *     2_5            4                  32                          *
 *-------------------------------------------------------------------*/

/*------------------------------------------------*
 * 1st stage codebook; 1st split:   isf0 to isf8
 *------------------------------------------------*/

static Word16 dico1_isf[SIZE_BK1*9] = {

   579,  1081,  1035,   390,     3,  -263,  -198,   -82,    38,
    18,   -68,   -12,   313,   761,   405,   249,   111,   -76,
   740,  1263,  1292,  1006,   997,  1019,  1017,   976,   923,
   -91,   827,   948,   648,   613,   535,   522,   490,   421,
    41,   -44,  -281,  -472,   652,   534,   193,   135,   -90,
    41,  -121,  -356,   -60,   663,   307,    61,   -48,  -344,
   557,   946,  1049,   867,   846,   990,  1112,  1262,  1241,
  -118,  -204,   328,   512,   870,   793,   610,   402,   186,
   156,   293,    74,  -338,  -475,  -897,  -594,  -161,  -497,
   226,   131,  -138,   307,   169,  -271,  -164,  -387,  -624,
    62,   -32,   -61,  -252,  -541,  -828, -1027,  -523,  -662,
   102,   -61,   141,   112,  -270,  -251,  -541,    25,  -150,
     6,  -132,  -356,  -686,   -96,  -322,  -522,   -31,  -326,
   -36,  -209,  -521,  -229,   307,  -132,    -5,   -99,  -384,
    60,   -51,  -237,  -668,  -973,  -407,  -708,   -75,  -172,
    26,  -138,  -266,   111,  -302,    43,  -278,  -356,  -359,
   570,   822,   496,  -154,  -312,   -92,   137,   279,   371,
  -146,   368,   409,    68,     6,    77,   167,   202,   162,
   633,   898,   996,   756,   662,   683,   783,   909,   996,
  -103,   294,   607,   415,   483,   462,   480,   431,   408,
  -120,  -338,  -612,  -524,   584,   331,    92,   433,   276,
  -178,  -293,  -154,   -41,   269,   100,    -9,   213,   160,
   830,   736,   278,   820,  1254,   686,   712,  1039,   473,
  -218,  -304,   463,   454,   397,   273,   202,   286,   273,
  -232,     7,     6,  -388,  -472,  -427,  -378,  -167,  -100,
  -294,  -183,   134,   -47,   101,   -88,   -84,  -117,    -3,
    57,    17,  -202,  -634,  -989, -1119,  -533,   176,   -36,
   120,   -28,    23,   111,  -319,   318,   -22,   -77,   266,
  -271,  -464,  -434,  -658,  -640,  -385,  -385,   -99,   -69,
  -198,  -259,  -266,   -44,   -39,  -139,  -137,   171,    66,
     9,  -145,  -377,  -846, -1000,  -111,  -325,   342,   135,
   -81,  -286,  -380,   192,   -57,   307,    76,   -24,  -140,
   677,   702,   247,    56,   249,   141,  -105,  -236,   -99,
    36,   -39,   -69,   348,   198,   -93,   322,    91,   -72,
   503,   885,  1508,  1307,  1282,  1172,  1119,  1209,  1061,
   416,   719,   989,  1227,  1001,  1052,   954,   741,  1044,
  -127,  -376,  -657,   139,   623,   223,   501,   306,   220,
  -113,  -384,  -796,   504,   438,    85,   213,   -83,  -194,
   585,  1132,  1233,  1091,  1247,  1433,  1512,  1448,  1314,
  -174,  -422,     7,  1155,  1089,  1182,  1003,   945,   806,
     8,  -126,  -317,  -103,  -351,  -695,   -98,  -268,  -537,
    33,  -103,  -290,   167,   -39,  -407,    44,  -208,  -375,
   104,   -23,   -64,  -291,  -637,  -851, -1084,   -61,  -112,
   -75,  -306,  -434,   218,  -148,  -354,  -680,  -133,  -216,
  -121,  -377,  -718,   -97,  -130,  -361,  -156,  -379,  -599,
   -56,  -254,  -586,   235,   157,  -214,    11,  -260,  -149,
  -124,  -267,  -397,  -580,  -593,  -527,  -805,  -385,   346,
  -193,  -440,  -708,  -351,  -141,  -255,  -499,  -147,  -185,
   448,   660,   494,   208,   509,   461,   338,   291,   149,
  -223,    88,   335,   159,   212,   191,   286,   308,   205,
   -31,   469,   803,   659,   619,   658,   843,   987,  1113,
  -171,  -242,   514,   362,   295,   524,   552,   694,   585,
   -64,  -308,  -448,   -21,   284,   786,   446,   289,    92,
  -218,  -390,    -7,   169,   206,   330,   352,   408,   358,
   -36,   702,   959,   859,   861,  1115,  1269,  1357,  1305,
  -133,  -341,   -65,   678,   417,   440,   486,   518,   780,
    33,   -44,  -191,  -344,  -461,  -755,  -201,   217,   -31,
  -353,  -547,   -44,   123,   -61,   -68,   -79,    29,    60,
    73,   -57,  -406,  -766, -1243, -1203,   240,   400,   165,
   -73,  -282,  -601,  -213,  -171,  -375,   332,    35,  -103,
   -29,  -207,  -553,  -476,  -638,  -908,   172,   -22,  -135,
  -192,  -239,  -164,  -103,  -111,   -47,   153,   125,   110,
    -1,  -203,  -570, -1030, -1424,  -535,   155,     1,   147,
  -333,  -653,  -865,  -197,  -158,   -21,   -44,    95,   108,
   389,   588,   490,    33,  -237,  -524,  -628,  -136,  -260,
    40,  -177,  -462,   453,   862,   380,   131,  -130,  -405,
   842,  1678,  1841,  1549,  1474,  1256,  1082,   905,   742,
   370,  1216,  1768,  1633,  1212,   636,    22,  -330,    71,
   -76,  -281,  -741,  -742,   898,   619,   277,    71,  -222,
   -32,  -265,  -556,   -25,   994,   682,   305,   126,  -165,
    73,   738,   893,   968,   993,  1768,  2273,  1840,  1391,
   -69,  -349,  -585,   234,  1158,   903,   626,   510,   251,
    -1,   -99,  -272,  -210,  -603,  -351,  -540,  -811,  -383,
   -16,  -230,  -504,   410,   149,  -205,  -343,  -651,  -639,
   103,    -9,  -227,  -205,  -562,  -781, -1079, -1208,  -156,
   143,    63,  -135,   -67,  -317,  -602,  -784, -1154,  -640,
  -144,  -391,  -674,  -622,  -200,  -254,  -660,  -947,  -395,
   -40,  -250,  -625,    27,   543,    94,  -131,  -386,  -673,
  -123,  -371,  -757,  -451,  -564,  -614,  -415,  -711,   -35,
  -116,  -309,  -593,  -268,   239,   -33,  -338,  -650,  -135,
    94,   251,   554,    57,  -312,  -423,  -154,   -57,   235,
  -268,   -71,   381,   114,   -44,   -87,   125,   173,   133,
  1513,  1714,  1238,   534,   276,   315,   461,   459,   508,
  -131,   -19,  1149,   670,   486,   356,   309,   369,   296,
  -223,  -501,  -899,  -722,   -70,     6,   131,   310,   394,
   -99,  -303,  -517,   249,    64,   -53,   135,   -11,   453,
  -147,  -399,  -730,  -401,   817,   738,   802,   749,   575,
  -154,  -435,  -739,   800,   593,   366,   529,   318,   326,
  -224,    45,   -39,  -387,  -515,  -518,  -608,  -384,  -321,
  -315,  -377,   143,  -101,  -113,  -377,  -177,  -144,   -12,
   117,    40,  -239,  -651, -1051,  -581,  -737,  -990,  -328,
    26,   -50,  -157,   -23,  -453,  -283,  -531,  -546,   192,
  -252,  -501,  -743,  -589,  -627,  -499,  -328,  -118,   -72,
  -324,  -494,  -244,  -306,  -144,  -177,  -262,  -135,   -78,
   -36,  -234,  -519,  -961, -1290,  -314,  -479,  -371,   -45,
   -95,  -292,  -535,    -8,  -300,   112,  -164,  -277,   198,
   -99,  -128,   880,   836,   579,   351,    23,   -95,  -217,
   -27,  -258,   124,  1011,   597,   425,   144,     7,   -73,
   421,  1293,  1640,  1623,  1742,  1617,  1499,  1284,  1006,
   -95,   752,  1680,  1569,  1618,  1436,  1200,   980,   712,
   -69,  -300,  -683,  -435,  1132,   899,   504,   332,   109,
   -74,  -323,  -637,   563,  1074,   608,   371,   105,   -49,
   -78,   831,  1194,  1110,  1378,  1481,  1492,  1365,  1217,
  -259,  -121,  1440,  1334,  1628,  1490,  1438,  1223,   933,
   -82,  -306,  -613,  -222,  -378,  -675,  -545,  -671,  -845,
    53,  -124,  -347,   422,    52,  -125,  -270,  -529,     9,
    79,   -89,  -320,  -662,  -999, -1199, -1243,  -676,  -297,
   -68,  -273,  -611,   137,  -146,  -397,  -627,  -845,  -220,
  -112,  -346,  -797,  -826,   234,  -132,  -188,  -278,  -522,
  -159,  -405,  -734,  -419,   293,    74,  -167,  -167,   184,
  -153,  -437,  -833, -1080,  -336,  -472,  -561,  -340,  -253,
  -169,  -423,  -820,  -904,  -131,   -19,  -346,  -604,    31,
    33,   -31,   312,    62,  -148,    49,   -59,   564,   486,
  -306,  -333,   194,   -44,    67,    72,   147,   205,   243,
  -207,   -49,  1360,   983,   969,   991,  1014,  1110,   973,
  -211,  -172,   883,   627,   711,   674,   705,   798,   746,
   -88,  -325,  -763,  -974,   687,   908,   514,   382,   172,
  -292,  -612,  -805,    63,   131,   270,   259,   352,   348,
  -235,   -84,   955,   818,  1120,  1289,  1559,  1480,  1285,
  -180,  -461,  -614,   657,   691,   745,   854,   783,   713,
   -97,  -309,  -477,  -614,  -777,  -734,  -768,  -526,  -472,
  -344,  -476,   -35,  -169,    49,   -77,  -150,  -240,  -141,
   -52,  -268,  -639,  -919, -1278, -1113,  -342,  -333,  -151,
   -68,  -242,  -585,   -73,  -209,  -478,  -159,  -429,   133,
  -197,  -499, -1005, -1268,  -272,  -224,  -105,   -67,    17,
  -363,  -618,  -414,  -116,   -62,    20,    10,   116,   108,
  -195,  -475,  -906, -1260,  -891,  -441,  -277,  -142,   -28,
  -226,  -519,  -950,  -700,  -275,  -266,  -116,  -105,    82,
   404,   511,   520,   327,    17,  -194,  -333,  -536,  -586,
  -114,  -130,   276,   237,   204,   342,   135,   -16,  -111,
   670,  1208,  1168,   860,   742,   601,   528,   403,   309,
   397,   621,   966,   752,   579,   398,   400,   329,   252,
   191,   180,  -137,  -467,   272,   106,   -95,    17,  -192,
   -80,  -290,  -626,   194,   598,   196,    21,  -281,    77,
   510,   864,  1108,   807,   939,   902,   925,   717,   481,
   137,   367,   534,   764,   670,   382,   296,   153,    84,
   303,   497,   144,   -85,  -125,  -539,  -482,  -464,  -764,
   233,   347,    68,  -147,   169,  -210,  -242,  -226,  -482,
   307,   422,   154,  -175,  -386,  -722,  -724,  -904, -1015,
   309,   308,   160,   -60,  -470,  -420,  -598,  -791,  -219,
    68,   121,  -137,  -560,  -146,  -446,  -515,  -494,  -729,
   130,    53,  -227,    46,   474,    32,  -161,  -192,  -490,
   213,   164,   -71,  -465,  -876,  -161,  -456,  -587,   -48,
   218,   117,    39,   177,  -194,   -88,  -226,  -418,    50,
   210,   547,   569,   279,   121,   -44,   -50,    10,   -84,
    58,   140,   182,    -5,   267,   117,   106,   211,   198,
   539,   835,   913,   719,   617,   544,   591,   565,   642,
   153,   559,   872,   460,   222,   108,   188,   180,   183,
   158,   119,   284,  -153,  -271,   229,    87,   110,   -57,
  -183,    82,   118,    21,    13,    40,   118,   191,   185,
   162,   889,   654,   108,   -34,   244,   488,   561,   532,
   163,    56,   609,   341,    50,   329,    68,   266,   218,
   100,   206,    18,  -304,  -107,  -436,  -487,   -65,  -306,
   -86,   154,   134,   -30,   -45,   -73,  -104,   -80,   -96,
   245,   330,    10,  -440,  -849, -1082,    79,    40,  -265,
   196,   372,   272,  -181,  -493,  -389,   275,    80,   -59,
     2,   -12,  -246,  -505,  -100,  -436,    21,  -187,  -431,
  -221,   -48,    36,  -271,  -186,  -147,  -109,    26,    71,
   213,   140,    72,  -351,  -620,   -84,  -363,    69,    46,
    91,   167,    -3,   -95,   -99,  -105,   -48,   114,   147,
   259,   249,   172,   607,   406,    52,    59,  -189,  -320,
   115,   -85,   -54,   574,   128,   226,   -59,  -253,   130,
   -62,  1033,  1308,  1035,  1127,  1098,  1029,   961,   823,
    39,   364,   757,   940,   728,   660,   659,   583,   770,
  -115,  -338,  -760,  -471,   394,    37,   441,   178,     6,
   -57,  -305,  -525,   796,   453,   188,    -4,  -114,   248,
    71,   444,   797,   731,  1096,  1157,  1222,  1029,   811,
   135,   359,   551,   425,   749,   815,   874,   704,   502,
   132,   247,     0,  -206,  -449,  -750,  -258,  -514,  -633,
   248,   249,    91,   121,  -195,  -499,   -90,  -282,  -435,
    78,    20,  -277,  -623,  -983, -1224,  -415,  -458,  -639,
   347,   509,   208,  -179,  -464,  -728,   -76,  -237,  -486,
  -103,  -343,  -756,  -713,  -265,  -609,  -191,  -398,  -636,
  -121,  -383,  -749,   567,   252,   -36,  -354,  -417,   -50,
   204,   100,  -149,  -650, -1081,   -47,    -7,  -263,   111,
   -46,  -180,  -267,  -324,  -562,  -394,  -692,   398,   292,
   482,   670,   683,   624,   442,   165,   116,    36,  -149,
   108,   247,   291,   247,   355,   122,   109,   224,   296,
   -14,   945,   990,   801,   755,   815,   847,   913,   892,
   292,   349,   725,   482,   388,   329,   429,   620,   667,
   -34,   197,   213,  -127,    84,   494,   620,   575,   375,
   126,   207,   172,   167,   362,   202,   296,   395,   455,
    -6,   250,   539,   467,   636,   801,  1149,  1287,  1118,
    27,   240,   369,   280,   440,   411,   634,   892,   953,
   159,   170,   -58,  -395,  -797,  -690,    77,  -211,  -334,
    -5,   -28,   -13,   -74,  -335,  -603,   300,    88,  -205,
    82,   -33,  -364,  -698, -1203, -1153,   110,  -146,  -289,
   113,     1,  -243,  -588,  -994,  -496,   414,   160,    42,
   -56,  -247,  -440,  -693,  -996,  -479,    11,  -178,  -357,
  -151,  -353,  -327,  -211,  -340,   141,    65,   425,   453,
    34,  -169,  -455,  -932, -1215,   138,   499,   256,   324,
    68,   139,   -15,  -547,  -478,    17,   306,   502,   481,
   -32,  -134,   445,   129,  -143,  -244,  -503,  -507,  -599,
    61,  -140,  -345,   496,   458,    -2,    20,  -227,  -514,
   394,  1765,  1666,  1339,  1117,   806,   642,   479,   380,
   215,   519,   920,  1053,  1090,   791,   528,   290,   155,
   -54,  -233,  -647,  -602,   639,   294,    -2,  -167,  -442,
   -78,  -315,  -791,  -113,   820,   403,   158,  -116,  -356,
   529,  1851,  2003,  1228,   622,   -41,  -416,   344,   819,
  -105,  -379,  -236,  1224,   893,   749,   568,   356,   214,
   -17,  -199,  -144,    50,  -283,  -247,  -578,  -846, -1087,
    69,   -11,  -381,  -206,   209,  -284,  -387,  -416,  -716,
    39,    -5,  -145,  -374,  -682,  -909, -1074, -1169, -1066,
   287,   226,    67,  -221,  -662,  -171,  -421,  -642,  -707,
  -132,  -348,  -538,  -448,   -20,    -4,  -354,  -748,  -933,
     4,   -75,  -289,  -598,   317,    52,  -208,  -297,  -559,
   -88,  -264,  -358,  -589,  -631,  -248,  -523,  -822, -1071,
    70,    -8,    54,  -314,  -515,    92,  -146,  -274,  -493,
   199,    62,   391,   158,  -141,    71,  -219,  -203,  -207,
   152,    40,   329,   162,   -29,    48,  -149,   108,   127,
   635,  1058,   883,   492,   372,   312,   317,   274,   241,
   267,   722,  1256,   882,   625,   248,     8,   -81,   -60,
   -58,  -138,  -291,  -600,   -12,    -2,   -39,   147,   117,
  -107,  -345,  -513,   459,    76,    92,  -272,   388,   262,
   362,   516,   203,  -409,  -716,  -831,  -331,   185,   209,
  -117,  -391,  -298,   671,   292,   538,   257,   166,   -38,
  -102,  -319,  -194,  -283,  -573,  -262,  -579,  -219,  -444,
  -235,    78,    11,  -168,  -101,  -229,  -263,  -321,  -123,
    70,    50,  -170,  -599,  -996,  -588,  -263,  -516,  -455,
   394,   363,   229,  -136,  -538,    21,  -183,  -348,  -201,
  -124,  -368,  -640,  -879,  -847,  -209,  -409,  -494,  -515,
  -127,  -341,  -541,  -425,  -510,   -10,  -252,  -473,  -291,
    84,   -69,  -201,  -676,  -868,   103,  -311,  -132,  -320,
     5,  -173,  -188,  -297,  -628,   197,   -57,     7,   -11,
    49,  -160,    56,   558,   111,    33,  -311,  -440,  -463,
    -1,  -246,  -307,   862,   453,   139,  -170,  -355,  -232,
   279,   966,  1642,  1478,  1463,  1123,   795,   525,   339,
  -197,   -38,  1702,  1331,  1252,   950,   692,   504,   426,
  -108,  -344,  -861, -1172,   444,   354,    88,   -46,  -220,
   -53,  -321,  -494,  1113,   744,   364,   198,   -34,   -75,
   457,   955,  1177,  1214,  1427,  1457,  1345,   917,   539,
   -69,   199,   897,  1140,  1343,  1183,   977,   742,   522,
   122,    44,  -269,    27,  -155,  -562,  -307,  -590,  -773,
   154,    42,  -160,   252,  -129,  -305,  -471,  -733,  -371,
   135,   185,   -82,  -416,  -722,  -913,  -504,  -743,  -880,
   149,   214,   -84,  -329,  -680,  -835,  -426,  -661,   -81,
  -128,  -380,  -735,  -998,  -337,    17,  -182,  -467,  -697,
   -84,  -290,  -510,  -592,    13,   440,   154,   -38,  -279,
    70,   -61,  -246,  -727, -1047,   -80,  -381,  -535,  -704,
   178,    -2,  -146,  -670,  -938,   482,   138,    63,    65,
   -11,    15,   772,   443,   142,   -20,  -209,  -126,  -161,
   -32,  -249,    95,   552,   124,    30,  -343,    82,   -86,
   148,   751,  1515,  1105,   867,   606,   474,   448,   399,
  -163,  -257,   899,  1097,   906,   751,   502,   390,   294,
   -51,  -258,  -447,  -806,  -368,   763,   464,   364,   183,
  -166,  -374,  -367,    87,    35,   399,   418,   856,   833,
  -205,  -310,   588,   778,   785,  1065,  1118,  1245,  1157,
  -173,  -312,   107,   345,   400,   790,   870,  1113,  1001,
    -7,  -120,  -387,  -410,  -614,  -943,  -226,  -384,  -491,
  -203,  -288,   -51,  -331,   -90,  -178,  -408,  -573,  -338,
    56,   -29,  -273,  -627, -1041,  -798,  -247,  -467,   148,
    66,    -2,  -205,  -205,  -575,  -349,   -57,  -352,   -58,
   -45,  -225,  -471,  -924,  -497,    77,   -32,    44,  -135,
  -277,  -491,  -497,  -502,  -424,  -202,  -137,    77,    96,
    26,  -179,  -469, -1008, -1260,   262,   -35,  -132,  -259,
   -66,  -232,  -447,  -533,  -789,  -191,  -100,  -267,   364};

/*------------------------------------------------*
 * 1st stage codebook; 2nd split:   isf9 to isf15
 *------------------------------------------------*/

static Word16 dico2_isf[SIZE_BK2*7] = {

  1357,  1313,  1136,   784,   438,   181,   145,
   636,   648,   667,   568,   442,   217,   362,
   427,   440,   674,   524,   332,   117,  -417,
   121,   295,   468,   465,   230,    44,  -221,
  -147,  -240,   149,    80,   390,   278,   106,
  -418,  -556,   552,   511,   235,   144,   -95,
    43,   193,   274,   150,    67,    34,  -273,
   -43,  -126,   171,   416,   282,    63,  -354,
  -372,   -86,  -344,  -108,   -94,  -182,   -89,
  -600,  -840,  -200,   465,   258,   -11,  -253,
   -48,   329,    97,  -290,  -543,  -795,  -354,
  -570,  -117,   187,    10,  -133,  -416,   -76,
  -618,  -129,  -247,  -371,    45,   -76,   277,
 -1022, -1079,   126,   474,   254,   127,    52,
  -281,    76,  -167,  -361,  -283,  -551,  -283,
  -119,   -52,    -1,   134,   -32,  -204,  -415,
  1064,   827,   637,   684,   464,   209,    12,
   482,   416,   449,   371,   335,   294,   194,
   719,   576,   365,   135,   113,    91,  -199,
   298,   176,   493,   366,   194,   163,    36,
   -35,  -236,  -259,   -36,    -4,    99,   152,
   -98,  -306,   -27,   228,    90,   111,   -86,
    91,    13,  -211,  -258,  -106,    86,   -64,
    73,   -35,   -57,   -31,   162,    35,  -192,
  -109,  -335,  -629,   -66,   -61,  -128,   322,
  -495,  -669,  -728,   193,    31,  -220,   122,
   324,    95,   -89,   -91,  -409,  -710,  -154,
     0,  -234,    92,    33,  -343,  -609,  -220,
  -343,  -408,  -476,  -655,  -153,    82,   222,
  -490,  -745,  -255,    49,   -48,   135,  -127,
   119,   -67,  -328,  -390,  -272,  -545,   -56,
   -57,  -130,   -10,    -7,  -164,   -47,   -22,
   984,  1064,   961,   568,   210,   -27,    16,
   811,   691,   754,   514,   224,   -35,   166,
   662,   704,   618,   386,    57,  -211,  -257,
   510,   359,   418,   393,    91,  -144,   -18,
  -193,   -31,   -27,   223,    89,  -143,    24,
  -112,   -98,   471,   319,   185,     3,   175,
   252,   146,   -47,   272,    48,  -211,  -234,
   146,    69,   203,   364,    68,   -52,    51,
  -259,  -478,  -697,  -349,  -758,  -501,    63,
  -501,  -769,  -289,    79,  -311,  -497,  -106,
   251,    53,  -235,  -469,  -895,  -884,   145,
  -416,  -551,   140,  -133,  -523,  -775,    44,
  -326,  -423,  -713,  -497,   -86,  -431,    99,
  -757,  -772,  -160,   -76,   -46,   -32,   379,
    85,   -35,  -200,  -401,  -663, -1040,  -247,
  -180,  -330,   -92,  -376,    27,  -183,  -110,
  1279,  1086,   781,   502,   324,   164,   157,
   682,   466,   449,   277,   146,    28,   409,
   635,   472,   390,   107,  -232,  -538,  -139,
   196,   396,   332,   213,   209,   -29,   -81,
   150,   -95,  -312,    76,   -77,  -320,   -50,
    46,     9,    47,   175,   139,    30,   384,
   218,   206,   -24,  -250,   -96,  -276,  -183,
    26,   119,    38,    14,    -4,  -133,   -52,
  -477,  -614,  -987,  -715,  -631,  -813,   200,
  -744, -1009, -1065,  -745,  -631,  -171,    18,
  -137,  -251,  -483,  -613,  -980, -1203,    12,
  -605,  -767,  -562,  -686, -1088,  -515,    58,
  -202,  -428,  -782, -1072,   -96,  -234,  -179,
  -480,  -709, -1070,  -897,  -131,   -92,   321,
  -145,  -193,  -512,  -729,  -572,  -765,  -210,
  -331,  -585,  -525,  -631,  -281,  -208,  -303,
  1165,  1104,   939,   828,   716,   426,   155,
     6,  -109,   820,   778,   415,   113,   -27,
   381,   339,   314,   265,   121,    -9,  -474,
  -373,    47,   584,   442,    99,  -231,  -113,
  -496,   -38,  -285,   262,   305,   170,     4,
  -587,  -556,    69,    66,   471,   354,    13,
  -138,    70,   -18,   106,    67,   167,  -302,
  -445,  -141,   185,   191,   151,    83,  -133,
  -257,  -521,  -720,  -198,   134,   -46,  -182,
  -819, -1168,  -777,   512,   359,    95,  -113,
   137,    -2,   -74,  -138,  -401,  -114,  -371,
  -242,  -466,   204,   223,   -31,  -212,  -192,
  -532,  -637,  -466,  -686,   256,   277,  -139,
 -1141, -1244,  -381,   -75,   -54,    14,    88,
  -311,   115,  -143,  -499,  -343,   124,  -416,
  -616,  -147,  -135,    43,    -4,   121,  -369,
   835,   783,   641,   390,   355,   350,    64,
    72,   194,   443,   467,   436,   219,   372,
   464,   369,   192,     4,  -156,   -72,  -226,
    57,   206,   303,   205,   188,   101,   265,
   -40,  -205,  -488,  -184,   276,    64,   -26,
  -217,  -433,  -297,   137,   328,   308,  -289,
   378,    81,  -308,  -465,    57,   -37,   227,
  -100,    24,   -36,  -151,   199,     8,   143,
  -426,  -697, -1059,  -133,   388,   161,   321,
  -644, -1023, -1271,    39,    66,  -123,    70,
   372,   177,  -173,  -556,  -553,  -304,  -189,
  -117,  -369,  -425,  -122,  -462,  -152,   -73,
  -649,  -850, -1189,  -767,   497,   360,   222,
  -798, -1139, -1455,  -190,   430,   234,   179,
    42,   -94,  -405,  -692,    38,  -202,  -246,
  -169,  -366,  -290,   -88,   -64,    32,  -292,
  1010,   923,   938,   710,   465,   230,   342,
   217,   300,  1054,   675,    68,  -458,  -179,
    78,   453,   316,    18,  -237,  -496,  -243,
   167,    21,   424,   215,   -91,  -303,  -170,
  -290,   -81,   -70,   -67,    40,    54,   -59,
  -353,  -427,   -90,    53,    94,     9,    54,
   -28,   318,   283,    15,  -240,   -58,    79,
   -75,  -121,   229,    35,    58,     6,  -133,
  -351,  -514,  -744,  -834,  -705,  -137,   164,
 -1124, -1388, -1055,  -230,   -73,    40,    36,
  -163,  -233,  -532,  -785, -1170,  -697,    96,
  -788,  -959,  -246,  -430,  -624,  -165,    -8,
  -856,  -540,  -630,  -907,  -337,   -70,    76,
  -937, -1042,  -659,  -733,  -208,   199,   -26,
  -523,    78,   -98,  -501,  -869,  -890,   -81,
  -624,  -703,   -45,  -348,   -25,    87,  -186,
  1005,   823,   546,   249,    90,   -22,   207,
   298,   397,   381,   319,   200,    62,   303,
   473,   379,   133,  -247,  -632,  -441,    75,
   284,   208,   391,   115,   -25,    44,    95,
   -72,    79,   -95,   -63,  -129,  -293,   203,
  -164,  -349,   115,   122,    69,    -1,   378,
   348,   170,    99,    58,  -179,  -302,   188,
  -190,    -2,   150,    23,   -51,   -11,   216,
  -615,  -863, -1090, -1427,  -802,   -48,    -6,
  -961, -1276, -1548,  -727,   -58,    56,   223,
  -124,  -255,  -561,  -988, -1277,  -148,   -82,
  -480,  -660,  -891, -1191, -1339,  -325,    20,
  -621,  -917, -1296, -1350,   264,   289,    50,
  -844, -1022, -1345, -1329,  -293,    46,   278,
  -260,  -468,  -829, -1176,  -533,  -560,   -78,
  -215,  -484,  -822, -1233,  -791,    15,  -138,
  1301,  1317,  1262,  1048,   716,   357,   -64,
   578,   824,   925,   802,   630,   362,   102,
   470,   925,   767,   514,   327,   190,  -112,
   225,   492,   495,   437,   598,   384,   -45,
    43,    82,   -42,   175,   519,   342,   -64,
  -304,  -154,   159,   576,   403,   221,   327,
   214,   244,   122,   -62,   312,    92,  -160,
   218,   208,   310,   268,   306,   323,  -199,
  -285,  -269,   -79,  -124,  -143,  -153,   236,
  -205,  -384,  -426,   344,    59,  -185,  -184,
  -272,   247,   126,  -210,  -518,  -468,    78,
   -99,  -120,   502,   160,  -280,  -557,   304,
  -423,   -17,  -283,  -443,   215,   212,  -140,
  -564,  -684,  -228,   510,   361,   130,   323,
  -428,   335,    98,   -65,    36,  -215,  -246,
  -362,    51,   364,   -16,  -234,   150,  -165,
   914,   883,   751,   653,   676,   464,  -153,
   631,   545,   535,   720,   596,   360,   -81,
   783,   712,   512,   439,   341,   251,  -391,
   497,   417,   249,   372,   295,   173,  -193,
   128,  -110,  -385,    93,    39,   173,  -231,
   216,   -59,  -253,   462,   389,   154,    69,
   455,   270,    -4,  -337,   -49,   233,  -322,
   307,   143,    53,   218,   128,   236,  -156,
   -37,  -186,  -240,  -411,  -110,     9,   399,
  -140,  -365,  -628,   258,   380,   214,   277,
   131,   454,   177,  -285,  -520,   108,  -214,
    77,  -141,   201,  -123,  -490,  -131,    60,
   -14,  -194,  -521,  -741,   273,   362,   -33,
  -362,  -566,  -287,  -228,   161,   237,   317,
  -269,   195,   -75,  -375,  -204,    11,    77,
  -128,  -264,  -156,  -223,  -475,   265,    27,
  1238,  1147,   916,   689,   432,   210,  -280,
   800,   664,   879,   726,   411,   160,  -164,
   454,   686,   536,   275,   147,    46,   111,
   303,   486,   512,   355,   241,   181,   -69,
    79,    92,    29,   147,   233,    52,    17,
  -171,   289,   131,   439,   271,     3,   -10,
   413,   241,   144,   174,   155,    -2,    14,
    58,   217,   247,   219,   149,   175,   -18,
   228,    -8,  -240,  -206,  -513,  -191,   202,
   -96,  -272,  -454,    33,  -300,  -575,    46,
   -10,  -108,  -246,  -347,  -770,  -535,     9,
  -326,  -430,   -61,  -321,  -704,  -299,   201,
    -1,  -280,  -603,  -419,  -185,    18,   -36,
  -516,  -522,  -379,  -291,  -181,   -97,    27,
  -159,  -313,  -525,  -224,  -510,  -831,  -197,
  -292,  -459,   -59,  -310,  -562,  -143,  -351,
  1066,   912,   631,   389,   207,    86,  -224,
   596,   512,   596,   505,   314,   122,   -48,
   787,   861,   441,   -93,  -303,    33,  -190,
   257,   469,   337,    51,    15,   298,   -93,
   295,    73,  -119,    25,    36,    23,   108,
   -28,    -3,   -32,   114,    21,   185,   107,
   482,   305,    15,  -279,  -319,    52,    96,
   226,    46,   115,    72,  -136,   133,  -125,
    18,  -207,  -559,  -590,  -503,  -482,   321,
  -571,  -789,  -951,  -172,  -441,  -538,   113,
   181,    14,  -310,  -641, -1001,  -202,   159,
  -136,  -393,  -433,  -513,  -911,  -144,   -22,
    72,  -265,  -706,  -954,  -159,    53,   332,
  -338,  -591,  -852,  -383,  -395,    56,    44,
    43,  -158,  -464,  -897,  -631,  -157,  -294,
  -161,  -128,  -328,  -573,  -483,  -125,    11,
  1017,   906,  1051,  1005,   679,   341,  -102,
   359,   334,  1567,  1314,   723,   105,    10,
   -65,   726,   529,   301,   220,    43,  -273,
  -510,   436,   719,   566,   358,   179,   114,
  -560,   298,   133,  -120,   342,   225,    14,
  -899,  -101,   217,   617,   400,   146,   -58,
   -41,   352,    82,  -196,    39,   121,  -167,
  -212,    59,   447,   284,   423,   250,  -169,
  -371,  -484,  -596,    30,   -41,   249,    22,
  -372,  -650,  -794,   477,   445,   216,   -79,
  -352,   275,    17,  -443,  -929,    92,    19,
  -699,  -696,   431,   264,   -49,  -310,   182,
  -978,  -217,  -430,  -400,   101,   261,    72,
  -929,  -889,  -357,   -13,   463,   378,   236,
  -826,    56,    30,  -299,  -360,  -128,   -51,
  -878,  -299,  -111,    75,    65,    36,     3,
   817,   368,   -25,   354,   697,   591,  -173,
   309,   212,   222,   751,   484,   140,   -56,
   593,   379,    70,    -8,   258,   180,   110,
   165,   -46,   255,   297,   219,   273,   105,
   160,   -70,  -358,  -181,   379,   330,   319,
  -238,  -369,  -198,   740,   580,   319,  -143,
   201,   109,  -202,  -456,   328,   276,  -141,
   203,   170,   111,    42,   207,   360,   188,
  -345,  -399,  -513,  -233,   650,   422,    81,
  -635,  -961, -1220,   463,   539,   204,   209,
   202,   -25,  -194,  -498,  -787,   193,  -143,
  -449,  -538,   195,  -106,  -331,    68,    62,
  -228,  -477,  -840,  -576,   317,   128,   283,
  -671,  -937,  -807,  -114,   391,   335,   -62,
   246,     2,  -314,  -679,  -303,   180,   -88,
  -107,  -272,    90,  -198,   -28,   290,  -112,
   885,  1149,  1021,   712,   496,   281,   -83,
   269,   492,   787,   643,   347,    70,   124,
   336,   636,   499,    92,  -229,  -179,   191,
    26,   402,   564,   340,   149,   -11,   135,
  -440,   561,   470,   204,   -72,  -186,   140,
  -720,    14,   355,   229,    68,  -133,   465,
   110,   310,   103,    12,   106,    29,   158,
  -178,   113,   161,   142,   121,   115,    27,
  -651,  -414,  -645,  -152,  -164,   -13,  -429,
  -639,  -944,  -681,  -104,   -81,    52,  -189,
  -663,  -164,  -316,  -683,  -954,  -205,   -83,
  -609,  -669,  -172,  -517,  -694,   283,   -80,
  -646,  -152,  -383,  -678,  -246,   -40,  -143,
  -747,  -796,  -745,  -390,   -98,    43,   275,
  -599,  -199,  -398,  -433,  -436,  -538,    31,
 -1107,  -568,  -376,  -265,  -126,   -21,     1,
   847,   573,   308,   392,   305,   101,    55,
   273,   293,   201,   267,   346,   201,   123,
   727,   480,   226,     2,   -65,  -138,   164,
   273,   208,   173,   292,    12,   253,   174,
   340,   207,   180,    88,   116,    46,   475,
  -460,  -166,   -30,    13,   110,   173,   396,
   137,    88,    43,  -137,   -94,    34,   284,
    96,   -14,   226,    40,    63,    70,   130,
  -467,  -735, -1012, -1174,  -307,   305,   -67,
  -612,  -920, -1146,  -567,    -8,    92,   -25,
  -182,  -271,  -492,  -754,  -857,   287,   -75,
  -494,  -787,  -689,  -683,  -709,   137,  -326,
  -288,  -550,  -903, -1105,   334,   321,   -62,
  -354,  -653,  -834,  -445,     1,   377,  -152,
  -162,  -306,  -608,  -937,  -297,   247,  -192,
  -234,  -477,  -244,  -488,  -266,   342,  -332};

/*---------------------------------------------------*
 * 2nd stage codebook; 1st split:   isf2_0 to isf2_2
 *---------------------------------------------------*/


static Word16 dico21_isf[SIZE_BK21*3] = {

   329,   409,   249,
   -33,   505,   160,
   -29,   -14,   582,
  -262,   127,   354,
   145,   237,   175,
  -152,   245,   122,
    27,    42,   340,
   -84,   -93,   311,
   285,   222,  -156,
    47,   -43,  -504,
   234,   121,   385,
   104,  -317,    45,
   176,   195,     8,
   104,   -59,   -94,
   177,    53,   192,
   -34,  -127,   152,
   570,   277,   -34,
   -67,  -329,  -639,
  -157,  -272,   462,
  -177,  -462,   198,
   322,   179,   115,
  -386,   171,    19,
    19,   -12,   195,
  -120,  -252,   201,
   304,    36,  -336,
  -128,  -221,  -380,
   171,  -185,   296,
  -242,  -312,    23,
   198,    39,    16,
    -3,  -177,  -111,
   111,   -93,    76,
   -92,  -223,     4,
   177,   406,   -44,
  -168,   380,  -149,
    -4,   273,   331,
  -420,   513,   277,
    21,   247,    47,
   -58,   131,    -2,
    -3,   134,   180,
  -145,    40,   175,
   189,    74,  -145,
   -27,   -45,  -325,
   370,  -114,   -21,
   -83,  -415,  -173,
    77,    95,   -51,
   -40,   -30,   -67,
    71,    88,    86,
   -35,   -98,    14,
    69,   197,  -334,
  -196,    79,  -231,
  -348,  -137,   218,
  -352,   -89,   -85,
    47,   201,  -130,
  -165,    37,   -15,
   -43,     3,    86,
  -161,  -108,    79,
    83,    21,  -237,
   -81,  -149,  -238,
   150,  -186,  -251,
  -186,  -249,  -162,
   -19,    66,  -139,
   -26,   -50,  -181,
    24,    11,     0,
  -130,  -105,   -98};



/*---------------------------------------------------*
 * 2nd stage codebook; 2nd split:   isf2_3 to isf2_5
 *---------------------------------------------------*/


static Word16 dico22_isf[SIZE_BK22*3] = {

  -127,   310,    42,
  -242,   197,     5,
  -151,    84,   -17,
  -214,   127,  -149,
  -247,  -131,   159,
  -268,  -267,   -95,
  -217,     1,   -79,
  -271,   -80,  -185,
   -45,   436,   159,
   165,   199,   391,
   -33,    81,   187,
   -66,   -42,   355,
  -298,   -57,   343,
  -108,  -537,   226,
  -144,   -23,   193,
   176,  -402,    87,
    53,   296,    25,
   -84,   253,  -104,
   -58,   105,  -126,
  -169,   174,  -314,
   -48,    44,  -294,
  -164,  -417,  -242,
  -139,     3,  -194,
  -155,  -207,  -211,
   119,   322,   213,
   333,    50,   380,
   237,   247,    -2,
   466,   -16,   201,
   238,  -255,  -107,
    67,  -440,  -149,
   122,   -88,  -139,
    88,  -247,   -73,
   -41,   231,   167,
   -62,   155,    16,
   -65,    16,    77,
   -68,    -2,   -63,
  -151,  -300,   160,
   -18,  -333,    54,
   -56,   -94,     5,
     2,  -190,    14,
    92,   148,   209,
   108,     9,   272,
   108,    35,   110,
   142,   -85,   145,
    47,  -157,   279,
     3,  -320,   246,
    43,   -72,    68,
    86,  -217,   135,
    36,   140,    79,
    56,   175,   -49,
    26,    45,     3,
    73,    55,  -101,
   109,  -183,  -242,
    -4,  -283,  -242,
    48,   -68,   -48,
    -6,  -153,  -122,
   161,   196,    96,
   232,    80,   190,
   165,    97,    11,
   258,   -31,    71,
   267,   -77,   -91,
   311,  -209,    87,
   152,   -14,   -22,
   150,  -149,     9,
  -324,   557,   187,
  -384,   307,    46,
  -251,    27,    77,
  -365,    77,   -52,
  -482,   -84,   160,
  -424,  -515,   -64,
  -294,  -120,    -4,
  -476,  -116,  -109,
   -97,   318,   365,
   106,   627,   445,
  -190,   120,   287,
  -146,    65,   619,
  -427,   242,   363,
  -361,  -371,   432,
  -347,   102,   168,
  -629,   195,   -14,
   -65,   476,   -47,
  -297,   320,  -168,
   -55,   356,  -264,
  -391,    82,  -286,
   -51,   -31,  -556,
  -178,  -399,  -586,
  -205,   -49,  -360,
  -343,  -238,  -337,
   220,   457,    58,
   561,   467,   259,
   340,   270,  -168,
   450,    77,  -280,
    60,   167,  -413,
   133,  -252,  -492,
   216,   157,  -290,
   282,     0,  -495,
  -226,   293,   183,
  -157,   135,   122,
  -158,   -59,    39,
  -133,  -118,   -97,
  -332,  -309,   113,
  -160,  -425,    -6,
  -149,  -211,    24,
   -80,  -277,   -90,
   -11,   125,   338,
   130,   -71,   465,
     5,   -45,   184,
   237,   -95,   253,
  -139,  -197,   297,
   -19,  -300,   511,
   -63,  -152,   139,
   250,  -289,   336,
   124,   339,  -150,
    34,   176,  -208,
   171,   166,  -116,
    94,    38,  -229,
    75,   -65,  -339,
   -78,  -205,  -385,
     0,   -30,  -163,
   -56,  -110,  -242,
   321,   244,   194,
   505,   238,    -1,
   317,   116,    65,
   309,    88,   -74,
   452,   -51,   -50,
   334,  -217,  -290,
   211,    41,  -152,
   238,   -55,  -260};


/*---------------------------------------------------*
 * 2nd stage codebook; 3rd split:   isf2_6 to isf2_8
 *---------------------------------------------------*/


static Word16 dico23_isf[SIZE_BK23*3] = {

   -10,   151,   359,
   136,   298,   223,
   255,  -104,   290,
   423,     6,   183,
  -270,  -269,   -98,
   -52,   -82,    13,
   -82,  -274,   -97,
    90,  -246,   -72,
  -299,   -70,   421,
   -88,   365,   430,
   187,  -318,   381,
   380,    37,   488,
  -373,  -316,    79,
  -308,  -101,     5,
  -135,  -451,     8,
    72,  -421,  -154,
   180,   170,  -121,
    62,   177,   -40,
   326,    80,  -105,
   248,   263,    -5,
  -168,  -181,  -221,
    -2,   -23,  -158,
   -14,  -149,  -121,
   119,   -91,  -147,
   119,   332,  -153,
    49,   303,    34,
   442,   -55,   -69,
   217,   454,    58,
  -359,  -187,  -375,
   -42,    50,  -274,
    -8,  -267,  -249,
    85,   -86,  -346,
   -77,   -40,   345,
    89,   134,   219,
   156,   -80,   160,
   108,    40,   116,
  -158,  -206,    29,
     5,   -32,   175,
   -65,  -158,   146,
    55,   -78,    73,
  -114,  -222,   353,
   -47,    81,   211,
    49,  -151,   268,
   105,     4,   302,
  -263,  -132,   183,
  -151,   -28,   201,
  -177,  -307,   166,
   101,  -221,   130,
    74,    58,   -98,
    32,    44,    13,
   194,    30,  -142,
   170,    96,     8,
  -136,  -119,   -91,
   -65,     8,   -55,
     3,  -188,    12,
    45,   -63,   -49,
   149,   -21,   -19,
    24,   144,    95,
   254,   -22,    60,
   161,   196,    96,
  -158,   -61,    48,
   -70,    33,    82,
   -23,  -321,    58,
   155,  -147,     5,
  -364,   328,    77,
   -21,   453,   173,
  -108,    82,   630,
   367,   263,   208,
  -300,   -62,  -176,
  -205,   143,  -158,
  -169,  -410,  -264,
   257,  -269,  -100,
  -636,   289,    -2,
  -292,   627,   173,
  -382,  -363,   387,
   248,   524,   447,
  -521,  -111,  -107,
  -395,   118,  -274,
  -343,  -680,  -125,
  -172,  -447,  -663,
    75,   148,  -367,
   -79,   263,   -94,
   249,   148,  -286,
   380,   271,  -162,
  -142,    -4,  -186,
   -57,   111,  -125,
   -35,  -108,  -254,
   100,    29,  -242,
   -80,   303,  -264,
   -78,   464,   -57,
   248,   -22,  -494,
   661,   662,    44,
  -193,   -40,  -330,
  -178,   145,  -337,
   -90,  -199,  -400,
   -40,   -23,  -498,
  -192,   114,   315,
   -41,   244,   190,
    88,   -97,   485,
   241,    80,   212,
  -246,    40,    87,
  -156,   147,   134,
    -2,  -334,   239,
   308,  -203,   110,
  -459,   251,   422,
  -218,   310,   228,
   -86,  -346,   654,
   184,   175,   425,
  -481,   -63,   169,
  -349,   117,   188,
  -125,  -560,   310,
   158,  -416,    94,
    46,   171,  -192,
   -63,   157,    14,
   256,   -35,  -271,
   322,   123,    53,
  -214,     4,   -76,
  -156,    86,   -18,
   128,  -197,  -232,
   265,   -90,   -98,
  -308,   332,  -145,
  -131,   308,    58,
   509,    59,  -339,
   562,   196,   -14,
  -378,   100,   -47,
  -234,   202,     1,
   104,  -270,  -493,
   319,  -210,  -325};


/*---------------------------------------------------*
 * 2nd stage codebook; 4th split:   isf2_9 to isf2_11
 *---------------------------------------------------*/

static Word16 dico24_isf[SIZE_BK24*3] = {

   -79,   -89,    -4,
  -171,    77,  -211,
   160,  -193,    98,
   120,  -103,   323,
    32,   -22,  -129,
    72,    78,  -268,
   182,   -76,   -66,
   309,    99,  -145,
  -229,  -157,   -84,
  -383,    98,   -71,
   -90,  -352,    12,
  -284,  -178,   178,
   -65,  -125,  -166,
   -87,  -175,  -351,
    42,  -198,   -48,
   154,  -140,  -243,
   -77,    18,   108,
   -39,   355,    91,
    87,     8,   155,
    -4,   158,   239,
   128,    95,   -54,
     7,   246,  -124,
   258,    15,    89,
   206,   216,    98,
  -201,     9,    18,
  -312,   233,   204,
   -39,  -174,   155,
  -144,    -9,   284,
   -57,    70,   -69,
  -157,   187,    18,
    54,   -30,    23,
    24,   135,    55};


/*---------------------------------------------------*
 * 2nd stage codebook; 5th split:   isf2_12 to isf2_15
 *---------------------------------------------------*/

static Word16 dico25_isf[SIZE_BK25*4] = {

   169,   142,  -119,   115,
   206,   -20,    94,   226,
  -106,   313,   -21,    16,
   -62,   161,    71,   255,
   -89,   101,  -185,   125,
    72,   -30,  -201,   344,
  -258,    33,    -8,    81,
  -104,  -154,    72,   296,
   144,   -68,  -268,   -25,
    81,   -78,   -87,   106,
    22,   155,  -186,  -119,
   -46,   -28,    27,    91,
  -114,   -37,  -175,   -33,
   -94,  -222,  -189,   122,
  -132,  -119,  -191,  -270,
  -172,  -173,    18,   -43,
   279,   135,   -42,  -128,
   187,   -86,   229,  -138,
   159,   240,   140,    46,
    69,    25,   227,    77,
    21,   115,    13,     8,
    68,  -248,   126,    81,
  -150,   137,   207,    -9,
  -154,  -133,   289,    67,
   143,   -37,   -86,  -326,
   180,   -32,    19,   -23,
    26,   168,   116,  -233,
   -32,   -26,   118,   -78,
     3,    -8,   -45,  -115,
    57,  -215,   -54,   -83,
  -209,   112,   -22,  -167,
   -91,  -151,   168,  -262};



       /* 36 bit */
/*-------------------------------------------------------------------*
 *  isf codebooks:  two-stage VQ with split-by-3 in 2nd stage        *
 *                1st stage is kept the same as the 46 bit quantizer *
 *                                                                   *
 *  codebook   vector dimension    number of vectors                 *
 *  ~~~~~~~~   ~~~~~~~~~~~~~~~~    ~~~~~~~~~~~~~~~~~                 *
 *     1_1            9                  256                         *
 *     1_2            7                  256                         *
 *     2_1            5                  128                         *
 *     2_2            4                  128                         *
 *     2_3            7                  64                          *
 *-------------------------------------------------------------------*/

static Word16 dico21_isf_36b[SIZE_BK21_36b*5] = {

   -52,   -96,   212,   315,   -73,
    82,  -204,   363,   136,  -197,
  -126,  -331,   183,   218,   143,
   -49,   -41,   557,   230,    72,
     2,   -73,   163,   377,   221,
   133,   111,   278,   215,  -110,
  -102,   -20,   284,   113,   273,
    84,   319,   290,    18,    85,
   -25,    -5,   125,   132,  -204,
   -38,    -5,   286,    -9,  -356,
  -140,  -256,    92,   117,  -189,
  -144,   191,   313,    51,   -98,
   167,   -10,    44,   247,    36,
   381,   197,   238,    74,     6,
    38,  -408,    29,    -3,   -85,
    92,   266,   157,   -25,  -200,
   161,  -121,    70,    84,  -140,
   -16,   -86,   112,   -94,  -189,
  -269,  -270,   351,   107,   -24,
   -68,   -67,   492,  -103,  -155,
   -53,  -131,    62,   122,    10,
   135,    84,   283,   -55,  -120,
   -12,  -219,   331,   -81,   167,
   220,  -136,   147,  -172,   -42,
   140,   -95,  -109,   -88,  -194,
     0,    -2,    -4,   -33,  -381,
   -66,  -217,   152,  -186,  -402,
   244,   108,   156,  -140,  -395,
   113,  -136,  -196,   110,   -24,
   214,   118,    11,   -64,  -131,
  -110,  -286,    -6,  -332,    16,
    94,    97,    79,  -291,  -205,
    -5,   -39,   -20,   252,   -96,
    76,   174,   101,   163,    61,
   -69,  -239,   -55,   399,     6,
  -115,   319,   164,   275,   196,
   -15,    36,   -47,   331,   121,
   226,   209,   271,   325,   184,
    13,   -80,  -218,   471,   353,
   288,   378,    16,   -51,   251,
   174,   116,    52,   149,  -279,
   235,   276,    39,   120,   -48,
     0,  -108,  -108,   241,  -339,
   -93,   534,    45,    33,   -87,
   194,   149,   -71,   405,   -44,
   409,   370,    81,  -186,  -154,
    25,  -102,  -448,   124,  -173,
    22,   408,  -110,  -310,  -214,
   -26,    23,   -83,   114,    14,
  -110,   164,    52,   223,   -82,
    37,   -25,  -263,   306,   -15,
  -466,   415,   292,   165,   -18,
    29,   -19,  -171,   155,   182,
   179,   144,   -27,   231,   258,
  -103,  -247,  -396,   238,   113,
   375,  -154,  -109,    -4,   156,
    98,    85,  -292,    -5,  -124,
   116,   139,  -116,   -98,  -294,
   -14,   -83,  -278,  -117,  -378,
   106,    33,  -106,  -344,  -484,
   119,    17,  -412,   138,   166,
   384,   101,  -204,    88,  -156,
  -121,  -284,  -300,    -1,  -166,
   280,    33,  -152,  -313,   -81,
   -37,    22,   229,   153,    37,
   -60,   -83,   236,    -8,   -41,
  -169,  -228,   126,   -20,   363,
  -235,    17,   364,  -156,   156,
   -25,   -30,    72,   144,   156,
   153,   -26,   256,    97,   144,
   -21,   -37,    48,   -65,   250,
    63,    77,   273,  -128,   124,
  -129,   -26,    40,     9,  -115,
    -6,    82,    38,   -90,  -182,
  -336,   -13,    28,   158,    91,
   -30,   241,   137,  -170,   -17,
   146,    14,   -11,    33,    61,
   192,   197,    54,   -84,    85,
    23,  -200,   -78,   -29,   140,
   122,   237,   106,  -341,   136,
   -57,  -142,   -85,   -16,   -74,
   -59,   -90,    -8,  -187,   -20,
  -211,  -267,   216,  -179,  -110,
   -50,    -7,   220,  -267,   -70,
   -57,   -42,   -17,   -15,    71,
    32,    21,    63,  -137,    33,
  -137,  -175,   104,   -68,    97,
   -67,   -43,   133,  -301,   221,
  -116,  -200,   -81,   -92,  -272,
   -64,   -41,   -54,  -244,  -220,
  -287,  -242,   -50,   -87,   -89,
  -245,   236,   102,  -166,  -295,
    66,    24,  -162,   -71,    95,
    66,   136,   -90,  -220,   -36,
   -98,  -161,  -222,  -188,    29,
   -18,    18,   -19,  -415,     9,
    49,    61,   100,    39,   -56,
  -111,    82,   135,   -31,    52,
   -90,  -153,   -93,   189,   182,
  -214,   295,   119,   -74,   284,
     2,   137,    37,    47,   182,
    92,   117,   184,   -53,   373,
   -21,   -14,   -35,   136,   391,
   146,   129,  -164,   -28,   333,
    92,    80,   -84,   100,  -134,
    -8,   217,   -32,     3,   -47,
  -151,   251,  -215,   142,    92,
  -224,   310,  -172,  -275,    98,
   159,   155,  -177,   112,    53,
   205,    27,     8,  -240,   192,
   169,   120,  -319,  -201,   106,
    11,    36,   -86,  -237,   455,
  -109,  -154,  -163,   174,   -55,
   -38,    32,  -101,   -78,   -59,
  -205,  -321,   -97,    69,    79,
  -310,    44,    18,  -185,    34,
  -115,   -20,  -148,   -39,   203,
   -29,   154,   -30,  -158,   166,
   -45,  -131,  -317,   -24,   363,
  -165,  -205,  -112,  -222,   265,
   -32,   -44,  -150,    54,  -193,
    -6,   -38,  -255,  -169,  -115,
  -266,    87,  -189,   -36,  -169,
   -60,   -87,  -266,  -436,  -170,
   -68,   -81,  -278,    24,    38,
   -23,   -19,  -155,  -256,   141,
   -61,  -226,  -565,  -175,    71,
     9,   -29,  -237,  -515,   263};

static Word16 dico22_isf_36b[SIZE_BK22_36b*4] = {

  -298,    -6,    95,    31,
  -213,   -87,  -122,   261,
     4,   -49,   208,    14,
  -129,  -110,    30,   118,
  -214,   258,   110,  -235,
   -41,   -18,  -126,   120,
   103,    65,   127,   -37,
   126,   -36,   -24,    25,
  -138,   -67,  -278,  -186,
  -164,  -194,  -201,    78,
  -211,   -87,   -51,  -221,
  -174,   -79,   -94,   -39,
    23,    -6,  -157,  -240,
    22,  -110,  -153,   -68,
   148,    -5,    -2,  -149,
    -1,  -135,   -39,  -179,
    68,   360,  -117,   -15,
   137,    47,  -278,   146,
   136,   260,   135,    65,
    61,   116,   -45,    97,
   231,   379,    87,  -120,
   338,   177,  -272,     3,
   266,   156,    28,   -69,
   260,    84,   -85,    86,
  -266,   154,  -256,  -182,
   -17,   -65,  -304,    -6,
   -40,   175,  -151,  -180,
   -27,    27,   -87,   -63,
   121,   114,  -166,  -469,
   159,   -66,  -323,  -231,
   214,   152,  -141,  -212,
   137,    36,  -184,   -51,
  -282,  -237,    40,    10,
   -48,  -235,   -37,   251,
   -54,  -323,   136,    29,
   -88,  -174,   213,   198,
  -390,    99,   -63,  -375,
   107,  -169,  -164,   424,
    69,  -111,   141,  -167,
    74,  -129,    65,   144,
  -353,  -207,  -205,  -109,
  -160,  -386,  -355,    98,
  -176,  -493,   -20,  -143,
  -252,  -432,    -2,   216,
   -90,  -174,  -168,  -411,
    13,  -284,  -229,  -160,
   -87,  -279,    34,  -251,
   -75,  -263,   -58,   -42,
   420,    53,  -211,  -358,
   384,   -35,  -374,   396,
    68,  -228,   323,    -2,
   167,  -307,   192,   194,
   459,   329,    -5,  -332,
   375,    79,    -7,   313,
   282,  -124,   200,   -92,
   271,  -162,   -70,   180,
  -157,  -298,  -514,  -309,
    58,  -163,  -546,    18,
   124,  -364,   167,  -238,
    83,  -411,  -117,    96,
   140,  -112,  -388,  -624,
   259,  -133,  -317,    41,
   163,  -130,   -64,  -334,
   226,  -165,  -124,  -110,
  -466,   -61,     6,   229,
  -153,   205,  -145,   242,
  -159,    48,   195,   148,
   -58,    28,    31,   279,
  -303,   185,   279,    -4,
   -61,   197,    59,    86,
  -114,   123,   168,   -52,
    35,    36,   100,   126,
  -407,   102,   -77,   -40,
  -338,    -1,  -342,   156,
  -179,   105,   -34,   -97,
  -185,    84,   -35,   108,
  -133,   107,   -91,  -357,
  -180,    54,  -229,    24,
   -44,    47,    47,  -182,
   -66,    13,    45,     4,
  -339,   251,    64,   226,
   -42,   101,  -350,   275,
   -99,   398,   142,   121,
   111,    12,  -102,   260,
     0,   505,   260,   -94,
   161,   285,   -96,   224,
    -4,   206,   314,    33,
   167,   139,    88,   204,
  -235,   316,   -60,   -25,
    -8,  -150,  -312,   201,
   -36,   292,    61,  -104,
   -40,   174,  -162,    42,
   -21,   402,   -29,  -351,
    21,   152,  -360,   -93,
    57,   191,   212,  -196,
    76,   158,   -21,   -69,
  -328,  -185,   331,   119,
   -53,   285,    56,   337,
  -107,   -24,   405,    29,
   -18,   137,   272,   277,
  -255,    22,   173,  -191,
   295,   322,   325,   302,
    21,   -27,   332,  -178,
   119,    13,   271,   129,
  -455,  -180,   116,  -191,
  -227,    62,  -148,   524,
  -176,  -287,   282,  -157,
  -243,    13,   199,   430,
   -59,   -49,   115,  -365,
    72,  -172,  -137,    93,
  -138,  -126,   141,   -84,
     5,  -124,    38,   -20,
  -258,   311,   601,   213,
    94,   130,   -61,   502,
    -1,  -157,   485,   313,
   146,   -74,   158,   345,
   276,   135,   280,   -57,
   490,   252,    99,    43,
   267,   -74,   429,   105,
   278,   -23,   119,    94,
  -542,   488,   257,  -115,
   -84,  -244,  -438,   478,
  -113,  -545,   387,   101,
   -95,  -306,   111,   498,
    95,   166,    22,  -301,
   420,   -15,   -58,   -78,
   270,    29,   122,  -282,
   160,  -240,    50,   -38};

static Word16 dico23_isf_36b[SIZE_BK23_36b*7] = {

    81,   -18,    68,   -27,  -122,  -280,    -4,
    45,  -177,   209,   -30,  -136,   -74,   131,
   -44,   101,   -75,   -88,   -48,  -137,   -54,
  -245,   -28,    63,   -18,  -112,  -103,    58,
   -79,    -6,   220,   -65,   114,   -35,   -50,
   109,   -65,   143,  -114,   129,    76,   125,
   166,    90,   -61,  -242,   186,   -74,   -43,
   -46,   -92,    49,  -227,    24,  -155,    39,
    67,    85,    99,   -42,    53,  -184,  -281,
   142,  -122,     0,    21,  -142,   -15,   -17,
   223,    92,   -21,   -48,   -82,   -14,  -167,
    51,   -37,  -243,   -30,   -90,    18,   -56,
    54,   105,    74,    86,    69,    13,  -101,
   196,    72,   -89,    43,    65,    19,    39,
   121,    34,   131,   -82,    25,   213,  -156,
   101,  -102,  -136,   -21,    57,   214,    22,
    36,  -124,   205,   204,    58,  -156,   -83,
    83,  -117,   137,   137,    85,   116,    44,
   -92,  -148,   -68,    11,  -102,  -197,  -220,
   -76,  -185,   -58,   132,   -26,  -183,    85,
    -7,   -31,    -2,    23,   205,  -151,    10,
   -27,   -37,    -5,   -18,   292,   131,     1,
   117,  -168,     9,   -93,    80,   -59,  -125,
  -182,  -244,    98,   -24,   135,   -22,    94,
   221,    97,   106,    42,    43,  -160,    83,
    25,   -64,   -21,     6,    14,   -15,   154,
   126,    15,  -140,   150,   -10,  -207,  -114,
    79,   -63,  -211,   -70,   -28,  -217,   165,
    46,    38,   -22,   281,   132,   -62,   109,
   112,    54,  -112,   -93,   208,    27,   296,
   115,    10,  -147,    41,   216,    42,  -276,
    50,  -115,  -254,   167,   117,    -2,    61,
    17,   144,    34,   -72,  -186,  -150,   272,
   -29,   -66,   -89,   -95,  -149,   129,   251,
   122,     0,   -50,  -234,   -91,    36,    26,
  -105,  -102,   -88,  -121,  -236,    -7,   -11,
  -204,   109,     5,  -191,   105,   -15,   163,
   -80,    32,   -24,  -209,    41,   294,    70,
  -106,   -94,  -204,  -118,   120,   -50,   -37,
   -82,  -241,    46,  -131,   -29,   150,   -55,
    33,   155,   120,   -89,    -8,     7,    62,
   213,    82,    61,    18,  -161,   144,   152,
    30,   131,    65,   -87,  -255,   -17,  -107,
    -8,    85,   -64,    51,  -162,   223,   -53,
  -134,   261,    69,   -56,   218,    72,  -111,
     2,   155,  -113,   -87,    49,    85,   -28,
  -163,    42,    -1,  -196,     7,    39,  -245,
    14,  -137,   -79,    11,  -160,   202,  -293,
   -94,    33,   208,   100,    56,   -44,   326,
   -78,   -41,   232,    13,  -142,   227,    80,
   -16,   -87,   201,    33,  -133,    15,  -183,
   -58,  -192,   -47,   184,  -128,   133,    99,
  -205,    11,  -155,    78,    52,    72,   141,
  -246,    26,    99,   151,    59,   115,   -64,
   -79,   -47,   -16,   -14,     6,    47,   -43,
   -72,  -178,   -27,   162,   112,    43,  -174,
  -175,   238,   186,    71,   -54,  -188,   -76,
  -225,   233,    39,   -39,  -158,   122,    44,
   -26,    43,    84,   130,   -93,   -51,    22,
     3,    92,  -150,   136,  -182,   -57,    97,
  -131,   179,   -78,    80,    91,  -165,    90,
    -2,   148,    15,   130,    65,   175,   117,
  -138,   114,  -137,   132,     3,   -10,  -186,
   140,    -4,   -37,   254,   -62,    92,  -109};


