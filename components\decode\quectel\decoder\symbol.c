
#include "quec_decoder_config.h"
//#include <stdio.h>
#include <string.h>
#include <assert.h>

#include <quec_decoder.h>
#include "symbol.h"
#include "ql_zo_malloc.h" 

const char *quec_decoder_get_symbol_name (quec_decoder_symbol_type_t sym)
{
    switch(sym & QUEC_DECODER_SYMBOL) {
    case QUEC_DECODER_EAN8: return("EAN-8");
    case QUEC_DECODER_UPCE: return("UPC-E");
    case QUEC_DECODER_ISBN10: return("ISBN-10");
    case QUEC_DECODER_UPCA: return("UPC-A");
    case QUEC_DECODER_EAN13: return("EAN-13");
    case QUEC_DECODER_ISBN13: return("ISBN-13");
    case QUEC_DECODER_I25: return("I2/5");
    case QUEC_DECODER_CODE39: return("CODE-39");
    case QUEC_DECODER_CODE128: return("CODE-128");
    case QUEC_DECODER_PDF417: return("PDF417");
    case QUEC_DECODER_QRCODE: return("QR-Code");
    default: return("UNKNOWN");
    }
}

const char *quec_decoder_get_addon_name (quec_decoder_symbol_type_t sym)
{
    switch(sym & QUEC_DECODER_ADDON) {
    case QUEC_DECODER_ADDON2: return("+2");
    case QUEC_DECODER_ADDON5: return("+5");
    default: return("");
    }
}


void _quec_decoder_symbol_free (quec_decoder_symbol_t *sym)
{
    if(sym->syms) {
        quec_decoder_symbol_set_ref(sym->syms, -1);
        sym->syms = NULL;
    }
    if(sym->pts)
    {
        free(sym->pts);
        //zo_myfree(sym->pts);//mymalloc_test
    }
        
    if(sym->data_alloc && sym->data)
    {
        free(sym->data);
        //zo_myfree(sym->data);//mymalloc_test
    }
	if(sym)
    	free(sym);
    //zo_myfree(sym);//mymalloc_test
}

void quec_decoder_symbol_ref (const quec_decoder_symbol_t *sym,
                      int refs)
{
    quec_decoder_symbol_t *ncsym = (quec_decoder_symbol_t*)sym;
    _quec_decoder_symbol_refcnt(ncsym, refs);
}

quec_decoder_symbol_type_t quec_decoder_symbol_get_type (const quec_decoder_symbol_t *sym)
{
    return(sym->type);
}

const char *quec_decoder_symbol_get_data (const quec_decoder_symbol_t *sym)
{
    return(sym->data);
}

unsigned int quec_decoder_symbol_get_data_length (const quec_decoder_symbol_t *sym)
{
    return(sym->datalen);
}

int quec_decoder_symbol_get_count (const quec_decoder_symbol_t *sym)
{
    return(sym->cache_count);
}

int quec_decoder_symbol_get_quality (const quec_decoder_symbol_t *sym)
{
    return(sym->quality);
}

unsigned quec_decoder_symbol_get_loc_size (const quec_decoder_symbol_t *sym)
{
    return(sym->npts);
}

int quec_decoder_symbol_get_loc_x (const quec_decoder_symbol_t *sym,
                           unsigned idx)
{
    if(idx < sym->npts)
        return(sym->pts[idx].x);
    else
        return(-1);
}

int quec_decoder_symbol_get_loc_y (const quec_decoder_symbol_t *sym,
                           unsigned idx)
{
    if(idx < sym->npts)
        return(sym->pts[idx].y);
    else
        return(-1);
}

const quec_decoder_symbol_t *quec_decoder_symbol_next (const quec_decoder_symbol_t *sym)
{
    return((sym) ? sym->next : NULL);
}

const quec_decoder_symbol_set_t*
quec_decoder_symbol_get_components (const quec_decoder_symbol_t *sym)
{
    return(sym->syms);
}

const quec_decoder_symbol_t *quec_decoder_symbol_first_component (const quec_decoder_symbol_t *sym)
{
    return((sym && sym->syms) ? sym->syms->head : NULL);
}


static const char *xmlfmt[] = {
    "<symbol type='%s' quality='%d'",
    " count='%d'",
    "><data><![CDATA[",
    "]]></data></symbol>",
};

/* FIXME suspect... */
#define MAX_INT_DIGITS 10

char *quec_decoder_symbol_xml (const quec_decoder_symbol_t *sym,
                       char **buf,
                       unsigned *len)
{
    const char *type = quec_decoder_get_symbol_name(sym->type);
    /* FIXME binary data */
    unsigned datalen = strlen(sym->data);
    unsigned maxlen = (strlen(xmlfmt[0]) + strlen(xmlfmt[1]) +
                       strlen(xmlfmt[2]) + strlen(xmlfmt[3]) +
                       strlen(type) + datalen + MAX_INT_DIGITS + 1);
    if(!*buf || (*len < maxlen)) {
        if(*buf)
            free(*buf);
        *buf = malloc(maxlen);
        /* FIXME check OOM */
        *len = maxlen;
    }

    //int n = snprintf(*buf, maxlen, xmlfmt[0], type, sym->quality);
    int n = 1;
    assert(n > 0);
    assert(n <= maxlen);

    if(sym->cache_count) {
        //int i = snprintf(*buf + n, maxlen - n, xmlfmt[1], sym->cache_count);
        int i = 1;
        assert(i > 0);
        n += i;
        assert(n <= maxlen);
    }

    int i = strlen(xmlfmt[2]);
    memcpy(*buf + n, xmlfmt[2], i + 1);
    n += i;
    assert(n <= maxlen);

    /* FIXME binary data */
    /* FIXME handle "]]>" */
    strncpy(*buf + n, sym->data, datalen + 1);
    n += datalen;
    assert(n <= maxlen);

    i = strlen(xmlfmt[3]);
    memcpy(*buf + n, xmlfmt[3], i + 1);
    n += i;
    assert(n <= maxlen);

    *len = n;
    return(*buf);
}


quec_decoder_symbol_set_t *_quec_decoder_symbol_set_create ()
{
    quec_decoder_symbol_set_t *syms = calloc(1, sizeof(*syms));
    //quec_decoder_symbol_set_t *syms = zo_mymalloc(sizeof(*syms));//mymalloc_test
    _quec_decoder_refcnt(&syms->refcnt, 1);
    return(syms);
}

inline void _quec_decoder_symbol_set_free (quec_decoder_symbol_set_t *syms)
{
    quec_decoder_symbol_t *sym, *next;
    for(sym = syms->head; sym; sym = next) {
        next = sym->next;
        sym->next = NULL;
        _quec_decoder_symbol_refcnt(sym, -1);
    }
    syms->head = NULL;
	if(syms)
   		free(syms);
    //zo_myfree(syms);//mymalloc_test
}

void quec_decoder_symbol_set_ref (const quec_decoder_symbol_set_t *syms,
                          int delta)
{
    quec_decoder_symbol_set_t *ncsyms = (quec_decoder_symbol_set_t*)syms;
    if(!_quec_decoder_refcnt(&ncsyms->refcnt, delta) && delta <= 0)
        _quec_decoder_symbol_set_free(ncsyms);
}

int quec_decoder_symbol_set_get_size (const quec_decoder_symbol_set_t *syms)
{
    return(syms->nsyms);
}

const quec_decoder_symbol_t*
quec_decoder_symbol_set_first_symbol (const quec_decoder_symbol_set_t *syms)
{
    quec_decoder_symbol_t *sym = syms->tail;
    if(sym)
        return(sym->next);
    return(syms->head);
}
