#------------------------------------------------------------
# (C) Copyright [2006-2008] Marvell International Ltd.
# All Rights Reserved
#------------------------------------------------------------

#--------------------------------------------------------------------------------------------------------------------
# INTEL CONFIDENTIAL
# Copyright 2006 Intel Corporation All Rights Reserved.
# The source code contained or described herein and all documents related to the source code ("Material") are owned
# by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
# its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
# Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
# treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
# transmitted, distributed, or disclosed in any way without Intel's prior express written permission.
#
# No license under any patent, copyright, trade secret or other intellectual property right is granted to or
# conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
# estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
# Intel in writing.
# -------------------------------------------------------------------------------------------------------------------

#=========================================================================
# File Name      : amr.mak
# Description    : Main make file for the amr package.
#
# Usage          : make [-s] -f amr.mak OPT_FILE=<path>/<opt_file>
#
# Notes          : The options file defines macro values defined
#                  by the environment, target, and groups. It
#                  must be included for proper package building.
#
#
#
#=========================================================================

# Package build options
include ${OPT_FILE}

# Package Makefile information
GEN_PACK_MAKEFILE = ${BUILD_ROOT}/env/${HOST}/build/cpppackage.mak

# Define Package ---------------------------------------
PACKAGE_NAME     = amr
PACKAGE_BASE     = pcac
PACKAGE_PATH     = ${BUILD_ROOT}/${PACKAGE_BASE}/${PACKAGE_NAME}

# The relative path locations of source and include file directories.
PACKAGE_SRC_PATH    = ${PACKAGE_PATH}/src	\
					${PACKAGE_PATH}/amrnb/common/src	\
					${PACKAGE_PATH}/amrnb/dec/src	\
					${PACKAGE_PATH}/amrnb/dec/api	\
					${PACKAGE_PATH}/amrnb/enc/src	\
					${PACKAGE_PATH}/amrnb/enc/api	\
					${PACKAGE_PATH}/amrwb/src	\
					${PACKAGE_PATH}/amrwb/api	\
					
PACKAGE_DFLAGS   = -I${PACKAGE_PATH}/include     	\
			-I${PACKAGE_PATH}/amrnb/common/include 	\
			-I${PACKAGE_PATH}/amrnb/dec/api 	\
			-I${PACKAGE_PATH}/amrnb/dec/src 	\
			-I${PACKAGE_PATH}/amrnb/enc/api 	\
			-I${PACKAGE_PATH}/amrnb/enc/src 	\
			-I${PACKAGE_PATH}/amrwb/include 	\
			-I${PACKAGE_PATH}/amrwb/api 		\
			-I${PACKAGE_PATH}/amrwb/src 		\
			-I${PACKAGE_PATH}/amrwbenc/api		\
			-I${BUILD_ROOT}/aud_sw/AudioService/inc	\
			-I${BUILD_ROOT}/aud_sw/Audio/inc	\
			-I${BUILD_ROOT}/aud_sw/AuC/inc		\
			-I${BUILD_ROOT}/quectel/open/common_api/include \
			-I${BUILD_ROOT}/quectel/cust/inc \
			-I${BUILD_ROOT}/softutil/littlefs/inc\
		    -I${BUILD_ROOT}/hop/utilities/inc \
			-I${BUILD_ROOT}/aud_sw/ACM_COMM/inc \
			
PACKAGE_VARIANT = 	      
PACKAGE_VARIANT += AMRNB_DEC
#PACKAGE_VARIANT += AMRWB_DEC
PACKAGE_VARIANT += AMRNB_ENC
#PACKAGE_VARIANT += AMRWB_ENC

# Package source files, paths not required
#amr api
PACKAGE_SRC_FILES =  amr_vocoder_api.cpp \
		     ql_amr_vocoder_api.cpp

ifneq (,$(findstring AMRNB_,${PACKAGE_VARIANT}))
#amrnb common
	PACKAGE_SRC_FILES += 		add.cpp		\
		az_lsp.cpp		\
		bitno_tab.cpp		\
		bitreorder_tab.cpp		\
		bits2prm.cpp		\
		c2_9pf_tab.cpp		\
		copy.cpp		\
		div_32.cpp		\
		div_s.cpp		\
		extract_h.cpp		\
		extract_l.cpp		\
		gains_tbl.cpp		\
		gc_pred.cpp		\
		gmed_n.cpp		\
		gray_tbl.cpp		\
		grid_tbl.cpp		\
		int_lpc.cpp		\
		inv_sqrt.cpp		\
		inv_sqrt_tbl.cpp		\
		l_abs.cpp		\
		l_deposit_h.cpp		\
		l_deposit_l.cpp		\
		l_shr_r.cpp		\
		log2.cpp		\
		log2_norm.cpp		\
		log2_tbl.cpp		\
		lsfwt.cpp		\
		lsp.cpp		\
		lsp_az.cpp		\
		lsp_lsf.cpp		\
		lsp_lsf_tbl.cpp		\
		lsp_tab.cpp		\
		mult_r.cpp		\
		negate.cpp		\
		norm_l.cpp		\
		norm_s.cpp		\
		ph_disp_tab.cpp		\
		pow2.cpp		\
		pow2_tbl.cpp		\
		pred_lt.cpp		\
		q_plsf.cpp		\
		q_plsf_3.cpp		\
		q_plsf_3_tbl.cpp		\
		q_plsf_5.cpp		\
		q_plsf_5_tbl.cpp		\
		qua_gain_tbl.cpp		\
		reorder.cpp		\
		residu.cpp		\
		round.cpp		\
		set_zero.cpp		\
		shr.cpp		\
		shr_r.cpp		\
		sqrt_l.cpp		\
		sqrt_l_tbl.cpp		\
		sub.cpp		\
		syn_filt.cpp		\
		vad1.cpp		\
		weight_a.cpp		\
		window_tab.cpp		\

endif

ifneq (,$(findstring AMRNB_DEC,${PACKAGE_VARIANT}))
#amrnb dec
	PACKAGE_SRC_FILES +=  		a_refl.cpp		\
			agc.cpp		\
			amrdecode.cpp		\
			b_cn_cod.cpp		\
			bgnscd.cpp		\
			c_g_aver.cpp		\
			d_gain_c.cpp		\
			d_gain_p.cpp		\
			d_plsf.cpp		\
			d_plsf_3.cpp		\
			d_plsf_5.cpp		\
			d1035pf.cpp		\
			d2_11pf.cpp		\
			d2_9pf.cpp		\
			d3_14pf.cpp		\
			d4_17pf.cpp		\
			d8_31pf.cpp		\
			dec_amr.cpp		\
			dec_gain.cpp		\
			dec_input_format_tab.cpp		\
			dec_lag3.cpp		\
			dec_lag6.cpp		\
			dtx_dec.cpp		\
			ec_gains.cpp		\
			ex_ctrl.cpp		\
			if2_to_ets.cpp		\
			int_lsf.cpp		\
			lsp_avg.cpp		\
			ph_disp.cpp		\
			post_pro.cpp		\
			preemph.cpp		\
			pstfilt.cpp		\
			qgain475_tab.cpp		\
			sp_dec.cpp		\
			wmf_to_ets.cpp		\

#amrnb dec api
	PACKAGE_SRC_FILES +=  amrnb_dec_api.cpp
	PACKAGE_CFLAGS  += -DFEATURE_AUDIO_NBAMR_DEC=1
endif

ifneq (,$(findstring AMRNB_ENC,${PACKAGE_VARIANT}))
#amrnb enc
	PACKAGE_SRC_FILES +=  		amrencode.cpp		\
			autocorr.cpp		\
			c1035pf.cpp		\
			c2_11pf.cpp		\
			c2_9pf.cpp		\
			c3_14pf.cpp		\
			c4_17pf.cpp		\
			c8_31pf.cpp		\
			calc_cor.cpp		\
			calc_en.cpp		\
			cbsearch.cpp		\
			cl_ltp.cpp		\
			cod_amr.cpp		\
			convolve.cpp		\
			cor_h.cpp		\
			cor_h_x.cpp		\
			cor_h_x2.cpp		\
			corrwght_tab.cpp		\
			dtx_enc.cpp		\
			enc_lag3.cpp		\
			enc_lag6.cpp		\
			enc_output_format_tab.cpp		\
			ets_to_if2.cpp		\
			ets_to_wmf.cpp		\
			g_adapt.cpp		\
			g_code.cpp		\
			g_pitch.cpp		\
			gain_q.cpp		\
			hp_max.cpp		\
			inter_36.cpp		\
			inter_36_tab.cpp		\
			l_comp.cpp		\
			l_extract.cpp		\
			l_negate.cpp		\
			lag_wind.cpp		\
			lag_wind_tab.cpp		\
			levinson.cpp		\
			lpc.cpp		\
			ol_ltp.cpp		\
			p_ol_wgh.cpp		\
			pitch_fr.cpp		\
			pitch_ol.cpp		\
			pre_big.cpp		\
			pre_proc.cpp		\
			prm2bits.cpp		\
			q_gain_c.cpp		\
			q_gain_p.cpp		\
			qgain475.cpp		\
			qgain795.cpp		\
			qua_gain.cpp		\
			s10_8pf.cpp		\
			set_sign.cpp		\
			sid_sync.cpp		\
			sp_enc.cpp		\
			spreproc.cpp		\
			spstproc.cpp		\
			ton_stab.cpp		\

#amrnb enc api
	PACKAGE_SRC_FILES +=  amrnb_enc_api.cpp
	PACKAGE_CFLAGS  += -DFEATURE_AUDIO_NBAMR_ENC=1
endif

ifneq (,$(findstring AMRWB_DEC,${PACKAGE_VARIANT}))
#amrwb dec
	PACKAGE_SRC_FILES +=  		agc2_amr_wb.cpp		\
			band_pass_6k_7k.cpp		\
			dec_acelp_2p_in_64.cpp		\
			dec_acelp_4p_in_64.cpp		\
			dec_alg_codebook.cpp		\
			dec_gain2_amr_wb.cpp		\
			deemphasis_32.cpp		\
			dtx_decoder_amr_wb.cpp		\
			get_amr_wb_bits.cpp		\
			highpass_400hz_at_12k8.cpp		\
			highpass_50hz_at_12k8.cpp		\
			homing_amr_wb_dec.cpp		\
			interpolate_isp.cpp		\
			isf_extrapolation.cpp		\
			isp_az.cpp		\
			isp_isf.cpp		\
			lagconceal.cpp		\
			low_pass_filt_7k.cpp		\
			median5.cpp		\
			mime_io.cpp		\
			noise_gen_amrwb.cpp		\
			normalize_amr_wb.cpp		\
			oversamp_12k8_to_16k.cpp		\
			phase_dispersion.cpp		\
			pit_shrp.cpp		\
			pred_lt4.cpp		\
			preemph_amrwb_dec.cpp		\
			pvamrwb_math_op.cpp		\
			pvamrwbdecoder.cpp		\
			q_gain2_tab.cpp		\
			qisf_ns.cpp		\
			qisf_ns_tab.cpp		\
			qpisf_2s.cpp		\
			qpisf_2s_tab.cpp		\
			scale_signal.cpp		\
			synthesis_amr_wb.cpp		\
			voice_factor.cpp		\
			wb_syn_filt.cpp		\
			weight_amrwb_lpc.cpp		\

#amrwb dec api
	PACKAGE_SRC_FILES +=  amrwb_dec_api.cpp
	PACKAGE_CFLAGS  += -DFEATURE_AUDIO_WBAMR_DEC=1
endif

ifneq (,$(findstring AMRWB_ENC,${PACKAGE_VARIANT}))
	PACKAGE_SRC_FILES +=  amrwb_enc_api.cpp
	PACKAGE_CFLAGS  += -DFEATURE_AUDIO_WBAMR_ENC=1
endif

PACKAGE_CFLAGS  += -Otime --arm --diag_suppress=2523,3007
PACKAGE_ARFLAGS =
PACKAGE_DFLAGS  += -DPV_ARM_V5=1

# Include the Standard Package Make File ---------------
include ${GEN_PACK_MAKEFILE}

# Include the Make Dependency File ---------------------
# This must be the last line in the file
