NAME := quectel

include config/$(KCONFIG_CONFIG)

GLOBAL_INCS = include

$(NAME)_SRCS += \
	decode/src/ql_decoder.c \
	decoder/decoder.c \
	decoder/image.c \
	decoder/img_scanner.c \
	decoder/refcnt.c \
	decoder/scanner.c \
	decoder/symbol.c \
	decoder_code/code39.c \
	decoder_code/code128.c \
	decoder_code/ean.c \
	decoder_code/i25.c \
	decoder_code/qr_finder.c \
	decoder_qrcode/bch15_5.c \
	decoder_qrcode/binarize.c \
	decoder_qrcode/isaac.c \
	decoder_qrcode/qrdec.c \
	decoder_qrcode/rs.c \
	decoder_qrcode/util.c \
	opencv/core/src/alloc.c \
	opencv/imgproc/src/newcheck.c \
	iconv/lib/iconv.c \
	iconv/libcharset/lib/localcharset.c




GLOBAL_INCS += \
	. \
	inc \
	iconv/inc \
	iconv/lib \
	opencv/core/inc \
	opencv/imgpro/inc \
	opencv/imgpro/src


$(NAME)_CFLAGS += \
	-Wno-error=implicit-function-declaration \
	-Wno-error=int-conversion \
	-Wno-error=missing-braces \
	-Wno-error=unused-parameter \
	-Wno-error=sign-compare \
	-Wno-error=empty-body \
	-Wno-error=type-limits \
	-Wno-sign-compare \
	-Wno-unused-parameter \
	-Wno-error=unused-variable \
	-Wno-error=absolute-value \
	-Wno-error=missing-field-initializers \
	-Wno-error=implicit-fallthrough=


$(NAME)_COMPONENTS = peripheral


