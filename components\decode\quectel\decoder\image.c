
//#include "error.h"
#include "string.h"
#include "image.h"
#include "refcnt.h"
#include "ql_zo_malloc.h"  

quec_decoder_image_t *quec_decoder_image_create ()
{
    quec_decoder_image_t *img = calloc(1, sizeof(quec_decoder_image_t));
	memset(img,0,sizeof(quec_decoder_image_t));
    //quec_decoder_image_t *img = zo_mymalloc(sizeof(quec_decoder_image_t));//mymalloc_test
    _quec_decoder_refcnt_init();
    _quec_decoder_image_refcnt(img, 1);
    img->srcidx = -1;
    return(img);
}

void _quec_decoder_image_free (quec_decoder_image_t *img)
{
    if(img->syms) {
        quec_decoder_symbol_set_ref(img->syms, -1);
        img->syms = NULL;
    }
	if(img)
    	free(img);
    //zo_myfree(img);//mymalloc_test
}

void quec_decoder_image_destroy (quec_decoder_image_t *img)
{
    _quec_decoder_image_refcnt(img, -1);
}

void quec_decoder_image_ref (quec_decoder_image_t *img,
                     int refs)
{
    _quec_decoder_image_refcnt(img, refs);
}

unsigned long quec_decoder_image_get_format (const quec_decoder_image_t *img)
{
    return(img->format);
}

unsigned quec_decoder_image_get_sequence (const quec_decoder_image_t *img)
{
    return(img->seq);
}

unsigned quec_decoder_image_get_width (const quec_decoder_image_t *img)
{
    return(img->width);
}

unsigned quec_decoder_image_get_height (const quec_decoder_image_t *img)
{
    return(img->height);
}

const void *quec_decoder_image_get_data (const quec_decoder_image_t *img)
{
    return(img->data);
}

unsigned long quec_decoder_image_get_data_length (const quec_decoder_image_t *img)
{
    return(img->datalen);
}

void quec_decoder_image_set_format (quec_decoder_image_t *img,
                            unsigned long fmt)
{
    img->format = fmt;
}

void quec_decoder_image_set_sequence (quec_decoder_image_t *img,
                              unsigned seq)
{
    img->seq = seq;
}

void quec_decoder_image_set_size (quec_decoder_image_t *img,
                          unsigned w,
                          unsigned h)
{
    img->width = w;
    img->height = h;
}

inline void quec_decoder_image_free_data (quec_decoder_image_t *img)
{
    if(!img)
        return;
    if(img->src) {
        /* replace video image w/new copy */
        quec_decoder_image_t *newimg = quec_decoder_image_create();
        memcpy(newimg, img, sizeof(quec_decoder_image_t));
        /* recycle video image */
        newimg->cleanup(newimg);
        /* detach old image from src */
        img->cleanup = NULL;
        img->src = NULL;
        img->srcidx = -1;
    }
    else if(img->cleanup && img->data) {
        if(img->cleanup != quec_decoder_image_free_data) {
            /* using function address to detect this case is a bad idea;
             * windows link libraries add an extra layer of indirection...
             * this works around that problem (bug #2796277)
             */
            quec_decoder_image_cleanup_handler_t *cleanup = img->cleanup;
            img->cleanup = quec_decoder_image_free_data;
            cleanup(img);
        }
        else
        {
            //free((void*)img->data);
            zo_myfree((void*)img->data);//mymalloc_test
        }
    }
    img->data = NULL;
}

void quec_decoder_image_set_data (quec_decoder_image_t *img,
                          const void *data,
                          unsigned long len,
                          quec_decoder_image_cleanup_handler_t *cleanup)
{
    quec_decoder_image_free_data(img);
    img->data = data;
    img->datalen = len;
    img->cleanup = cleanup;
}

void quec_decoder_image_set_userdata (quec_decoder_image_t *img,
                              void *userdata)
{
    img->userdata = userdata;
}

void *quec_decoder_image_get_userdata (const quec_decoder_image_t *img)
{
    return(img->userdata);
}

quec_decoder_image_t *quec_decoder_image_copy (const quec_decoder_image_t *src)
{
    quec_decoder_image_t *dst = quec_decoder_image_create();
    dst->format = src->format;
    dst->width = src->width;
    dst->height = src->height;
    dst->datalen = src->datalen;
    dst->data = malloc(src->datalen);
    memcpy((void*)dst->data, src->data, src->datalen);
    dst->cleanup = quec_decoder_image_free_data;
    return(dst);
}

const quec_decoder_symbol_set_t *quec_decoder_image_get_symbols (const quec_decoder_image_t *img)
{
    return(img->syms);
}

void quec_decoder_image_set_symbols (quec_decoder_image_t *img,
                             const quec_decoder_symbol_set_t *syms)
{
    if(img->syms)
        quec_decoder_symbol_set_ref(img->syms, -1);
    img->syms = (quec_decoder_symbol_set_t*)syms;
    if(syms)
        quec_decoder_symbol_set_ref(img->syms, 1);
}

const quec_decoder_symbol_t *quec_decoder_image_first_symbol (const quec_decoder_image_t *img)
{
    return((img->syms) ? img->syms->head : NULL);
}

typedef struct zimg_hdr_s {
    unsigned int magic, format;
    unsigned short width, height;
    unsigned int size;
} zimg_hdr_t;

