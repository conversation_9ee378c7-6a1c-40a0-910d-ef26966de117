#ifndef __QL_ZO_MALLOC_H
#define __QL_ZO_MALLOC_H
 
#ifndef NULL
#define NULL 0
#endif

#define MEM_BLOCK_SIZE			24  	  						
#define MEM_MAX_SIZE			200*1024  						//70K
#define MEM_ALLOC_TABLE_SIZE	MEM_MAX_SIZE/MEM_BLOCK_SIZE 	
 
		 

struct _m_mallco_dev
{
	void (*init)(void);				
	unsigned char (*perused)(void);		  	
	unsigned char 	*membase;					
	unsigned short *memmap; 					
	unsigned char  memrdy; 					
};
extern struct _m_mallco_dev mallco_dev;	

void mymemset(void *s,unsigned char c,unsigned int count);	
void mymemcpy(void *des,void *src,unsigned int n);
void zo_mem_init(void);		
void zo_mem_release(void);

unsigned int zo_mem_malloc(unsigned int size);		 		
unsigned char zo_mem_free(unsigned int offset);		 		
unsigned char mem_perused(void);					 
////////////////////////////////////////////////////////////////////////////////

void zo_myfree(void *ptr);  				
void *zo_mymalloc(unsigned int size);				
void *zo_myrealloc(void *ptr,unsigned int size);	
#endif













