#ifndef __HELIOS_AUDIO_FS_H__
#define __HELIOS_AUDIO_FS_H__

#ifdef __cplusplus
extern "C" {
#endif

typedef int audio_fs_res_t;

typedef void HeliosAudFILE;


typedef struct _audio_fs_drv_t {
    char letter;	//reserve, not use
	uint8_t is_used;

    void * (*open_cb)(const char * path, const char * mode);
    audio_fs_res_t (*close_cb)(void * file_p);
    audio_fs_res_t (*read_cb)(void *buffer, size_t size, size_t num, void *file);
    audio_fs_res_t (*write_cb)(void *buffer, size_t size, size_t num, void *file);
    audio_fs_res_t (*seek_cb)(void *file, long offset, int origin);
    audio_fs_res_t (*tell_cb)(void *file);

    void * user_data; /**< Custom file user data*/
} audio_fs_drv_t;


void Helios_Aud_fs_drv_register(audio_fs_drv_t *drv_p);

void Helios_Aud_fs_drv_unregister();
void * Helios_Aud_fopen (const char * path, const char* mode);

audio_fs_res_t Helios_Aud_fclose (void * file_p);

audio_fs_res_t Helios_Aud_fread (void *buffer, size_t size, size_t num, void *file);


audio_fs_res_t Helios_Aud_fwrite (void *buffer, size_t size, size_t num, void *file);

audio_fs_res_t Helios_Aud_fseek (void *file, long offset, int origin);

audio_fs_res_t Helios_Aud_ftell (void *file);

#ifdef __cplusplus
}
#endif


#endif

