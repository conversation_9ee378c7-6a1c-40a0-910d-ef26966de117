/**  @file
  ql_iconv.h

  @brief
  This file is for iconv .

*/

/*================================================================
  Copyright (c) 2020 Quectel Wireless Solution, Co., Ltd.  All Rights Reserved.
  Quectel Wireless Solution Proprietary and Confidential.
=================================================================*/
/*=================================================================

                        EDIT HISTORY FOR MODULE

This section contains comments describing changes made to the module.
Notice that changes are listed in reverse chronological order.

WHEN              WHO         WHAT, WHERE, WHY
------------     -------     -------------------------------------------------------------------------------

=================================================================*/

#ifndef _QL_ICONV_H
#define _QL_ICONV_H

#ifdef __cplusplus
extern "C" {
#endif

#include "iconv.h"
/*===========================================================================
 * include files
 ===========================================================================*/

/*===========================================================================
 * Macro Definition
 ===========================================================================*/
#define ICONV_CONST

/*===========================================================================
 * Struct
 ===========================================================================*/

/*===========================================================================
 * Enum
 ===========================================================================*/

/*===========================================================================
 * Variate
 ===========================================================================*/
 
/*===========================================================================
 * Functions
 ===========================================================================*/
 
iconv_t iconv_open (const char* tocode, const char* fromcode);
size_t iconv (iconv_t icd, ICONV_CONST char* * inbuf, size_t *inbytesleft, char* * outbuf, size_t *outbytesleft);
int iconv_close (iconv_t icd);




#ifdef __cplusplus
    } /*"C" */
#endif
    
#endif /* _QL_ICONV_H */

