fp_open_library()


set(mp3_dec_srcs
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_normalize.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_alias_reduction.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_crc.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_decode_header.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_decode_huff_cw.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_getbits.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_dequantize_sample.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_framedecoder.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_get_main_data_size.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_get_side_info.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_get_scale_factors.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_mpeg2_get_scale_data.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_mpeg2_get_scale_factors.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_mpeg2_stereo_proc.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_huffman_decoding.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_huffman_parsing.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_tables.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_imdct_synth.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_mdct_6.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_dct_6.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_poly_phase_synthesis.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_equalizer.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_seek_synch.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_stereo_proc.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_reorder.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_polyphase_filter_window.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_mdct_18.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_dct_9.cpp
        ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/pvmp3_dct_16.cpp
)
set(ARM_ASM_OPT true)

if (${ARM_ASM_OPT})
set (mp3_dec_asm_srcs
    ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/asm/pvmp3_polyphase_filter_window_armcc.s
    ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/asm/pvmp3_mdct_18_armcc.s
    ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/asm/pvmp3_dct_9_armcc.s
    ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/src/asm/pvmp3_dct_16_armcc.s
)
set (mp3_asm_definition
	"$<$<COMPILE_LANGUAGE:C>:PV_ARM_V5>"
	"$<$<COMPILE_LANGUAGE:CXX>:PV_ARM_V5>"
)
endif()

set(mp3_dec_include_dirs ${HAL_SOURCE_DIR}/media/external_codecs/mp3_dec/include)


fp_library_include_directories(
    ${mp3_dec_include_dirs}
)

fp_open_library_sources(
    ${mp3_dec_srcs}
	${mp3_dec_asm_srcs}
)


fp_library_compile_options(
    "$<$<COMPILE_LANGUAGE:C>:--gnu>"
    "$<$<COMPILE_LANGUAGE:CXX>:--gnu>"
    "$<$<COMPILE_LANGUAGE:C>:-O3>"
    "$<$<COMPILE_LANGUAGE:CXX>:-O3>"
    "$<$<COMPILE_LANGUAGE:CXX>:--cpp>"
)

fp_library_compile_definitions(
    "$<$<COMPILE_LANGUAGE:CXX>:OSCL_EXPORT_REF=>"
    "$<$<COMPILE_LANGUAGE:C>:OSCL_EXPORT_REF=>"
    "$<$<COMPILE_LANGUAGE:CXX>:OSCL_IMPORT_REF=>"
    "$<$<COMPILE_LANGUAGE:C>:OSCL_IMPORT_REF=>"
    "$<$<COMPILE_LANGUAGE:CXX>:OSCL_UNUSED_ARG=>"
	"$<$<COMPILE_LANGUAGE:C>:OSCL_UNUSED_ARG=>"
    "$<$<COMPILE_LANGUAGE:CXX>:CRANE_UOS>"
    "$<$<COMPILE_LANGUAGE:C>:CRANE_UOS>"
	${mp3_asm_definition}
)




