/**
 ******************************************************************************
 * @ audio record struct    
 ******************************************************************************
 */

#include "helios_audio_py.h"

#ifndef bool
#define bool unsigned char
#endif

enum {
	FORMAT_WAV,
	FORMAT_AMR,
	FORMAT_MP3,
};


typedef enum{
	HELIOS_RECORD_STOP,
	HELIOS_RECORD_RELEASE_MSG,
	HELIOS_RECORD_RECORDING,
	HELIOS_RECORD_STOPPING
}RECORD_STATUS;

typedef enum{
	HELIOS_RECORD_WAV_FILE,
	HELIOS_RECORD_WAV_STREAM,
	HELIOS_RECORD_AMRNB_FILE,
	HELIOS_RECORD_AMRNB_STREAM,
	HELIOS_RECORD_MP3_FILE,
	HELIOS_RECORD_MP3_STREAM,
	HELIOS_RECORD_FORMAT_MAX
}RECORD_FROMAT;

struct wav_header{
	unsigned int riff_id;
	unsigned int riff_sz;
	unsigned int riff_fmt;
	unsigned int fmt_id;
	unsigned int fmt_sz;
	unsigned short audio_format;
	unsigned short num_channels;
	unsigned int sample_rate;
	unsigned int byte_rate;
	unsigned short block_align;
	unsigned short bits_per_sample;
	unsigned int data_id;
	unsigned int data_sz;
};



typedef enum
{
	AUD_RECORD_ERROR = -1,
	AUD_RECORD_START = 0,
	AUD_RECORD_DATA,
	AUD_RECORD_PAUSE,
	AUD_RECORD_FINISHED,
	AUD_RECORD_DISK_FULL,
}enum_aud_record_state;

/**
 * \brief AMR-WB modes
 */
typedef enum
{
	HELIOS_AMRWB_MODE_NONE = 0,
		
    HELIOS_AMRWB_MODE_660, 	///< 6.60 kbps
    HELIOS_AMRWB_MODE_885,     ///< 8.85 kbps
    HELIOS_AMRWB_MODE_1265,    ///< 12.65 kbps
    HELIOS_AMRWB_MODE_1425,    ///< 14.25 kbps
    HELIOS_AMRWB_MODE_1585,    ///< 15.85 kbps
    HELIOS_AMRWB_MODE_1825,    ///< 18.25 kbps
    HELIOS_AMRWB_MODE_1985,    ///< 19.85 kbps
    HELIOS_AMRWB_MODE_2305,    ///< 23.05 kbps
    HELIOS_AMRWB_MODE_2385,    ///< 23.85 kbps

	HELIOS_AMRWB_MODE_MAX,
} Helios_AmrwbMode;

typedef enum
{
	HELIOS_AMRNB_MODE_NONE = 0,

    HELIOS_AMRNB_MODE_475, 	///< 4.75 kbps
    HELIOS_AMRNB_MODE_515,     ///< 5.15 kbps
    HELIOS_AMRNB_MODE_590,     ///< 5.90 kbps
    HELIOS_AMRNB_MODE_670,     ///< 6.70 kbps
    HELIOS_AMRNB_MODE_740,     ///< 7.40 kbps
    HELIOS_AMRNB_MODE_795,     ///< 7.95 kbps
    HELIOS_AMRNB_MODE_1020,    ///< 10.2 kbps
    HELIOS_AMRNB_MODE_1220,    ///< 12.2 kbps

	HELIOS_AMRNB_MODE_MAX,
} Helios_AmrnbMode;


typedef struct
{
	Helios_AmrwbMode amrwb_mode;
	int reserve[2];
}Helios_AmrwbParam;

typedef struct
{
	Helios_AmrnbMode amrnb_mode;
	int reserve[2];
}Helios_AmrnbParam;


typedef struct
{
	int channels;
	int samplerate;
	int len_size;

	Helios_AmrwbParam amrwb_param;
	Helios_AmrnbParam amrnb_param;
	
}Helios_AudRecordConfig;

typedef enum
{
    HELIOS_REC_TYPE_NONE = 0,
    HELIOS_REC_TYPE_MIC,
    HELIOS_REC_TYPE_VOICE,
}Helios_AudRecordType;

typedef enum
{
	HELIOS_AUD_RECORD_ERROR = -1,
	HELIOS_AUD_RECORD_START = 0,   //audio recorder start record
	HELIOS_AUD_RECORD_DATA,		//record data comming
	HELIOS_AUD_RECORD_PAUSE,	    //record has been paused by call or pause api
	HELIOS_AUD_RECORD_CLOSE,		//record closed
	HELIOS_AUD_RECORD_FINISHED = HELIOS_AUD_RECORD_CLOSE,
	HELIOS_AUD_RECORD_RESUME,		//resume from pause
}Helios_EnumAudRecordState;



typedef int(*helios_cb_on_record)(char *p_data, unsigned int len, Helios_EnumAudRecordState state);


typedef struct {
	Helios_AudRecordConfig *record_para;
	Helios_AudStreamFormat format;
	Helios_AudRecordType type;
	helios_cb_on_record cb;	
}Helios_AUDRecordInitStruct;


typedef void(*cb_on_record)(char *p_data, int len, enum_aud_record_state state);

typedef struct _aud_config
{
	int channels;
	int samplerate;
}_aud_config;


typedef struct helios_aud_record_config
{
	_aud_config conf;
	char* file_name;
	RECORD_FROMAT format;
	cb_on_record cb;
}_Aud_Record_CONFIG;

