
#ifndef _EAN_H_
#define _EAN_H_

/* state of each parallel decode attempt */
typedef struct ean_pass_s {
    signed char state;          /* module position of w[idx] in symbol */
#define STATE_ADDON 0x40        /*   scanning add-on */
#define STATE_IDX   0x1f        /*   element offset into symbol */
    unsigned char raw[7];       /* decode in process */
} ean_pass_t;

/* EAN/UPC specific decode state */
typedef struct ean_decoder_s {
    ean_pass_t pass[4];         /* state of each parallel decode attempt */
    quec_decoder_symbol_type_t left;   /* current holding buffer contents */
    quec_decoder_symbol_type_t right;
    quec_decoder_symbol_type_t addon;
    unsigned s4;                /* character width */
    signed char buf[18];        /* holding buffer */

    signed char enable;
    unsigned ean13_config;
    unsigned ean8_config;
    unsigned upca_config;
    unsigned upce_config;
    unsigned isbn10_config;
    unsigned isbn13_config;
} ean_decoder_t;

/* reset EAN/UPC pass specific state */
static inline void ean_new_scan (ean_decoder_t *ean)
{
    ean->pass[0].state = ean->pass[1].state = -1;
    ean->pass[2].state = ean->pass[3].state = -1;
    ean->s4 = 0;
}

/* reset all EAN/UPC state */
static inline void ean_reset (ean_decoder_t *ean)
{
    ean_new_scan(ean);
    ean->left = ean->right = ean->addon = QUEC_DECODER_NONE;
}

static inline unsigned ean_get_config (ean_decoder_t *ean,
                                       quec_decoder_symbol_type_t sym)
{
    switch(sym & QUEC_DECODER_SYMBOL) {
    case QUEC_DECODER_EAN13:  return(ean->ean13_config);
    case QUEC_DECODER_EAN8:   return(ean->ean8_config);
    case QUEC_DECODER_UPCA:   return(ean->upca_config);
    case QUEC_DECODER_UPCE:   return(ean->upce_config);
    case QUEC_DECODER_ISBN10: return(ean->isbn10_config);
    case QUEC_DECODER_ISBN13: return(ean->isbn13_config);
    default:           return(0);
    }
}

/* decode EAN/UPC symbols */
quec_decoder_symbol_type_t _quec_decoder_decode_ean(quec_decoder_decoder_t *dcode);

#endif
