cc_test {
    name: "AMRWBEncTest",
    gtest: false,

    srcs: ["AMRWB_E_SAMPLE.c"],

    arch: {
        arm: {
            instruction_set: "arm",
        },
    },

    shared_libs: [
        "libdl",
    ],

    static_libs: [
        "libstagefright_amrwbenc",
        "libstagefright_enc_common",
    ],

    sanitize: {
        cfi: true,
        diag: {
            cfi: true,
        },
    },
}
