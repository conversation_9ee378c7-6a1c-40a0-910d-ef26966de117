/* Copyright (C) 1999-2011 Free Software Foundation, Inc.
   This file is part of the GNU LIBICONV Library.

   The GNU LIBICONV Library is free software; you can redistribute it
   and/or modify it under the terms of the GNU Library General Public
   License as published by the Free Software Foundation; either version 2
   of the License, or (at your option) any later version.

   The GNU LIBICONV Library is distributed in the hope that it will be
   useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANT<PERSON><PERSON>ITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
   Library General Public License for more details.

   You should have received a copy of the GNU Library General Public
   License along with the GNU LIBICONV Library; see the file COPYING.LIB.
   If not, see <https://www.gnu.org/licenses/>.  */

/* The list of all system independent user-visible encodings. */

/* By convention, an encoding named FOOBAR or FOO_BAR or FOO-BAR is defined
   in a file named "foobar.h" through the functions foobar_mbtowc and
   foobar_wctomb (and possibly foobar_reset). */

/* DEFENCODING(( name, alias1, ..., ),
               xxx,
               { xxx_mbtowc, xxx_flushwc },
               { xxx_wctomb, xxx_reset })
   defines an encoding with the given name and aliases. (There is no
   difference between a name and an alias. By convention, the name is chosen
   as the preferred MIME name or the standard name.)
   All names and aliases must be in ASCII. Case is not significant, but
   for the "cs*" aliases mixed case is preferred, otherwise UPPERCASE is
   preferred. For all names and aliases, note where it comes from.
   xxx is the name as used in the C code (lowercase).
 */

#if 0
DEFENCODING(( "US-ASCII",               /* IANA */
              "ASCII",                  /* IANA, JDK 1.1 */
              "ISO646-US",              /* IANA */
              "ISO_646.IRV:1991",       /* IANA */
              "ISO-IR-6",               /* IANA */
              "ANSI_X3.4-1968",         /* IANA */
              "ANSI_X3.4-1986",         /* IANA */
              "CP367",                  /* IANA */
              "IBM367",                 /* IANA */
              "US",                     /* IANA */
              "csASCII",                /* IANA */
            /*"ISO646.1991-IRV",           X11R6.4 */
            ),
            ascii,
            { ascii_mbtowc, NULL },       { ascii_wctomb, NULL })
#ifdef USE_SOLARIS_ALIASES
DEFALIAS(     "646",                    /* Solaris */
            ascii)
#endif
#endif
/* General multi-byte encodings */

DEFENCODING(( "UTF-8",                  /* IANA, RFC 2279 */
            /*"UTF8",                      JDK 1.1 */
            /*"CP65001",                   Windows */
            ),
            utf8,
            { utf8_mbtowc, NULL },        { utf8_wctomb, NULL })
#ifdef USE_HPUX_ALIASES
DEFALIAS(     "UTF8",                   /* HP-UX */
            utf8)
#endif
#if 0
DEFENCODING(( "UCS-2",                  /* glibc */
              "ISO-10646-UCS-2",        /* IANA */
              "csUnicode",              /* IANA */
            ),
            ucs2,
            { ucs2_mbtowc, NULL },        { ucs2_wctomb, NULL })

DEFENCODING(( "UCS-2BE",                /* glibc */
              "UNICODEBIG",             /* glibc */
              "UNICODE-1-1",            /* IANA */
              "csUnicode11",            /* IANA */
            /*"CP1201",                    Windows */
            ),
            ucs2be,
            { ucs2be_mbtowc, NULL },      { ucs2be_wctomb, NULL })

DEFENCODING(( "UCS-2LE",                /* glibc */
              "UNICODELITTLE",          /* glibc */
            /*"CP1200",                    Windows */
            ),
            ucs2le,
            { ucs2le_mbtowc, NULL },      { ucs2le_wctomb, NULL })

DEFENCODING(( "UCS-4",                  /* glibc */
              "ISO-10646-UCS-4",        /* IANA */
              "csUCS4",                 /* IANA */
            ),
            ucs4,
            { ucs4_mbtowc, NULL },        { ucs4_wctomb, NULL })

DEFENCODING(( "UCS-4BE",                /* glibc */
            /*"CP12001",                   Windows */
            ),
            ucs4be,
            { ucs4be_mbtowc, NULL },      { ucs4be_wctomb, NULL })

DEFENCODING(( "UCS-4LE",                /* glibc */
            /*"CP12000",                   Windows */
            ),
            ucs4le,
            { ucs4le_mbtowc, NULL },      { ucs4le_wctomb, NULL })
#endif
#if 0
DEFENCODING(( "UTF-16",                 /* IANA, RFC 2781 */
            ),
            utf16,
            { utf16_mbtowc, NULL },       { utf16_wctomb, NULL })

DEFENCODING(( "UTF-16BE",               /* IANA, RFC 2781 */
            ),
            utf16be,
            { utf16be_mbtowc, NULL },     { utf16be_wctomb, NULL })

DEFENCODING(( "UTF-16LE",               /* IANA, RFC 2781 */
            ),
            utf16le,
            { utf16le_mbtowc, NULL },     { utf16le_wctomb, NULL })

DEFENCODING(( "UTF-32",                 /* IANA, Unicode 3.1 */
            ),
            utf32,
            { utf32_mbtowc, NULL },       { utf32_wctomb, NULL })

DEFENCODING(( "UTF-32BE",               /* IANA, Unicode 3.1 */
            ),
            utf32be,
            { utf32be_mbtowc, NULL },     { utf32be_wctomb, NULL })

DEFENCODING(( "UTF-32LE",               /* IANA, Unicode 3.1 */
            ),
            utf32le,
            { utf32le_mbtowc, NULL },     { utf32le_wctomb, NULL })

DEFENCODING(( "UTF-7",                  /* IANA, RFC 2152 */
              "UNICODE-1-1-UTF-7",      /* IANA, RFC 1642 */
              "csUnicode11UTF7",        /* IANA */
            /*"CP65000",                   Windows */
            ),
            utf7,
            { utf7_mbtowc, NULL },        { utf7_wctomb, utf7_reset })
#endif
#if 0
DEFENCODING(( "UCS-2-INTERNAL",         /* libiconv */
            ),
            ucs2internal,
            { ucs2internal_mbtowc, NULL }, { ucs2internal_wctomb, NULL })

DEFENCODING(( "UCS-2-SWAPPED",          /* libiconv */
            ),
            ucs2swapped,
            { ucs2swapped_mbtowc, NULL }, { ucs2swapped_wctomb, NULL })

DEFENCODING(( "UCS-4-INTERNAL",         /* libiconv */
            ),
            ucs4internal,
            { ucs4internal_mbtowc, NULL },{ ucs4internal_wctomb, NULL })

DEFENCODING(( "UCS-4-SWAPPED",          /* libiconv */
            ),
            ucs4swapped,
            { ucs4swapped_mbtowc, NULL }, { ucs4swapped_wctomb, NULL })
#endif
#if 0
DEFENCODING(( "C99",
            ),
            c99,
            { c99_mbtowc, NULL },         { c99_wctomb, NULL })

DEFENCODING(( "JAVA",
            ),
            java,
            { java_mbtowc, NULL },        { java_wctomb, NULL })
#endif
/* Standard 8-bit encodings */

DEFENCODING(( "ISO-8859-1",             /* IANA */
              "ISO_8859-1",             /* IANA */
              "ISO_8859-1:1987",        /* IANA */
              "ISO-IR-100",             /* IANA */
              "CP819",                  /* IANA */
              "IBM819",                 /* IANA */
              "LATIN1",                 /* IANA */
              "L1",                     /* IANA */
              "csISOLatin1",            /* IANA */
              "ISO8859-1",              /* X11R6.4, glibc, FreeBSD, AIX, IRIX, OSF/1, Solaris */
            /*"ISO8859_1",                 JDK 1.1 */
            /*"CP28591",                   Windows */
            ),
            iso8859_1,
            { iso8859_1_mbtowc, NULL },   { iso8859_1_wctomb, NULL })
#ifdef USE_HPUX_ALIASES
DEFALIAS(     "ISO88591",               /* HP-UX */
            iso8859_1)
#endif
#if 0
DEFENCODING(( "ISO-8859-2",             /* IANA */
              "ISO_8859-2",             /* IANA */
              "ISO_8859-2:1987",        /* IANA */
              "ISO-IR-101",             /* IANA */
              "LATIN2",                 /* IANA */
              "L2",                     /* IANA */
              "csISOLatin2",            /* IANA */
              "ISO8859-2",              /* X11R6.4, glibc, FreeBSD, AIX, IRIX, OSF/1, Solaris */
            /*"ISO8859_2",                 JDK 1.1 */
            /*"CP28592",                   Windows */
            ),
            iso8859_2,
            { iso8859_2_mbtowc, NULL },   { iso8859_2_wctomb, NULL })
#ifdef USE_HPUX_ALIASES
DEFALIAS(     "ISO88592",               /* HP-UX */
            iso8859_2)
#endif

DEFENCODING(( "ISO-8859-3",             /* IANA */
              "ISO_8859-3",             /* IANA */
              "ISO_8859-3:1988",        /* IANA */
              "ISO-IR-109",             /* IANA */
              "LATIN3",                 /* IANA */
              "L3",                     /* IANA */
              "csISOLatin3",            /* IANA */
              "ISO8859-3",              /* X11R6.4, glibc, FreeBSD, Solaris */
            /*"ISO8859_3",                 JDK 1.1 */
            /*"CP28593",                   Windows */
            ),
            iso8859_3,
            { iso8859_3_mbtowc, NULL },   { iso8859_3_wctomb, NULL })

DEFENCODING(( "ISO-8859-4",             /* IANA */
              "ISO_8859-4",             /* IANA */
              "ISO_8859-4:1988",        /* IANA */
              "ISO-IR-110",             /* IANA */
              "LATIN4",                 /* IANA */
              "L4",                     /* IANA */
              "csISOLatin4",            /* IANA */
              "ISO8859-4",              /* X11R6.4, glibc, FreeBSD, OSF/1, Solaris */
            /*"ISO8859_4",                 JDK 1.1 */
            /*"CP28594",                   Windows */
            ),
            iso8859_4,
            { iso8859_4_mbtowc, NULL },   { iso8859_4_wctomb, NULL })

DEFENCODING(( "ISO-8859-5",             /* IANA */
              "ISO_8859-5",             /* IANA */
              "ISO_8859-5:1988",        /* IANA */
              "ISO-IR-144",             /* IANA */
              "CYRILLIC",               /* IANA */
              "csISOLatinCyrillic",     /* IANA */
              "ISO8859-5",              /* X11R6.4, glibc, FreeBSD, AIX, IRIX, OSF/1, Solaris */
            /*"ISO8859_5",                 JDK 1.1 */
            /*"CP28595",                   Windows */
            ),
            iso8859_5,
            { iso8859_5_mbtowc, NULL },   { iso8859_5_wctomb, NULL })
#ifdef USE_HPUX_ALIASES
DEFALIAS(     "ISO88595",               /* HP-UX */
            iso8859_5)
#endif

DEFENCODING(( "ISO-8859-6",             /* IANA */
              "ISO_8859-6",             /* IANA */
              "ISO_8859-6:1987",        /* IANA */
              "ISO-IR-127",             /* IANA */
              "ECMA-114",               /* IANA */
              "ASMO-708",               /* IANA */
              "ARABIC",                 /* IANA */
              "csISOLatinArabic",       /* IANA */
              "ISO8859-6",              /* X11R6.4, glibc, FreeBSD, AIX, Solaris */
            /*"ISO8859_6",                 JDK 1.1 */
            /*"CP28596",                   Windows */
            ),
            iso8859_6,
            { iso8859_6_mbtowc, NULL },   { iso8859_6_wctomb, NULL })
#ifdef USE_HPUX_ALIASES
DEFALIAS(     "ISO88596",               /* HP-UX */
            iso8859_6)
#endif

DEFENCODING(( "ISO-8859-7",             /* IANA, RFC 1947 */
              "ISO_8859-7",             /* IANA */
              "ISO_8859-7:1987",        /* IANA */
              "ISO_8859-7:2003",
              "ISO-IR-126",             /* IANA */
              "ECMA-118",               /* IANA */
              "ELOT_928",               /* IANA */
              "GREEK8",                 /* IANA */
              "GREEK",                  /* IANA */
              "csISOLatinGreek",        /* IANA */
              "ISO8859-7",              /* X11R6.4, glibc, FreeBSD, AIX, IRIX, OSF/1, Solaris */
            /*"ISO8859_7",                 JDK 1.1 */
            /*"CP28597",                   Windows */
            ),
            iso8859_7,
            { iso8859_7_mbtowc, NULL },   { iso8859_7_wctomb, NULL })
#ifdef USE_HPUX_ALIASES
DEFALIAS(     "ISO88597",               /* HP-UX */
            iso8859_7)
#endif

DEFENCODING(( "ISO-8859-8",             /* IANA */
              "ISO_8859-8",             /* IANA */
              "ISO_8859-8:1988",        /* IANA */
              "ISO-IR-138",             /* IANA */
              "HEBREW",                 /* IANA */
              "csISOLatinHebrew",       /* IANA */
              "ISO8859-8",              /* X11R6.4, glibc, FreeBSD, AIX, OSF/1, Solaris */
            /*"ISO8859_8",                 JDK 1.1 */
            /*"CP28598",                   Windows */
            /*"CP38598",                   Windows */
            ),
            iso8859_8,
            { iso8859_8_mbtowc, NULL },   { iso8859_8_wctomb, NULL })
#ifdef USE_HPUX_ALIASES
DEFALIAS(     "ISO88598",               /* HP-UX */
            iso8859_8)
#endif

DEFENCODING(( "ISO-8859-9",             /* IANA */
              "ISO_8859-9",             /* IANA */
              "ISO_8859-9:1989",        /* IANA */
              "ISO-IR-148",             /* IANA */
              "LATIN5",                 /* IANA */
              "L5",                     /* IANA */
              "csISOLatin5",            /* IANA */
              "ISO8859-9",              /* X11R6.4, glibc, FreeBSD, AIX, IRIX, OSF/1, Solaris */
            /*"ISO8859_9",                 JDK 1.1 */
            /*"CP28599",                   Windows */
            ),
            iso8859_9,
            { iso8859_9_mbtowc, NULL },   { iso8859_9_wctomb, NULL })
#ifdef USE_HPUX_ALIASES
DEFALIAS(     "ISO88599",               /* HP-UX */
            iso8859_9)
#endif

DEFENCODING(( "ISO-8859-10",            /* IANA */
              "ISO_8859-10",
              "ISO_8859-10:1992",       /* IANA */
              "ISO-IR-157",             /* IANA */
              "LATIN6",                 /* IANA */
              "L6",                     /* IANA */
              "csISOLatin6",            /* IANA */
              "ISO8859-10",             /* X11R6.4, glibc, FreeBSD */
            ),
            iso8859_10,
            { iso8859_10_mbtowc, NULL },  { iso8859_10_wctomb, NULL })

DEFENCODING(( "ISO-8859-11",            /* glibc */
              "ISO_8859-11",
              "ISO8859-11",             /* X11R6.7, glibc */
            ),
            iso8859_11,
            { iso8859_11_mbtowc, NULL },  { iso8859_11_wctomb, NULL })

DEFENCODING(( "ISO-8859-13",            /* IANA, glibc */
              "ISO_8859-13",
              "ISO-IR-179",             /* glibc */
              "LATIN7",                 /* glibc */
              "L7",                     /* glibc */
              "ISO8859-13",             /* glibc, FreeBSD */
            /*"CP28603",                   Windows */
            ),
            iso8859_13,
            { iso8859_13_mbtowc, NULL },  { iso8859_13_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-921",                /* AIX */
            iso8859_13)
#endif

DEFENCODING(( "ISO-8859-14",            /* IANA, glibc */
              "ISO_8859-14",            /* IANA */
              "ISO_8859-14:1998",       /* IANA, glibc */
              "ISO-IR-199",             /* IANA */
              "LATIN8",                 /* IANA, glibc */
              "L8",                     /* IANA, glibc */
              "ISO-CELTIC",             /* IANA */
              "ISO8859-14",             /* glibc, FreeBSD */
            ),
            iso8859_14,
            { iso8859_14_mbtowc, NULL },  { iso8859_14_wctomb, NULL })

DEFENCODING(( "ISO-8859-15",            /* IANA, glibc */
              "ISO_8859-15",            /* IANA */
              "ISO_8859-15:1998",       /* glibc */
              "ISO-IR-203",
              "LATIN-9",                /* IANA */
              "ISO8859-15",             /* glibc, FreeBSD, AIX, OSF/1, Solaris */
            /*"CP28605",                   Windows */
            ),
            iso8859_15,
            { iso8859_15_mbtowc, NULL },  { iso8859_15_wctomb, NULL })
#ifdef USE_HPUX_ALIASES
DEFALIAS(     "ISO885915",              /* HP-UX */
            iso8859_15)
#endif

DEFENCODING(( "ISO-8859-16",            /* IANA */
              "ISO_8859-16",            /* IANA */
              "ISO_8859-16:2001",       /* IANA */
              "ISO-IR-226",             /* IANA */
              "LATIN10",                /* IANA */
              "L10",                    /* IANA */
              "ISO8859-16",             /* glibc, FreeBSD */
            ),
            iso8859_16,
            { iso8859_16_mbtowc, NULL },  { iso8859_16_wctomb, NULL })
#endif
#if 0
DEFENCODING(( "KOI8-R",                 /* IANA, RFC 1489, X11R6.4, JDK 1.1 */
              "csKOI8R",                /* IANA */
            /*"CP20866",                   Windows */
            ),
            koi8_r,
            { koi8_r_mbtowc, NULL },      { koi8_r_wctomb, NULL })

DEFENCODING(( "KOI8-U",                 /* IANA, RFC 2319 */
            ),
            koi8_u,
            { koi8_u_mbtowc, NULL },      { koi8_u_wctomb, NULL })

DEFENCODING(( "KOI8-RU",
            /*"CP21866",                   Windows */
            ),
            koi8_ru,
            { koi8_ru_mbtowc, NULL },     { koi8_ru_wctomb, NULL })
#endif
/* Windows 8-bit encodings */
#if 0
DEFENCODING(( "CP1250",                 /* JDK 1.1 */
              "WINDOWS-1250",           /* IANA */
              "MS-EE",
            ),
            cp1250,
            { cp1250_mbtowc, NULL },      { cp1250_wctomb, NULL })

DEFENCODING(( "CP1251",                 /* JDK 1.1 */
              "WINDOWS-1251",           /* IANA */
              "MS-CYRL",
            ),
            cp1251,
            { cp1251_mbtowc, NULL },      { cp1251_wctomb, NULL })
#ifdef USE_SOLARIS_ALIASES
DEFALIAS(     "ANSI-1251",              /* Solaris */
            cp1251)
#endif

DEFENCODING(( "CP1252",                 /* JDK 1.1 */
              "WINDOWS-1252",           /* IANA */
              "MS-ANSI",
            ),
            cp1252,
            { cp1252_mbtowc, NULL },      { cp1252_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-1252",               /* AIX */
            cp1252)
#endif

DEFENCODING(( "CP1253",                 /* JDK 1.1 */
              "WINDOWS-1253",           /* IANA */
              "MS-GREEK",
            ),
            cp1253,
            { cp1253_mbtowc, NULL },      { cp1253_wctomb, NULL })

DEFENCODING(( "CP1254",                 /* JDK 1.1 */
              "WINDOWS-1254",           /* IANA */
              "MS-TURK",
            ),
            cp1254,
            { cp1254_mbtowc, NULL },      { cp1254_wctomb, NULL })

DEFENCODING(( "CP1255",                 /* JDK 1.1 */
              "WINDOWS-1255",           /* IANA */
              "MS-HEBR",
            ),
            cp1255,
            { cp1255_mbtowc, cp1255_flushwc }, { cp1255_wctomb, NULL })

DEFENCODING(( "CP1256",                 /* JDK 1.1 */
              "WINDOWS-1256",           /* IANA */
              "MS-ARAB",
            ),
            cp1256,
            { cp1256_mbtowc, NULL },      { cp1256_wctomb, NULL })

DEFENCODING(( "CP1257",                 /* JDK 1.1 */
              "WINDOWS-1257",           /* IANA */
              "WINBALTRIM",
            ),
            cp1257,
            { cp1257_mbtowc, NULL },      { cp1257_wctomb, NULL })

DEFENCODING(( "CP1258",                 /* JDK 1.1 */
              "WINDOWS-1258",           /* IANA */
            ),
            cp1258,
            { cp1258_mbtowc, cp1258_flushwc }, { cp1258_wctomb, NULL })

/* DOS 8-bit encodings */

DEFENCODING(( "CP850",                  /* IANA, JDK 1.1 */
              "IBM850",                 /* IANA */
              "850",                    /* IANA */
              "csPC850Multilingual",    /* IANA */
            ),
            cp850,
            { cp850_mbtowc, NULL },       { cp850_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-850",                /* AIX */
            cp850)
#endif

DEFENCODING(( "CP862",                  /* IANA, JDK 1.1 */
              "IBM862",                 /* IANA */
              "862",                    /* IANA */
              "csPC862LatinHebrew",     /* IANA */
            ),
            cp862,
            { cp862_mbtowc, NULL },       { cp862_wctomb, NULL })

DEFENCODING(( "CP866",                  /* IANA, JDK 1.1 */
              "IBM866",                 /* IANA */
              "866",                    /* IANA */
              "csIBM866",               /* IANA */
            ),
            cp866,
            { cp866_mbtowc, NULL },       { cp866_wctomb, NULL })

DEFENCODING(( "CP1131",                 /* FreeBSD, MacOS X */
            ),
            cp1131,
            { cp1131_mbtowc, NULL },      { cp1131_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-1131",               /* AIX */
            cp1131)
#endif
#endif
/* Macintosh 8-bit encodings */
#if 0
DEFENCODING(( "MacRoman",               /* JDK 1.1 */
              /* This is the best table for MACINTOSH. The ones */
              /* in glibc and FreeBSD-iconv are bad quality. */
              "MACINTOSH",              /* IANA */
              "MAC",                    /* IANA */
              "csMacintosh",            /* IANA */
            /*"CP10000",                   Windows */
            ),
            mac_roman,
            { mac_roman_mbtowc, NULL },   { mac_roman_wctomb, NULL })

DEFENCODING(( "MacCentralEurope",       /* JDK 1.1 */
            /*"CP10029",                   Windows */
            ),
            mac_centraleurope,
            { mac_centraleurope_mbtowc, NULL }, { mac_centraleurope_wctomb, NULL })

DEFENCODING(( "MacIceland",             /* JDK 1.1 */
            /*"CP10079",                   Windows */
            ),
            mac_iceland,
            { mac_iceland_mbtowc, NULL }, { mac_iceland_wctomb, NULL })

DEFENCODING(( "MacCroatian",            /* JDK 1.1 */
            /*"CP10082",                   Windows */
            ),
            mac_croatian,
            { mac_croatian_mbtowc, NULL }, { mac_croatian_wctomb, NULL })

DEFENCODING(( "MacRomania",             /* JDK 1.1 */
            /*"CP10010",                   Windows */
            ),
            mac_romania,
            { mac_romania_mbtowc, NULL }, { mac_romania_wctomb, NULL })

DEFENCODING(( "MacCyrillic",            /* JDK 1.1 */
            /*"CP10007",                   Windows */
            ),
            mac_cyrillic,
            { mac_cyrillic_mbtowc, NULL }, { mac_cyrillic_wctomb, NULL })

DEFENCODING(( "MacUkraine",             /* JDK 1.1 */
            /*"CP10017",                   Windows */
            ),
            mac_ukraine,
            { mac_ukraine_mbtowc, NULL }, { mac_ukraine_wctomb, NULL })

DEFENCODING(( "MacGreek",               /* JDK 1.1 */
            /*"CP10006",                   Windows */
            ),
            mac_greek,
            { mac_greek_mbtowc, NULL },   { mac_greek_wctomb, NULL })

DEFENCODING(( "MacTurkish",             /* JDK 1.1 */
            /*"CP10081",                   Windows */
            ),
            mac_turkish,
            { mac_turkish_mbtowc, NULL }, { mac_turkish_wctomb, NULL })

DEFENCODING(( "MacHebrew",              /* JDK 1.1 */
            /*"CP10005",                   Windows */
            ),
            mac_hebrew,
            { mac_hebrew_mbtowc, NULL },  { mac_hebrew_wctomb, NULL })

DEFENCODING(( "MacArabic",              /* JDK 1.1 */
            /*"CP10004",                   Windows */
            ),
            mac_arabic,
            { mac_arabic_mbtowc, NULL },  { mac_arabic_wctomb, NULL })

DEFENCODING(( "MacThai",                /* JDK 1.1 */
            /*"CP10021",                   Windows */
            ),
            mac_thai,
            { mac_thai_mbtowc, NULL },    { mac_thai_wctomb, NULL })
#endif
/* Other platform specific 8-bit encodings */
#if 0
DEFENCODING(( "HP-ROMAN8",              /* IANA, X11R6.4 */
              "ROMAN8",                 /* IANA */
              "R8",                     /* IANA */
              "csHPRoman8",             /* IANA */
            ),
            hp_roman8,
            { hp_roman8_mbtowc, NULL },   { hp_roman8_wctomb, NULL })

DEFENCODING(( "NEXTSTEP",
            ),
            nextstep,
            { nextstep_mbtowc, NULL },    { nextstep_wctomb, NULL })

/* Regional 8-bit encodings used for a single language */

DEFENCODING(( "ARMSCII-8",
            ),
            armscii_8,
            { armscii_8_mbtowc, NULL },   { armscii_8_wctomb, NULL })

DEFENCODING(( "GEORGIAN-ACADEMY",
            ),
            georgian_academy,
            { georgian_academy_mbtowc, NULL }, { georgian_academy_wctomb, NULL })

DEFENCODING(( "GEORGIAN-PS",
            ),
            georgian_ps,
            { georgian_ps_mbtowc, NULL }, { georgian_ps_wctomb, NULL })

DEFENCODING(( "KOI8-T",
            ),
            koi8_t,
            { koi8_t_mbtowc, NULL },      { koi8_t_wctomb, NULL })

DEFENCODING(( "PT154",                  /* IANA, glibc */
              "PTCP154",                /* IANA */
              "CP154",                  /* IANA */
              "CYRILLIC-ASIAN",         /* IANA */
              "csPTCP154",              /* IANA */
            ),
            pt154,
            { pt154_mbtowc, NULL },       { pt154_wctomb, NULL })

DEFENCODING(( "RK1048",                 /* IANA, glibc */
              "STRK1048-2002",          /* IANA */
              "KZ-1048",                /* IANA */
              "csKZ1048",               /* IANA */
            ),
            rk1048,
            { rk1048_mbtowc, NULL },      { rk1048_wctomb, NULL })

DEFENCODING(( "MULELAO-1",
            ),
            mulelao,
            { mulelao_mbtowc, NULL },     { mulelao_wctomb, NULL })

DEFENCODING(( "CP1133",
              "IBM-CP1133",
            ),
            cp1133,
            { cp1133_mbtowc, NULL },      { cp1133_wctomb, NULL })

DEFENCODING(( "TIS-620",                /* IANA */
              "TIS620",                 /* glibc, HP-UX */
              "TIS620-0",               /* glibc */
              "TIS620.2529-1",          /* glibc */
              "TIS620.2533-0",          /* glibc */
              "TIS620.2533-1",
              "ISO-IR-166",             /* glibc */
            ),
            tis620,
            { tis620_mbtowc, NULL },      { tis620_wctomb, NULL })
#ifdef USE_OSF1_ALIASES
DEFALIAS(     "TACTIS",                 /* OSF/1 */
            tis620)
#endif
#ifdef USE_SOLARIS_ALIASES
DEFALIAS(     "TIS620.2533",            /* Solaris */
            tis620)
#endif

DEFENCODING(( "CP874",                  /* JDK 1.1 */
              "WINDOWS-874",
            ),
            cp874,
            { cp874_mbtowc, NULL },       { cp874_wctomb, NULL })

DEFENCODING(( "VISCII",                 /* IANA, RFC 1456 */
              "VISCII1.1-1",
              "csVISCII",               /* IANA */
            ),
            viscii,
            { viscii_mbtowc, NULL },      { viscii_wctomb, NULL })

DEFENCODING(( "TCVN",
              "TCVN-5712",
              "TCVN5712-1",
              "TCVN5712-1:1993",
            ),
            tcvn,
            { tcvn_mbtowc, tcvn_flushwc }, { tcvn_wctomb, NULL })
#endif
/* CJK character sets (not documented) */
#if 0
DEFENCODING(( "JIS_C6220-1969-RO",      /* IANA */
              "ISO646-JP",              /* IANA */
              "ISO-IR-14",              /* IANA */
              "JP",                     /* IANA */
              "csISO14JISC6220ro",      /* IANA */
            ),
            iso646_jp,
            { iso646_jp_mbtowc, NULL },   { iso646_jp_wctomb, NULL })

DEFENCODING(( "JIS_X0201",              /* IANA */
              "JISX0201-1976",
              "X0201",                  /* IANA */
              "csHalfWidthKatakana",    /* IANA */
            /*"JISX0201.1976-0",           X11R6.4 */
            /*"JIS0201",                   JDK 1.1 */
            ),
            jisx0201,
            { jisx0201_mbtowc, NULL },    { jisx0201_wctomb, NULL })

DEFENCODING(( "JIS_X0208",
              "JIS_X0208-1983",         /* IANA */
              "JIS_X0208-1990",
              "JIS0208",
              "X0208",                  /* IANA */
              "ISO-IR-87",              /* IANA */
              "JIS_C6226-1983",         /* IANA */
              "csISO87JISX0208",        /* IANA */
            /*"JISX0208.1983-0",           X11R6.4 */
            /*"JISX0208.1990-0",           X11R6.4 */
            /*"JIS0208",                   JDK 1.1 */
            ),
            jisx0208,
            { jisx0208_mbtowc, NULL },    { jisx0208_wctomb, NULL })

DEFENCODING(( "JIS_X0212",
              "JIS_X0212.1990-0",
              "JIS_X0212-1990",         /* IANA */
              "X0212",                  /* IANA */
              "ISO-IR-159",             /* IANA */
              "csISO159JISX02121990",   /* IANA */
            /*"JISX0212.1990-0",           X11R6.4 */
            /*"JIS0212",                   JDK 1.1 */
            ),
            jisx0212,
            { jisx0212_mbtowc, NULL },    { jisx0212_wctomb, NULL })
#endif
#if 0
DEFENCODING(( "GB_1988-80",             /* IANA */
              "ISO646-CN",              /* IANA */
              "ISO-IR-57",              /* IANA */
              "CN",                     /* IANA */
              "csISO57GB1988",          /* IANA */
            ),
            iso646_cn,
            { iso646_cn_mbtowc, NULL },   { iso646_cn_wctomb, NULL })

DEFENCODING(( "GB_2312-80",             /* IANA */
              "ISO-IR-58",              /* IANA */
              "csISO58GB231280",        /* IANA */
              "CHINESE",                /* IANA */
            /*"GB2312.1980-0",             X11R6.4 */
            ),
            gb2312,
            { gb2312_mbtowc, NULL },      { gb2312_wctomb, NULL })

DEFENCODING(( "ISO-IR-165",
              "CN-GB-ISOIR165",         /* RFC 1922 */
            ),
            isoir165,
            { isoir165_mbtowc, NULL },    { isoir165_wctomb, NULL })

DEFENCODING(( "KSC_5601",               /* IANA */
              "KS_C_5601-1987",         /* IANA */
              "KS_C_5601-1989",         /* IANA */
              "ISO-IR-149",             /* IANA */
              "csKSC56011987",          /* IANA */
              "KOREAN",                 /* IANA */
            /*"KSC5601.1987-0",            X11R6.4 */
            /*"KSX1001:1992",              Ken Lunde */
            ),
            ksc5601,
            { ksc5601_mbtowc, NULL },     { ksc5601_wctomb, NULL })
#endif
/* CJK encodings */
#if 0
DEFENCODING(( "EUC-JP",                 /* IANA */
              "EUCJP",                  /* glibc, HP-UX, IRIX, OSF/1, Solaris */
              "Extended_UNIX_Code_Packed_Format_for_Japanese", /* IANA */
              "csEUCPkdFmtJapanese",    /* IANA */
            /*"EUC_JP",                    JDK 1.1 */
            /*"CP51932",                   Windows */
            ),
            euc_jp,
            { euc_jp_mbtowc, NULL },      { euc_jp_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-EUCJP",              /* AIX */
            euc_jp)
#endif
#ifdef USE_OSF1_ALIASES
DEFALIAS(     "SDECKANJI",              /* OSF/1 */
            euc_jp)
#endif
#endif
#if 0
DEFENCODING(( "SHIFT_JIS",              /* IANA */
              "SHIFT-JIS",              /* glibc */
              "SJIS",                   /* JDK 1.1, HP-UX, OSF/1 */
              "MS_KANJI",               /* IANA */
              "csShiftJIS",             /* IANA */
            ),
            sjis,
            { sjis_mbtowc, NULL },        { sjis_wctomb, NULL })
#endif
#ifdef USE_SOLARIS_ALIASES
DEFALIAS(     "PCK",                    /* Solaris */
            sjis)
#endif
#if 0
DEFENCODING(( "CP932",                  /* glibc */
            ),
            cp932,
            { cp932_mbtowc, NULL },       { cp932_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-932",                /* AIX */
            cp932)
#endif

DEFENCODING(( "ISO-2022-JP",            /* IANA, RFC 1468 */
              "csISO2022JP",            /* IANA */
            /*"ISO2022JP",                 JDK 1.1 */
            ),
            iso2022_jp,
            { iso2022_jp_mbtowc, NULL },  { iso2022_jp_wctomb, iso2022_jp_reset })

DEFENCODING(( "ISO-2022-JP-1",          /* RFC 2237 */
            ),
            iso2022_jp1,
            { iso2022_jp1_mbtowc, NULL }, { iso2022_jp1_wctomb, iso2022_jp1_reset })

DEFENCODING(( "ISO-2022-JP-2",          /* IANA, RFC 1554 */
              "csISO2022JP2",           /* IANA */
            ),
            iso2022_jp2,
            { iso2022_jp2_mbtowc, NULL }, { iso2022_jp2_wctomb, iso2022_jp2_reset })

DEFENCODING(( "ISO-2022-JP-MS",
              "CP50221",
            /*"ISO-2022-JP-ESC",           Windows */
            ),
            iso2022_jpms,
            { iso2022_jpms_mbtowc, NULL }, { iso2022_jpms_wctomb, iso2022_jpms_reset })
#endif
#if 0
DEFENCODING(( "EUC-CN",                 /* glibc */
              "EUCCN",                  /* glibc, IRIX */
              "GB2312",                 /* IANA */
              "CN-GB",                  /* RFC 1922 */
              "csGB2312",               /* IANA */
            /*"EUC_CN",                    JDK 1.1 */
            /*"CP51936",                   Windows */
            ),
            euc_cn,
            { euc_cn_mbtowc, NULL },      { euc_cn_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-EUCCN",              /* AIX */
            euc_cn)
#endif
#ifdef USE_HPUX_ALIASES
DEFALIAS(     "HP15CN",                 /* HP-UX */
            euc_cn)
#endif
#ifdef USE_OSF1_ALIASES
DEFALIAS(     "DECHANZI",               /* OSF/1 */
            euc_cn)
#endif

DEFENCODING(( "GBK",                    /* IANA, JDK 1.1 */
            ),
            ces_gbk,
            { ces_gbk_mbtowc, NULL },     { ces_gbk_wctomb, NULL })

DEFENCODING(( "CP936",                  /* IANA */
              "MS936",                  /* IANA */
              "WINDOWS-936",            /* IANA */
            ),
            cp936,
            { cp936_mbtowc, NULL },       { cp936_wctomb, NULL })

DEFENCODING(( "GB18030",                /* IANA, glibc */
            /*"CP54936",                   Windows */
            ),
            gb18030,
            { gb18030_mbtowc, NULL },     { gb18030_wctomb, NULL })

DEFENCODING(( "ISO-2022-CN",            /* IANA, RFC 1922 */
              "csISO2022CN",
            /*"ISO2022CN",                 JDK 1.1 */
            ),
            iso2022_cn,
            { iso2022_cn_mbtowc, NULL },  { iso2022_cn_wctomb, iso2022_cn_reset })

DEFENCODING(( "ISO-2022-CN-EXT",        /* IANA, RFC 1922 */
            ),
            iso2022_cn_ext,
            { iso2022_cn_ext_mbtowc, NULL }, { iso2022_cn_ext_wctomb, iso2022_cn_ext_reset })

DEFENCODING(( "HZ",                     /* RFC 1843 */
              "HZ-GB-2312",             /* IANA, RFC 1842 */
            /*"CP52936",                   Windows */
            ),
            hz,
            { hz_mbtowc, NULL },          { hz_wctomb, hz_reset })

DEFENCODING(( "EUC-TW",                 /* glibc */
              "EUCTW",                  /* glibc, HP-UX, IRIX, OSF/1 */
              "csEUCTW",
            /*"EUC_TW",                    JDK 1.1 */
            /*"CP51950",                   Windows */
            ),
            euc_tw,
            { euc_tw_mbtowc, NULL },      { euc_tw_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-EUCTW",              /* AIX */
            euc_tw)
#endif
#ifdef USE_SOLARIS_ALIASES
DEFALIAS(     "CNS11643",               /* Solaris */
            euc_tw)
#endif

DEFENCODING(( "BIG5",                   /* IANA, JDK 1.1 */
              "BIG-5",                  /* glibc */
              "BIG-FIVE",               /* glibc */
              "BIGFIVE",                /* glibc */
              "CN-BIG5",                /* RFC 1922 */
              "csBig5",                 /* IANA */
            ),
            ces_big5,
            { ces_big5_mbtowc, NULL },    { ces_big5_wctomb, NULL })

DEFENCODING(( "CP950",                  /* JDK 1.1 */
            ),
            cp950,
            { cp950_mbtowc, NULL },       { cp950_wctomb, NULL })

DEFENCODING(( "BIG5-HKSCS:1999",
            ),
            big5hkscs1999,
            { big5hkscs1999_mbtowc, big5hkscs1999_flushwc }, { big5hkscs1999_wctomb, big5hkscs1999_reset })

DEFENCODING(( "BIG5-HKSCS:2001",
            ),
            big5hkscs2001,
            { big5hkscs2001_mbtowc, big5hkscs2001_flushwc }, { big5hkscs2001_wctomb, big5hkscs2001_reset })

DEFENCODING(( "BIG5-HKSCS:2004",
            ),
            big5hkscs2004,
            { big5hkscs2004_mbtowc, big5hkscs2004_flushwc }, { big5hkscs2004_wctomb, big5hkscs2004_reset })

DEFENCODING(( "BIG5-HKSCS",             /* IANA */
              "BIG5HKSCS",              /* glibc */
              "BIG5-HKSCS:2008",
            ),
            big5hkscs2008,
            { big5hkscs2008_mbtowc, big5hkscs2008_flushwc }, { big5hkscs2008_wctomb, big5hkscs2008_reset })

DEFENCODING(( "EUC-KR",                 /* IANA, RFC 1557 */
              "EUCKR",                  /* glibc, HP-UX, IRIX, OSF/1 */
              "csEUCKR",                /* IANA */
            /*"EUC_KR",                    JDK 1.1 */
            /*"CP51949",                   Windows */
            ),
            euc_kr,
            { euc_kr_mbtowc, NULL },      { euc_kr_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-EUCKR",              /* AIX */
            euc_kr)
#endif
#ifdef USE_OSF1_ALIASES
DEFALIAS(     "DECKOREAN",              /* OSF/1 */
            euc_kr)
#endif
#ifdef USE_SOLARIS_ALIASES
DEFALIAS(     "5601",                   /* Solaris */
            euc_kr)
#endif

DEFENCODING(( "CP949",                  /* JDK 1.1 */
              "UHC",                    /* glibc */
            ),
            cp949,
            { cp949_mbtowc, NULL },       { cp949_wctomb, NULL })
#ifdef USE_OSF1_ALIASES
DEFALIAS(     "KSC5601",                /* OSF/1 */
            cp949)
#endif

DEFENCODING(( "JOHAB",                  /* glibc */
              "CP1361",                 /* glibc */
            ),
            johab,
            { johab_mbtowc, NULL },       { johab_wctomb, NULL })
#ifdef USE_SOLARIS_ALIASES
DEFALIAS(     "KO_KR.JOHAP92",          /* Solaris */
            johab)
#endif

DEFENCODING(( "ISO-2022-KR",            /* IANA, RFC 1557 */
              "csISO2022KR",            /* IANA */
            /*"ISO2022KR",                 JDK 1.1 */
            /*"CP50225",                   Windows */
            ),
            iso2022_kr,
            { iso2022_kr_mbtowc, NULL },  { iso2022_kr_wctomb, iso2022_kr_reset })
#endif
