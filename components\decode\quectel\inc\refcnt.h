
#ifndef _REFCNT_H_
#define _REFCNT_H_

#include "quec_decoder_config.h"
#include <assert.h>

#if 0
# include <windows.h>

typedef volatile LONG refcnt_t;  /* FIXME where did volatile come from? */

static inline int _quec_decoder_refcnt (refcnt_t *cnt,
                                int delta)
{
    int rc = -1;
    if(delta > 0)
        while(delta--)
            rc = InterlockedIncrement(cnt);
    else if(delta < 0)
        while(delta++)
            rc = InterlockedDecrement(cnt);
    assert(rc >= 0);
    return(rc);
}


#include <pthread.h>

typedef int refcnt_t;

extern pthread_mutex_t _quec_decoder_reflock;

static inline int _quec_decoder_refcnt (refcnt_t *cnt,
                                int delta)
{
    pthread_mutex_lock(&_quec_decoder_reflock);
    int rc = (*cnt += delta);
    pthread_mutex_unlock(&_quec_decoder_reflock);
    assert(rc >= 0);
    return(rc);
}


#else

typedef int refcnt_t;

static inline int _quec_decoder_refcnt (refcnt_t *cnt, int delta)
{
    int rc = (*cnt += delta);
    return(rc);
}

#endif


void _quec_decoder_refcnt_init(void);

#endif
