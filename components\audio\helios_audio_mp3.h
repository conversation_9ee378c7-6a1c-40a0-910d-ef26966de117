
#ifndef __HELIOS_AUDIO_MP3_H__
#define __HELIOS_AUDIO_MP3_H__

typedef struct Helios_aud_config_t
{
	int channels;
	int samplerate;
	int len_size;
}He<PERSON><PERSON>_aud_config;

enum {
    PLAY_MOREFILES_ERR_OK = 0,
    PLAY_MOREFILES_ERR_SINGLEFILE = -1,
    PLAY_MOREFILES_ERR_FILENOTEXIST = -2,
	PLAY_MOREFILES_ERR_PLAY = -3
};

int Helios_play_mp3_stream_start(void *mp3_buff, int mp3_size );
int Helios_play_mp3_stream_stop();
int Helios_mp3_file_start(char *file_name, cb_on_player aud_cb);
int Helios_mp3_file_stop();

int helios_mp3_set_callback(cb_on_player aud_cb);
int helios_audio_mp3_play_morefiles(char *files, cb_on_player cb);
void Helios_mp3_file_jump_frame(uint32_t cnt);

#endif
