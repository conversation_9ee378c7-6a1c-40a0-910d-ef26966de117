
#ifndef _NEWCHECK_H_
#define _NEWCHECK_H_

#ifdef __cplusplus

/** C++ namespace for library interfaces */
namespace quec_decoder {
    extern "C" {
#endif

#define QVGA 76800
// #define IMG_H 240
// #define IMG_W 320
#define BLOCK_SIZE 5
// #define BLOCK_H (IMG_H/BLOCK_SIZE)
// #define BLOCK_W (IMG_W/BLOCK_SIZE)
#define BLOCK_COUNT 64
#define AREA_PERCENT_MIN 50
#define AREA_PERCENT_MAX 100

#define NORMAL_POINT 0x00
#define LEFT_POINT 0x01
#define BOTTOM_POINT 0x02
#define RIGHT_POINT 0x04
#define TOP_POINT 0x08
#define LEFT_TOP_POINT 0x09
#define LEFT_BOTTOM_POINT 0x03
#define RIGHT_TOP_POINT 0x0C
#define RIGHT_BOTTOM_POINT 0x06

#define LEFT_TOP 0x01
#define LEFT_BOTTOM 0x02
#define RIGHT_TOP 0x04
#define RIGHT_BOTTOM 0x08
#define LEFT 0x03
#define BOTTOM 0x0A
#define RIGHT 0x0C
#define TOP 0x05

#define CONVEXPOINT(x) (x>0)?(x+1):(x-1)
#define PI 3.1416159265
#define UP_PORT 1
#define DOWN_PORT 0
#define NOT_VERTICAL 1
#define VERTICAL 0

#define NO_CODE 0
#define BAR_CODE 1
#define QR_CODE 2

#define HORIZONTAL 1
#define NORMAL 2

typedef struct pt
{
	int x;
	int y;
}Pt;

typedef struct ptnode
{
	struct ptnode *next;
	struct ptnode *prev;
	Pt pt;
}PtNode;

typedef struct conblockdata
{
	int block_count;
	PtNode *head;
	PtNode *end;
	int total;
	Pt min_y_x;
	Pt max_y_x;
}ConBlockData;

typedef struct areadata
{
	Pt pt[4];
	int block_count;
	int area_percent;
	int wide;
	int length;
	double degree;
}AreaData;

typedef struct _imgdata
{
	int wide;
	int length;
	unsigned char *data;
	unsigned char code_check;
	double degree;
	Pt pt[4];
}ImgData;

typedef struct _SNode
{
	unsigned char con_flag;
	unsigned char x;
	unsigned char y;
	unsigned char tag;
}SNode;

typedef struct _Stack
{
	SNode *a;
	SNode *cur;
	int top;
}Stack;

AreaData ImgCheckCode(unsigned char *indata, int width, int height, int scan_size, int waveform_check);
int SingleBlockCheckWave(unsigned char *indata, int scan_size, int waveform_check);
void neighbourScaling(unsigned char *indata, unsigned char *outdata, int imgWidth, int width, int height, int min_pt_y, int min_pt_x, int uiZoomInWidth, int uiZoomInHigh);
void getScalingVal(int count, int *uizoominwidth, int *uizoominhigh, int n);
void getConvexPoint(ConBlockData *data);
void pushPt(ConBlockData *data, Pt inPt);
void releasePt(ConBlockData *data);
ImgData getDecoderImg(unsigned char *raw, int rawWidth, AreaData areadata);
Pt getPoint(Pt pt1, Pt pt2, Pt pt3);
void singlePixBuildBarCode_degree(unsigned char *raw, unsigned char *outdata, int rawWidth, int width, int height, Pt startPt, int sin, int cos, int direction);
void ql_gety_convert(unsigned char *inBuf, unsigned char *outBuf, int width, int height, int out_width, int out_height, int value);

#ifdef __cplusplus
    }
}
#endif
#endif /* NEWCHECK_H */