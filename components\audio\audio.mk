NAME := audio

include config/$(KCONFIG_CONFIG)

ifeq ($(CONFIG_AUDIO), y)
$(NAME)_SRCS += \
	helios_audio_py.c \
	ring_buffer.c \
	watermark.c \
	helios_audio_montage.c \
	
GLOBAL_INCS += \
	./	
	
endif

result1 := $(sort $(filter y,$(CONFIG_AUDIO) $(CONFIG_AUD_RECORD)))
ifeq (y,$(result1))
$(NAME)_SRCS += \
	helios_audio_fs.c
endif


ifeq ($(CONFIG_AUDIO_G711_COMPRESS), y)
$(NAME)_SRCS += \
	G711/g711.c
endif

ifeq ($(CONFIG_AUDIO_G722_COMPRESS), y)
$(NAME)_SRCS += \
	G722/g722_decode.c \
	G722/g722_encode.c
endif

ifeq ($(CONFIG_AUDIO_G729_COMPRESS), y)
$(NAME)_SRCS += \
	G729/src/adaptativeCodebookSearch.c \
	G729/src/codebooks.c \
	G729/src/computeAdaptativeCodebookGain.c \
	G729/src/computeLP.c \
	G729/src/computeWeightedSpeech.c \
	G729/src/decodeAdaptativeCodeVector.c \
	G729/src/decodeFixedCodeVector.c \
	G729/src/decodeGains.c \
	G729/src/decodeLSP.c \
	G729/src/decoder.c \
	G729/src/encoder.c \
	G729/src/findOpenLoopPitchDelay.c \
	G729/src/fixedCodebookSearch.c \
	G729/src/gainQuantization.c \
	G729/src/interpolateqLSP.c \
	G729/src/LP2LSPConversion.c \
	G729/src/LPSynthesisFilter.c \
	G729/src/LSPQuantization.c \
	G729/src/postFilter.c \
	G729/src/postProcessing.c \
	G729/src/preProcessing.c \
	G729/src/qLSP2LP.c \
	G729/src/utils.c \
	G729/src/cng.c \
	G729/src/dtx.c \
	G729/src/vad.c
endif

ifeq ($(CONFIG_AUDIO_WAV), y)
$(NAME)_SRCS += \
	helios_audio_wav.c
endif

ifeq ($(CONFIG_AUDIO_AMR), y)
$(NAME)_SRCS += \
	helios_audio_amr.c \


$(NAME)_SRCS += \
	amr/amrnb/dec/src/a_refl.cpp		\
	amr/amrnb/dec/src/agc.cpp		\
	amr/amrnb/dec/src/amrdecode.cpp		\
	amr/amrnb/dec/src/b_cn_cod.cpp		\
	amr/amrnb/dec/src/bgnscd.cpp		\
	amr/amrnb/dec/src/c_g_aver.cpp		\
	amr/amrnb/dec/src/d_gain_c.cpp		\
	amr/amrnb/dec/src/d_gain_p.cpp		\
	amr/amrnb/dec/src/d_plsf.cpp		\
	amr/amrnb/dec/src/d_plsf_3.cpp		\
	amr/amrnb/dec/src/d_plsf_5.cpp		\
	amr/amrnb/dec/src/d1035pf.cpp		\
	amr/amrnb/dec/src/d2_11pf.cpp		\
	amr/amrnb/dec/src/d2_9pf.cpp		\
	amr/amrnb/dec/src/d3_14pf.cpp		\
	amr/amrnb/dec/src/d4_17pf.cpp		\
	amr/amrnb/dec/src/d8_31pf.cpp		\
	amr/amrnb/dec/src/dec_amr.cpp		\
	amr/amrnb/dec/src/dec_gain.cpp		\
	amr/amrnb/dec/src/dec_input_format_tab.cpp		\
	amr/amrnb/dec/src/dec_lag3.cpp		\
	amr/amrnb/dec/src/dec_lag6.cpp		\
	amr/amrnb/dec/src/dtx_dec.cpp		\
	amr/amrnb/dec/src/ec_gains.cpp		\
	amr/amrnb/dec/src/ex_ctrl.cpp		\
	amr/amrnb/dec/src/if2_to_ets.cpp		\
	amr/amrnb/dec/src/int_lsf.cpp		\
	amr/amrnb/dec/src/lsp_avg.cpp		\
	amr/amrnb/dec/src/ph_disp.cpp		\
	amr/amrnb/dec/src/post_pro.cpp		\
	amr/amrnb/dec/src/preemph.cpp		\
	amr/amrnb/dec/src/pstfilt.cpp		\
	amr/amrnb/dec/src/qgain475_tab.cpp		\
	amr/amrnb/dec/src/sp_dec.cpp		\
	amr/amrnb/dec/src/wmf_to_ets.cpp	

GLOBAL_INCS += \
	amr/include     	\
	amr/amrnb/common/include 	\
	amr/amrnb/dec/api 	\
	amr/amrnb/dec/src 	\
	amr/amrwb/include 	\
	amr/amrwb/api 		\
	amr/amrwb/src 		\
	amr/amrwbenc/api

endif


ifeq ($(CONFIG_AUDIO_MP3), y)

$(NAME)_SRCS +=    \
			helios_audio_mp3.c \
		   mp3/mp3_dec/src/pvmp3_alias_reduction.cpp \
		   mp3/mp3_dec/src/pvmp3_crc.cpp \
		   mp3/mp3_dec/src/pvmp3_dct_6.cpp \
		   mp3/mp3_dec/src/pvmp3_dct_9.cpp \
		   mp3/mp3_dec/src/pvmp3_dct_16.cpp \
		   mp3/mp3_dec/src/pvmp3_decode_header.cpp \
		   mp3/mp3_dec/src/pvmp3_decode_huff_cw.cpp \
		   mp3/mp3_dec/src/pvmp3_dequantize_sample.cpp \
		   mp3/mp3_dec/src/pvmp3_equalizer.cpp \
		   mp3/mp3_dec/src/pvmp3_framedecoder.cpp \
		   mp3/mp3_dec/src/pvmp3_get_main_data_size.cpp \
		   mp3/mp3_dec/src/pvmp3_get_scale_factors.cpp \
		   mp3/mp3_dec/src/pvmp3_get_side_info.cpp \
		   mp3/mp3_dec/src/pvmp3_getbits.cpp \
		   mp3/mp3_dec/src/pvmp3_huffman_decoding.cpp \
		   mp3/mp3_dec/src/pvmp3_huffman_parsing.cpp \
		   mp3/mp3_dec/src/pvmp3_imdct_synth.cpp \
		   mp3/mp3_dec/src/pvmp3_mdct_6.cpp \
		   mp3/mp3_dec/src/pvmp3_mdct_18.cpp \
		   mp3/mp3_dec/src/pvmp3_mpeg2_get_scale_data.cpp \
		   mp3/mp3_dec/src/pvmp3_mpeg2_get_scale_factors.cpp \
		   mp3/mp3_dec/src/pvmp3_mpeg2_stereo_proc.cpp \
		   mp3/mp3_dec/src/pvmp3_normalize.cpp \
		   mp3/mp3_dec/src/pvmp3_poly_phase_synthesis.cpp \
		   mp3/mp3_dec/src/pvmp3_polyphase_filter_window.cpp \
		   mp3/mp3_dec/src/pvmp3_reorder.cpp \
		   mp3/mp3_dec/src/pvmp3_seek_synch.cpp \
		   mp3/mp3_dec/src/pvmp3_stereo_proc.cpp \
		   mp3/mp3_dec/src/pvmp3_tables.cpp


$(NAME)_SRCS +=    \
		   mp3/mp3_dec/src/api/mp3dec_api.cpp \
		   mp3/mp3_dec/src/api/mp3reader.cpp	


GLOBAL_INCS += \
	mp3/mp3_dec/include     	\
	mp3/mp3_dec/api \
	mp3/mp3_dec

endif

ifeq ($(CONFIG_AUDIO_WAV), y)
$(NAME)_SRCS +=    \
			helios_audio_snd.c
endif








ifeq ($(CONFIG_AUDIO_G711_COMPRESS), y)
GLOBAL_INCS += \
	G711
endif

ifeq ($(CONFIG_AUDIO_G722_COMPRESS), y)
GLOBAL_INCS += \
	G722
endif

ifeq ($(CONFIG_AUDIO_G729_COMPRESS), y)
GLOBAL_INCS += \
	G729 \
	G729/src \
	G729/include
endif
	
$(NAME)_CFLAGS = \
	-Wno-error=unused-function \
	-Wno-error=unused-but-set-variable \
	-Wno-error=unused-parameter \
	-Wno-error=incompatible-pointer-types \
	-Wno-error=sign-compare \
	-Wno-error=unused-variable \
	-Wno-error=unused-label \
	-Wno-error=memset-elt-size

$(NAME)_COMPONENTS = peripheral



ifeq ($(CONFIG_AUD_RECORD), y)
$(NAME)_SRCS += \
	helios_record.c \
	
GLOBAL_INCS += \
	./	\
	amr/amrnb/common/include 	\

	
endif

result2 := $(sort $(filter y,$(CONFIG_AUDIO_AMR) $(CONFIG_AUDIO_RECORD_AMRNB)))
ifeq (y,$(result2))
$(NAME)_SRCS += \
	amr/amrnb/common/src/add.cpp		\
	amr/amrnb/common/src/az_lsp.cpp		\
	amr/amrnb/common/src/bitno_tab.cpp		\
	amr/amrnb/common/src/bitreorder_tab.cpp		\
	amr/amrnb/common/src/bits2prm.cpp		\
	amr/amrnb/common/src/c2_9pf_tab.cpp		\
	amr/amrnb/common/src/copy.cpp		\
	amr/amrnb/common/src/div_32.cpp		\
	amr/amrnb/common/src/div_s.cpp		\
	amr/amrnb/common/src/extract_h.cpp		\
	amr/amrnb/common/src/extract_l.cpp		\
	amr/amrnb/common/src/gains_tbl.cpp		\
	amr/amrnb/common/src/gc_pred.cpp		\
	amr/amrnb/common/src/gmed_n.cpp		\
	amr/amrnb/common/src/gray_tbl.cpp		\
	amr/amrnb/common/src/grid_tbl.cpp		\
	amr/amrnb/common/src/int_lpc.cpp		\
	amr/amrnb/common/src/inv_sqrt.cpp		\
	amr/amrnb/common/src/inv_sqrt_tbl.cpp		\
	amr/amrnb/common/src/l_abs.cpp		\
	amr/amrnb/common/src/l_deposit_h.cpp		\
	amr/amrnb/common/src/l_deposit_l.cpp		\
	amr/amrnb/common/src/l_shr_r.cpp		\
	amr/amrnb/common/src/log2.cpp		\
	amr/amrnb/common/src/log2_norm.cpp		\
	amr/amrnb/common/src/log2_tbl.cpp		\
	amr/amrnb/common/src/lsfwt.cpp		\
	amr/amrnb/common/src/lsp.cpp		\
	amr/amrnb/common/src/lsp_az.cpp		\
	amr/amrnb/common/src/lsp_lsf.cpp		\
	amr/amrnb/common/src/lsp_lsf_tbl.cpp		\
	amr/amrnb/common/src/lsp_tab.cpp		\
	amr/amrnb/common/src/mult_r.cpp		\
	amr/amrnb/common/src/negate.cpp		\
	amr/amrnb/common/src/norm_l.cpp		\
	amr/amrnb/common/src/norm_s.cpp		\
	amr/amrnb/common/src/ph_disp_tab.cpp		\
	amr/amrnb/common/src/pow2.cpp		\
	amr/amrnb/common/src/pow2_tbl.cpp		\
	amr/amrnb/common/src/pred_lt.cpp		\
	amr/amrnb/common/src/q_plsf.cpp		\
	amr/amrnb/common/src/q_plsf_3.cpp		\
	amr/amrnb/common/src/q_plsf_3_tbl.cpp		\
	amr/amrnb/common/src/q_plsf_5.cpp		\
	amr/amrnb/common/src/q_plsf_5_tbl.cpp		\
	amr/amrnb/common/src/qua_gain_tbl.cpp		\
	amr/amrnb/common/src/reorder.cpp		\
	amr/amrnb/common/src/residu.cpp		\
	amr/amrnb/common/src/round.cpp		\
	amr/amrnb/common/src/set_zero.cpp		\
	amr/amrnb/common/src/shr.cpp		\
	amr/amrnb/common/src/shr_r.cpp		\
	amr/amrnb/common/src/sqrt_l.cpp		\
	amr/amrnb/common/src/sqrt_l_tbl.cpp		\
	amr/amrnb/common/src/sub.cpp		\
	amr/amrnb/common/src/syn_filt.cpp		\
	amr/amrnb/common/src/vad1.cpp		\
	amr/amrnb/common/src/weight_a.cpp		\
	amr/amrnb/common/src/window_tab.cpp		\
	
endif

result3 := $(sort $(filter y,$(CONFIG_POC_QS_R07) $(CONFIG_AUDIO_RECORD_AMRNB)))
ifeq ($(result3), y)
$(NAME)_SRCS += \
	amr/amrnb/enc/src/amrencode.cpp    \
	amr/amrnb/enc/src/autocorr.cpp    \
	amr/amrnb/enc/src/c1035pf.cpp    \
	amr/amrnb/enc/src/c2_11pf.cpp    \
	amr/amrnb/enc/src/c2_9pf.cpp    \
	amr/amrnb/enc/src/c3_14pf.cpp    \
	amr/amrnb/enc/src/c4_17pf.cpp    \
	amr/amrnb/enc/src/c8_31pf.cpp    \
	amr/amrnb/enc/src/calc_cor.cpp    \
	amr/amrnb/enc/src/calc_en.cpp    \
	amr/amrnb/enc/src/cbsearch.cpp    \
	amr/amrnb/enc/src/cl_ltp.cpp    \
	amr/amrnb/enc/src/cod_amr.cpp    \
	amr/amrnb/enc/src/convolve.cpp    \
	amr/amrnb/enc/src/cor_h.cpp    \
	amr/amrnb/enc/src/cor_h_x.cpp    \
	amr/amrnb/enc/src/cor_h_x2.cpp    \
	amr/amrnb/enc/src/corrwght_tab.cpp    \
	amr/amrnb/enc/src/dtx_enc.cpp    \
	amr/amrnb/enc/src/enc_lag3.cpp    \
	amr/amrnb/enc/src/enc_lag6.cpp    \
	amr/amrnb/enc/src/enc_output_format_tab.cpp    \
	amr/amrnb/enc/src/ets_to_if2.cpp    \
	amr/amrnb/enc/src/ets_to_wmf.cpp    \
	amr/amrnb/enc/src/g_adapt.cpp    \
	amr/amrnb/enc/src/g_code.cpp    \
	amr/amrnb/enc/src/g_pitch.cpp    \
	amr/amrnb/enc/src/gain_q.cpp    \
	amr/amrnb/enc/src/hp_max.cpp    \
	amr/amrnb/enc/src/inter_36.cpp    \
	amr/amrnb/enc/src/inter_36_tab.cpp    \
	amr/amrnb/enc/src/l_comp.cpp    \
	amr/amrnb/enc/src/l_extract.cpp    \
	amr/amrnb/enc/src/l_negate.cpp    \
	amr/amrnb/enc/src/lag_wind.cpp    \
	amr/amrnb/enc/src/lag_wind_tab.cpp    \
	amr/amrnb/enc/src/levinson.cpp    \
	amr/amrnb/enc/src/lpc.cpp    \
	amr/amrnb/enc/src/ol_ltp.cpp    \
	amr/amrnb/enc/src/p_ol_wgh.cpp    \
	amr/amrnb/enc/src/pitch_fr.cpp    \
	amr/amrnb/enc/src/pitch_ol.cpp    \
	amr/amrnb/enc/src/pre_big.cpp    \
	amr/amrnb/enc/src/pre_proc.cpp    \
	amr/amrnb/enc/src/prm2bits.cpp    \
	amr/amrnb/enc/src/q_gain_c.cpp    \
	amr/amrnb/enc/src/q_gain_p.cpp    \
	amr/amrnb/enc/src/qgain475.cpp    \
	amr/amrnb/enc/src/qgain795.cpp    \
	amr/amrnb/enc/src/qua_gain.cpp    \
	amr/amrnb/enc/src/s10_8pf.cpp    \
	amr/amrnb/enc/src/set_sign.cpp    \
	amr/amrnb/enc/src/sid_sync.cpp    \
	amr/amrnb/enc/src/sp_enc.cpp    \
	amr/amrnb/enc/src/spreproc.cpp    \
	amr/amrnb/enc/src/spstproc.cpp    \
	amr/amrnb/enc/src/ton_stab.cpp    \
	amr/amrnb/enc/api/amrnb_enc_api.cpp \

GLOBAL_INCS += \
	./	\
	amr/amrnb/common/include 	\
	amr/amrnb/enc/api 	\
	amr/amrnb/enc/src 	\
	amr/amrwbenc/api


endif

ifeq ($(CONFIG_AUDIO_RECORD_MP3), y)

$(NAME)_SRCS += \
	mp3_shine/bitstream.c \
	mp3_shine/huffman.c \
	mp3_shine/l3bitstream.c \
	mp3_shine/l3loop.c \
	mp3_shine/l3mdct.c \
	mp3_shine/l3subband.c \
	mp3_shine/layer3.c \
	mp3_shine/reservoir.c \
	mp3_shine/tables.c \
	
GLOBAL_INCS += \
	./	\
	mp3_shine

endif
