
#ifndef _SYMBOL_H_
#define _SYMBOL_H_

#include <stdlib.h>
#include <quec_decoder.h>
#include "refcnt.h"

typedef struct point_s {
    int x, y;
} point_t;

struct quec_decoder_symbol_set_s {
    refcnt_t refcnt;
    int nsyms;                  /* number of filtered symbols */
    quec_decoder_symbol_t *head;        /* first of decoded symbol results */
    quec_decoder_symbol_t *tail;        /* last of unfiltered symbol results */
};

struct quec_decoder_symbol_s {
    quec_decoder_symbol_type_t type;    /* symbol type */
    unsigned int data_alloc;    /* allocation size of data */
    unsigned int datalen;       /* length of binary symbol data */
    char *data;                 /* symbol data */

    unsigned pts_alloc;         /* allocation size of pts */
    unsigned npts;              /* number of points in location polygon */
    point_t *pts;               /* list of points in location polygon */

    refcnt_t refcnt;            /* reference count */
    quec_decoder_symbol_t *next;        /* linked list of results (or siblings) */
    quec_decoder_symbol_set_t *syms;    /* components of composite result */
    unsigned long time;         /* relative symbol capture time */
    int cache_count;            /* cache state */
    int quality;                /* relative symbol reliability metric */
};

extern void _quec_decoder_symbol_free(quec_decoder_symbol_t*);

extern quec_decoder_symbol_set_t *_quec_decoder_symbol_set_create(void);
extern void _quec_decoder_symbol_set_free(quec_decoder_symbol_set_t*);

static inline void sym_add_point (quec_decoder_symbol_t *sym,
                                  int x,
                                  int y)
{
    int i = sym->npts;
    if(++sym->npts >= sym->pts_alloc)
        sym->pts = realloc(sym->pts, ++sym->pts_alloc * sizeof(point_t));
    sym->pts[i].x = x;
    sym->pts[i].y = y;
}

static inline void _quec_decoder_symbol_refcnt (quec_decoder_symbol_t *sym,
                                        int delta)
{
    if(!_quec_decoder_refcnt(&sym->refcnt, delta) && delta <= 0)
        _quec_decoder_symbol_free(sym);
}

static inline void _quec_decoder_symbol_set_add (quec_decoder_symbol_set_t *syms,
                                         quec_decoder_symbol_t *sym)
{
    sym->next = syms->head;
    syms->head = sym;
    syms->nsyms++;

    _quec_decoder_symbol_refcnt(sym, 1);
}

#endif
