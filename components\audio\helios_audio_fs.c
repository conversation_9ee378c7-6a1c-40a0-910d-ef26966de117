#include <stdio.h>
#include <stdlib.h>
#include "helios_audio_fs.h"
#include "helios_include.h"



audio_fs_drv_t audio_fs = {0};

enum {
	AUDIO_FS_IS_INIT,
	AUDIO_FS_NOT_INIT,
};

#define AUDIO_FS_REG_CHECK(x) do {						\
	if(audio_fs.is_used)								\
	{													\
		AUDLOGE("Audio file system not prepared\n");	\
		return x;										\
	}													\
}while(0);


void Helios_Aud_fs_drv_register(audio_fs_drv_t *drv_p)
{
	if(drv_p == NULL)
	{
		AUDLOGE("fs drv para error\n");
		return;
	}

	memcpy(&audio_fs, drv_p, sizeof(audio_fs_drv_t));
	audio_fs.is_used = AUDIO_FS_IS_INIT;

	return;
}

void Helios_Aud_fs_drv_unregister()
{
	memset(&audio_fs, 0, sizeof(audio_fs_drv_t));
	audio_fs.is_used = AUDIO_FS_NOT_INIT;

	return;
}



void * Helios_Aud_fopen (const char * path, const char* mode)
{
	AUDIO_FS_REG_CHECK(NULL);
	if(NULL == path || NULL == mode || NULL == audio_fs.open_cb)
	{
		AUDLOGE("fopen: Parameter verification failed\n");
		return NULL;
	}
	return audio_fs.open_cb(path, mode);
}

audio_fs_res_t Helios_Aud_fclose (void * file_p)
{
	AUDIO_FS_REG_CHECK(-1);
	if(NULL == file_p || NULL == audio_fs.close_cb)
	{
		AUDLOGE("fclose: Parameter verification failed\n");
		return -1;
	}
	return audio_fs.close_cb(file_p);
}


audio_fs_res_t Helios_Aud_fread (void *buffer, size_t size, size_t num, void *file)
{
	AUDIO_FS_REG_CHECK(-1);
	if(NULL == buffer || 0 == size || 0 == num || NULL == file || NULL == audio_fs.read_cb)
	{
		AUDLOGE("fread: Parameter verification failed\n");
		return -1;
	}
	return audio_fs.read_cb(buffer, size, num, file);
}


audio_fs_res_t Helios_Aud_fwrite (void *buffer, size_t size, size_t num, void *file)
{
	AUDIO_FS_REG_CHECK(-1);
	if(NULL == buffer || 0 == size || 0 == num || NULL == file || NULL == audio_fs.write_cb)
	{
		AUDLOGE("fwrite: Parameter verification failed\n");
		return -1;
	}
	return audio_fs.write_cb(buffer, size, num, file);
}

audio_fs_res_t Helios_Aud_fseek (void *file, long offset, int origin)
{
	AUDIO_FS_REG_CHECK(-1);
	if(NULL == file || NULL == audio_fs.write_cb)
	{
		AUDLOGE("fseek: Parameter verification failed\n");
		return -1;
	}
	return audio_fs.seek_cb(file, offset, origin);
}

audio_fs_res_t Helios_Aud_ftell (void *file)
{
	AUDIO_FS_REG_CHECK(-1);
	if(NULL == file || NULL == audio_fs.tell_cb)
	{
		AUDLOGE("ftell: Parameter verification failed\n");
		return -1;
	}
	return audio_fs.tell_cb(file);
}





