/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2020 ASR Ltd. All Rights Reserved
-------------------------------------------------------------------------------------------------------------------*/

#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include "gsmamr_enc.h"
#include "amrnb_enc_api.h"
#include "helios_audio_fs.h"
#include <assert.h>
extern "C" {

    typedef struct AmrNbEncState {
        void *encCtx;
        void *pidSyncCtx;
    }AmrNbEncState;

    typedef struct amrnb_enc_state {
        HeliosAudFILE* fSrc;
        HeliosAudFILE* fDst;
        uint16_t* inputBuf;
        uint8_t* outputBuf;
        AmrNbEncState* amr;
        int bytesGenerated;
        int mode;
        int out_format;
    }amrnb_enc_state;

    enum {
        kInputSize = 320, // 160 samples * 16-bit per sample.
        kOutputSize = 1024
    };

    int amrnb_encode_open(const amrnb_enc_config* config, amrnb_enc_handle* handle) {
        if (config && handle) {
            amrnb_enc_state* st = (amrnb_enc_state*)malloc(sizeof(amrnb_enc_state));
            if (!st)
                return -1;

            memset(st, 0, sizeof(amrnb_enc_state));
            st->mode = config->mode;
            st->out_format = config->output_format;
            // Open input file.
            if (config->name) {
                st->fSrc = Helios_Aud_fopen(config->name, "rb");
                if (st->fSrc == NULL) {
                    return -1;
                }
            }

            // Open output file.
            if (config->out_name) {
                st->fDst = Helios_Aud_fopen(config->out_name, "wb");
                if (st->fDst == NULL) {
                    return -2;
                }
            }

            // Allocate input buffer.
            st->inputBuf = (uint16_t*)malloc(kInputSize);
            assert(st->inputBuf != NULL);

            // Allocate output buffer.
            st->outputBuf = (uint8_t*)malloc(kOutputSize);
            assert(st->outputBuf != NULL);

            // Initialize encoder.
            st->amr = (AmrNbEncState*)malloc(sizeof(AmrNbEncState));
            AMREncodeInit(&st->amr->encCtx, &st->amr->pidSyncCtx, config->dtx_mode);

            // Write file header.
            if (st->out_format == AMR_TX_WMF && st->fDst)
                Helios_Aud_fwrite((void*)"#!AMR\n", 1, 6, st->fDst);
            *handle = (amrnb_enc_handle)st;
            return 0;
        }

        return -1;
    }

    int amrnb_encode_set_rate(int32_t mode, amrnb_enc_handle handle) {
        amrnb_enc_state* st = (amrnb_enc_state*)handle;
        if (st) {
            if (mode >= MR475 && mode < N_MODES)
                st->mode = mode;
            else
                mode = MR475;
            return 0;
        }
        return -1;
    }

    int amrnb_encode_read(amrnb_enc_handle handle) {
        amrnb_enc_state* st = (amrnb_enc_state*)handle;
        if (st) {
            // Read next input frame.
            int bytesRead;
            bytesRead = Helios_Aud_fread(st->inputBuf, 1, kInputSize, st->fSrc);
            if (bytesRead != kInputSize) {
                return -2;
            }
            return 0;
        }
        return -1;
    }

    int amrnb_encode_write(amrnb_enc_handle handle) {
        amrnb_enc_state* st = (amrnb_enc_state*)handle;
        if (st && st->fDst) {
            if (st->bytesGenerated <= 0)
                return -2;

            // Write the output.
            Helios_Aud_fwrite(st->outputBuf, 1, st->bytesGenerated, st->fDst);
            return 0;
        }
        return -1;
    }

    int amrnb_encode_do(amrnb_enc_handle handle) {
        amrnb_enc_state* st = (amrnb_enc_state*)handle;
        if (st) {
            // Encode the frame.
            Frame_Type_3GPP frame_type = (Frame_Type_3GPP)st->mode;
            int bytesGenerated;
            bytesGenerated = AMREncode(st->amr->encCtx, st->amr->pidSyncCtx, (Mode)st->mode,
                (Word16*)st->inputBuf, st->outputBuf, &frame_type,
                st->out_format);

            // Convert from WMF to RFC 3267 format.
            if (bytesGenerated > 0) {
                st->outputBuf[0] = ((st->outputBuf[0] << 3) | 4) & 0x7c;
                st->bytesGenerated = bytesGenerated;
                return 0;
            }
        }

        return -1;
    }

    int amrnb_encode_loop(amrnb_enc_handle handle) {
        int err_code = amrnb_encode_read(handle);
        if (err_code != 0)
            return -1;

        err_code = amrnb_encode_do(handle);
        if (err_code != 0)
            return -2;

        err_code = amrnb_encode_write(handle);
        if (err_code != 0)
            return -3;

        return 0;
    }

    int amrnb_encode_set(amrnb_enc_handle handle, const int16_t* input_data, uint32_t size) {
        if (handle && input_data) {
            if (size != kInputSize)
                return -2;

            amrnb_enc_state* st = (amrnb_enc_state*)handle;
            memcpy(st->inputBuf, input_data, kInputSize);
            return 0;
        }

        return -1;
    }

    int amrnb_encode_get(amrnb_enc_handle handle, uint8_t* output_data, uint32_t* size) {
        if (handle && output_data && size) {
            amrnb_enc_state* st = (amrnb_enc_state*)handle;
            *size = st->bytesGenerated;
            memcpy(output_data, st->outputBuf, st->bytesGenerated);
            return 0;
        }

        return -1;
    }

    int amrnb_encode_close(amrnb_enc_handle handle) {
        amrnb_enc_state* st = (amrnb_enc_state*)handle;
        if (st) {
            // Free the encoder instance.
            if (st->amr) {
                AMREncodeExit(&st->amr->encCtx, &st->amr->pidSyncCtx);
                free(st->amr);
            }

            // Free input and output buffer.
            if (st->inputBuf)
                free(st->inputBuf);
            if (st->outputBuf)
                free(st->outputBuf);

            // Close the input and output files.
            if (st->fSrc) {
                Helios_Aud_fclose(st->fSrc);
            }
            if (st->fDst) {
                Helios_Aud_fclose(st->fDst);
            }

            free(st);
            return 0;
        }
        return -1;
    }
}

#if ENCODER_TEST_AMR_NB == 1
int main(int argc, char *argv[]) {
    amrnb_enc_handle handle = 0;
    int err_code = -1;
    if (argc > 2) {
        amrnb_enc_config config = { 0 };
        config.name = argv[1];
        config.out_name = argv[2];
        config.mode = MR475;
        config.output_format = AMR_TX_WMF;
        if (argc > 3) {
            config.mode = atoi(argv[3]);
        }
        if (argc > 4) {
            config.output_format = atoi(argv[4]);
        }
        if (argc > 5) {
            config.dtx_mode = atoi(argv[5]);
        }
        err_code = amrnb_encode_open(&config, &handle);
    }

    if (err_code != 0) {
        goto safe_exit;
    }

    while (1) {
        err_code = amrnb_encode_loop(handle);
        if (err_code != 0)
            goto safe_exit;
    }

safe_exit:
    amrnb_encode_close(handle);
    return 0;
}
#endif
