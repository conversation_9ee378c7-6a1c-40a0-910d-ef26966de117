/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2020 ASR Ltd. All Rights Reserved
-------------------------------------------------------------------------------------------------------------------*/

#pragma once
#ifdef __cplusplus
extern "C" {
#endif
#include <stdint.h>

    /**
    * @file amrwb_dec_api.h
    * @brief ASR wb-amr decoder related API describes the process and functions used to decode wb-amr from file|memory to file|memory on ASR RTOS platform.
    */

    typedef struct amrwb_dec_config{
        /** input wb-amr file name, specify it when reading from file is needed */
        const char* name;
        /** output pcm file name, specify it when writing to file is needed */
        const char* out_name;
    }amrwb_dec_config;

    /** wb-amr decoder handle, held and used by wb-amr decoder user */
    typedef uint32_t amrwb_dec_handle;

    /** Start wb-amr decoder with configuration.
    * @param [in] config <tt>const amrwb_dec_config*</tt>: wb-amr decoder configuration
    * @param [in,out] handle <tt>amrwb_dec_handle*</tt>: wb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrwb_decode_open(const amrwb_dec_config* config, amrwb_dec_handle* handle);

    /** Read a frame from wb-amr file into memory.
    * @param [in] handle <tt>amrwb_dec_handle</tt>: wb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrwb_decode_read(amrwb_dec_handle handle);

    /** Write a frame from memory to pcm file.
    * @param [in] handle <tt>amrwb_dec_handle</tt>: wb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrwb_decode_write(amrwb_dec_handle handle);

    /** Read a frame from wb-amr file, decode to pcm and write to pcm file if possible.
    * @param [in] handle <tt>amrwb_dec_handle</tt>: wb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrwb_decode_loop(amrwb_dec_handle handle);

    /** Decode a wb-amr frame in memory to pcm frame in memory.
    * @param [in] handle <tt>amrwb_dec_handle</tt>: wb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrwb_decode_do(amrwb_dec_handle handle);

    /** Set a wb-amr frame in memory.
    * @param [in] handle <tt>amrwb_dec_handle</tt>: wb-amr decoder handle
    * @param [in] data <tt>const uint8_t*</tt>: wb-amr buffer address
    * @param [in] size <tt>uint32_t</tt>: wb-amr buffer size
    * @param [out] used_size <tt>uint32_t*</tt>: wb-amr frame size
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrwb_decode_set(amrwb_dec_handle handle, const uint8_t* data, uint32_t size, uint32_t* used_size);

    /** Get a decoded pcm frame in memory.
    * @param [in] handle <tt>amrwb_dec_handle</tt>: wb-amr decoder handle
    * @param [out] data <tt>int16_t*</tt>: pcm frame address
    * @param [out] size <tt>uint32_t*</tt>: pcm frame size
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrwb_decode_get(amrwb_dec_handle handle, int16_t* output_data, uint32_t* size);

    /** Get a wb-amr frame in memory.
     * @param [in] handle <tt>amrwb_dec_handle</tt>: wb-amr decoder handle
     * @param [out] data <tt>uint8_t*</tt>: wb-amr frame address
     * @param [out] size <tt>uint32_t*</tt>: wb-amr frame size
     * @returns error code in <tt>int</tt>, non-zero on failure
     */
    int amrwb_decode_get_amr(amrwb_dec_handle handle, uint8_t* output_data, uint32_t* size);

    /** Get current wb-amr rate.
    * @param [in] handle <tt>amrwb_dec_handle</tt>: wb-amr decoder handle
    * @param [out] rate <tt>int32_t*</tt>: wb-amr rate
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrwb_decode_get_rate(amrwb_dec_handle handle, int32_t* rate);

    /** Free wb-amr decoder resource.
    * @param [in] handle <tt>amrwb_dec_handle</tt>: wb-amr decoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrwb_decode_close(amrwb_dec_handle handle);

#ifdef __cplusplus
}
#endif