/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2020 ASR Ltd. All Rights Reserved
-------------------------------------------------------------------------------------------------------------------*/

#pragma once
#ifdef __cplusplus
extern "C" {
#endif
#include <stdint.h>

    /**
    * @file amrnb_enc_api.h
    * @brief ASR nb-amr encoder related API describes the process and functions used to encode pcm from file|memory to file|memory on ASR RTOS platform.
    */

    typedef struct amrnb_enc_config {
        /** input pcm file name, specify it when reading from file is needed */
        const char* name;
        /** output nb-amr file name, specify it when writing to file is needed */
        const char* out_name;
        /** nb-amr rate, default 0 for AMR 4.75kbps */
        int32_t mode;
        /** nb-amr format, default 0 for AMR_TX_WMF */
        int32_t output_format;
        /** enable dtx or not, 0 for disable*/
        int32_t dtx_mode;
    }amrnb_enc_config;

    /** nb-amr encoder handle, held and used by nb-amr encoder user */
    typedef uint32_t amrnb_enc_handle;

    /** Start nb-amr encoder with configuration.
    * @param [in] config <tt>const amrnb_enc_config*</tt>: nb-amr encoder configuration
    * @param [in,out] handle <tt>amrnb_enc_handle*</tt>: nb-amr encoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_encode_open(const amrnb_enc_config* config, amrnb_enc_handle* handle);

    /** Encode a pcm frame in memory to nb-amr frame in memory.
    * @param [in] handle <tt>amrnb_enc_handle</tt>: nb-amr encoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_encode_do(amrnb_enc_handle handle);

    /** Read a frame from pcm file, encode to nb-amr frame and write to nb-amr file if possible.
    * @param [in] handle <tt>amrnb_enc_handle</tt>: nb-amr encoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_encode_loop(amrnb_enc_handle handle);

    /** Read a frame from pcm file into memory.
    * @param [in] handle <tt>amrnb_enc_handle</tt>: nb-amr encoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_encode_read(amrnb_enc_handle handle);

    /** Write a frame from memory to nb-amr file.
    * @param [in] handle <tt>amrnb_enc_handle</tt>: nb-amr encoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_encode_write(amrnb_enc_handle handle);

    /** Set a pcm frame in memory.
    * @param [in] handle <tt>amrnb_enc_handle</tt>: nb-amr encoder handle
    * @param [in] data <tt>const int16_t*</tt>: pcm frame address
    * @param [in] size <tt>uint32_t</tt>: pcm frame size
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_encode_set(amrnb_enc_handle handle, const int16_t* input_data, uint32_t size);

    /** Get a encoded nb-amr frame in memory.
    * @param [in] handle <tt>amrnb_enc_handle</tt>: nb-amr encoder handle
    * @param [out] data <tt>uint8_t*</tt>: nb-amr frame address
    * @param [out] size <tt>uint32_t*</tt>: nb-amr frame size
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_encode_get(amrnb_enc_handle handle, uint8_t* output_data, uint32_t* size);

    /** Set current nb-amr rate.
    * @param [in] handle <tt>amrnb_enc_handle</tt>: nb-amr encoder handle
    * @param [in] mode <tt>int32_t</tt>: nb-amr rate, 0 for 4.75 kbps
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_encode_set_rate(int32_t mode, amrnb_enc_handle handle);

    /** Free nb-amr encoder resource.
    * @param [in] handle <tt>amrnb_enc_handle</tt>: nb-amr encoder handle
    * @returns error code in <tt>int</tt>, non-zero on failure
    */
    int amrnb_encode_close(amrnb_enc_handle handle);

#ifdef __cplusplus
}
#endif