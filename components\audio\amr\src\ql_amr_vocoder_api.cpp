/*================================================================
  Copyright (c) 2021, Quectel Wireless Solutions Co., Ltd. All rights reserved.
  Quectel Wireless Solutions Proprietary and Confidential.
=================================================================*/

extern "C" {
#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "helios_include.h"

//#include "diags.h"
//#include "gbl_types.h"
//#include "acm_comm.h"
#include "amr_vocoder_api.h"
#include "amr_vocoder_config.h"
#include "amrnb_enc_api.h"
#include "amrnb_dec_api.h"
#include "amrwb_dec_api.h"
//#include "acm_audio_track.h"
//#include "acm_audio_record.h"
//#include "audio_def.h"
//#include "acm_audio_def.h"
//#include "audio_file.h"
//#include "quec_cust_feature.h"
//#include "ql_fs.h"
#include "ring_buffer.h"
//#include "AudioHAL.h"
#include "gsmamr_dec.h"
#include "helios_os.h"

#if 1//def QUECTEL_PROJECT_CUST

    ////DECODER
    // Constants for AMR-NB
    enum {
        kInputBufferSize = 64,
        kSamplesPerFrame = 160,
        kBitsPerSample = 16,
        kOutputBufferSize = kSamplesPerFrame * kBitsPerSample / 8,
        kSampleRate = 8000,
        kChannels = 1,
        kFileHeaderSize = 6
    };

    typedef enum{
        AMR_PLAY_STOP,
        AMR_PLAY_SENDING_MESSAGE,
        AMR_PLAY_PLAYING,
        AMR_PLAY_STOPPING,
    }AMR_PLAY_STATUS;
    
    typedef enum
    {
        HELIOS_AUD_PLAYER_ERROR = -1,
        HELIOS_AUD_PLAYER_START = 0,
        HELIOS_AUD_PLAYER_PAUSE,
        HELIOS_AUD_PLAYER_RESUME,
        HELIOS_AUD_PLAYER_PLAYING,
        HELIOS_AUD_PLAYER_NODATA,
        HELIOS_AUD_PLAYER_LESSDATA,
        HELIOS_AUD_PLAYER_MOREDATA,
        HELIOS_AUD_PLAYER_FINISHED,
    }Helios_EnumAudPlayerState;
    
    #define AMR_DEC_PCM_TASK_CONTINUE   (0x01)
    #define AMR_DEC_PCM_STOP            (0x02)
    #define AMR_DEC_PCM_CLEAR  			(0x04)//august add Probability cannot be played after repairing stop
    #define AMR_DEC_PCM_TASK_MASK       (AMR_DEC_PCM_STOP | AMR_DEC_PCM_TASK_CONTINUE)

    typedef int(*cb_on_player)(char *p_data, int len, Helios_EnumAudPlayerState state);

    static char amr_file_name[256];
    int is_amr_playing = AMR_PLAY_STOP;
    static OSASemaRef amr_Sema;
    static void* amr_stack_ptr = NULL;
    static OSATaskRef amr_Decoder = NULL;

    static OSAFlagRef   amr_dec_pcm_flag_Ref;

    static unsigned char pAmrInputBuf[kInputBufferSize];
    static unsigned char pAmrOutputBuf[kOutputBufferSize];


    static unsigned short amr_dec_pcm_pingpong_buffer[320]; // two frame
    static const uint32_t amrkFrameSizes[] = {12, 13, 15, 17, 19, 20, 26, 31, 7};
    #define DEFAULT_AMR_CACHE_SIZE    (1600)  /* 4 frame*/
    static unsigned short amr_cache_buffer_default[DEFAULT_AMR_CACHE_SIZE]; 
    static unsigned short* amr_cache_buffer = amr_cache_buffer_default;
    static int amrcacheSize = DEFAULT_AMR_CACHE_SIZE;
    static int amrFrameSize = 0;
    static int amrcacheReadItem = 0;
    static int amrcacheWriteItem = 0;
    static int amrcacheItemCnt = 0;
    static int   amr_dec_pingpong_index = 0;
    static AUDIOHAL_STREAM_T amr_dec_pcm_stream;  
    static int amr_file_play_mode = 0;
    ring_buffer_handler_t amr_ring_buffer = NULL;

    int amrdec_set_cache_buffer(void* start, int size)
    {
        if(start){
            amr_cache_buffer = start;
            amrcacheSize = size / 2;        
        }else{
            amr_cache_buffer = amr_cache_buffer_default;
            amrcacheSize = DEFAULT_AMR_CACHE_SIZE;
        }
        return 0;
    }
    static int isCacheFull(void)
    {
        return (amrcacheWriteItem >= (amrcacheReadItem + amrcacheItemCnt));
    }
    
    static int isCacheEmpty(void)
    {
        return (amrcacheWriteItem == amrcacheReadItem);
    }
    
    static int fillCache(void)
    {
        unsigned short* start = NULL;
    
        if(0 == amrFrameSize){
            DIAG_FILTER(AUDIO, MP3_DEC, fillCache_error, DIAG_INFORMATION)
            diagPrintf("mp3FrameSize is 0");        
            return -1;
        }
        if(0 == amrcacheItemCnt){
            amrcacheItemCnt = amrcacheSize / amrFrameSize;
        }
        
        if(isCacheFull()){
            DIAG_FILTER(AUDIO, MP3_DEC, fillCache_full, DIAG_INFORMATION)
            diagPrintf("cacheWriteItem:0x%lx,cacheReadItem:0x%lx, cacheItemCnt:0x%lx", 
            amrcacheWriteItem, amrcacheReadItem, amrcacheItemCnt);      
            return 1;
        }
        
        start = amr_cache_buffer + (amrcacheWriteItem % amrcacheItemCnt) * amrFrameSize;
        memcpy(start, pAmrOutputBuf, amrFrameSize * sizeof(unsigned short));
        amrcacheWriteItem++;
        
        return 0;        
    }
    
    void amr_dec_pcm_half_handler(void)
    {
        char* bufferstart = (char*)amr_dec_pcm_stream.startAddress;
        unsigned short* input = NULL;
        if(amr_dec_pingpong_index % 2){bufferstart += amr_dec_pcm_stream.length / 2;}
    
        if(!isCacheEmpty()){
            input = amr_cache_buffer + (amrcacheReadItem % amrcacheItemCnt) * amrFrameSize;
        }
        
        if(input){
            memcpy(bufferstart, input, amr_dec_pcm_stream.length / 2);
            amrcacheReadItem++;
        };
        amr_dec_pingpong_index++;
        OSAFlagSet(amr_dec_pcm_flag_Ref, AMR_DEC_PCM_TASK_CONTINUE, OSA_FLAG_OR);
        return;
    
    }
    static AUDIOHAL_ERR_T amr_dec_pcm_start(unsigned int rate, unsigned int ch, unsigned int frameByteSize)
    {
        AUDIOHAL_ERR_T rc = AUDIOHAL_ERR_NO;
    
        amr_dec_pingpong_index = 0;
        
        amr_dec_pcm_stream.channelNb = ch;
        amr_dec_pcm_stream.length = frameByteSize * 2;
        
        memset(amr_dec_pcm_pingpong_buffer, 0, amr_dec_pcm_stream.length);
        
        amr_dec_pcm_stream.playSyncWithRecord = 0;
        amr_dec_pcm_stream.voiceQuality = 0;
        amr_dec_pcm_stream.sampleRate = rate;
        amr_dec_pcm_stream.startAddress = (UINT32 *)amr_dec_pcm_pingpong_buffer;
        amr_dec_pcm_stream.halfHandler = amr_dec_pcm_half_handler;
        
        rc = AudioHAL_AifPlayStream(&amr_dec_pcm_stream);
        return rc;
    }
    
    static void amr_dec_pcm_stop(void)
    {
        AudioHAL_AifDrain();
        AudioHAL_AifStopPlay(); 
    }
    
    extern int ql_audio_play_init(cb_on_player aud_cb);
    extern cb_on_player aud_play_cb;
    static int amrnb_dec(void)
    {
        QFILE *         fpInput = NULL;
        char*           name = amr_file_name;
        void*           amrHandle = NULL;
        void*           inputBuf = NULL;
        void*           outputBuf = NULL;
        
        char            header[kFileHeaderSize];
        unsigned int    event = 0;    
        int             bytesRead = 0;
        uint32_t        retVal = 0;
        uint8_t         mode = 0;
        Frame_Type_3GPP frameType ;
        int32_t         frameSize = 0;
        int             rc = 0;
        int             first_time  = 1;
        bool            success     = false;
        int             rec = 0;
        
        if(!amr_file_play_mode){
            fpInput = ql_fopen(name, "rb");
            if (!fpInput) {
                return 1;
            }
            // Validate the input AMR file
            bytesRead = ql_fread(header, 1, kFileHeaderSize, fpInput);
        }else {
            rec = quec_ring_buffer_read_ex(amr_ring_buffer, header, kFileHeaderSize, &bytesRead);
        }
        if (bytesRead != kFileHeaderSize || memcmp(header, "#!AMR\n", kFileHeaderSize)) {
            return 1;
        }
            
        // Create AMR-NB decoder instance
        int err = GSMInitDecode(&amrHandle, (Word8*)"AMRNBDecoder");
        if(err != 0){
            return 1;
        }
        inputBuf = pAmrInputBuf;
        outputBuf = pAmrOutputBuf;
        
        ASSERT(inputBuf != NULL);
        ASSERT(outputBuf != NULL);
        
        memset(pAmrOutputBuf, 0, kOutputBufferSize);
        amrFrameSize = 0;
        amrcacheReadItem = 0;
        amrcacheWriteItem = 0;
        amrcacheItemCnt = 0;
        // Decode loop
        if(aud_play_cb) {
            aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_START);
        }
        while (1) {
            do{
            // Read mode
                if(!amr_file_play_mode){
                    bytesRead = ql_fread(&mode, 1, 1, fpInput);
                }else {
                    quec_ring_buffer_read_ex(amr_ring_buffer, &mode, 1, &bytesRead);
                }   
                if (bytesRead != 1) {           
                    success = false;
                    break;
                } else {
                    success = true;
                }           
                
                // Find frame type
                frameType = (Frame_Type_3GPP)((mode >> 3) & 0x0f);
                if (frameType >= AMR_SID){
                    uart_printf("frameType:%d \n",frameType);
                    retVal = 1;
                    break;
                }
    
                // Find frame type
                frameSize = amrkFrameSizes[frameType];
                if(!amr_file_play_mode){
                    bytesRead = ql_fread(inputBuf, 1, frameSize, fpInput);
                }else{
                    rec = quec_ring_buffer_read_ex(amr_ring_buffer, inputBuf, frameSize, &bytesRead);
                }
                if (bytesRead != frameSize){ 
                    uart_printf("bytesRead:%d, frameSize:%d", bytesRead, frameSize);
                    success = false;
                    break;
                } else {
                    success = true;
                }
                //Decode frame
                int32_t decodeStatus;
                decodeStatus = AMRDecode(amrHandle, frameType, (uint8_t*)inputBuf,
                                         (int16_t*)outputBuf, MIME_IETF);
                if(decodeStatus == -1) {
                    uart_printf("amrnb_dec_decode_error");
                    retVal = 1;
                    break;
                }
                if(0 == amrFrameSize){
                        amrFrameSize = 160;
                }
                fillCache();
            }while(!isCacheFull());
            
            if(first_time){
                first_time = 0;
                rc = amr_dec_pcm_start(8000, 1, 320);
                if(AUDIOHAL_ERR_NO != rc){
                    retVal = EXIT_FAILURE;
                    if(aud_play_cb){
                        aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_ERROR);
                    }
                    break;
                }
            }
            OSAFlagWait(amr_dec_pcm_flag_Ref, AMR_DEC_PCM_TASK_MASK, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND); 
    
            if(!success){
                while((!isCacheEmpty()) 
                        && (AMR_DEC_PCM_STOP != (AMR_DEC_PCM_STOP & event))){
                    OSAFlagWait(amr_dec_pcm_flag_Ref, AMR_DEC_PCM_TASK_MASK, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND); 
                }
            }
    
            if(AMR_DEC_PCM_STOP == (AMR_DEC_PCM_STOP & event) ){
                uart_printf("dec_amr_stop");
                break;
            }
            if (!success){
                break;
            }
            
            
        }
    
    end:    
        if(AUDIOHAL_ERR_NO == rc){
            amr_dec_pcm_stop();
        }   
        // Close decoder instance
        if(amrHandle){
            GSMDecodeFrameExit(&amrHandle);
            amrHandle = NULL;
        }   
        if(fpInput){
            ql_fclose(fpInput);
            fpInput = NULL;
        }
        if(amr_ring_buffer){
            quec_ring_buffer_deinit(&amr_ring_buffer);
            amr_ring_buffer = NULL;
        }
        if(aud_play_cb) {
            aud_play_cb(NULL, 0, HELIOS_AUD_PLAYER_FINISHED);
        }
        return retVal;
    }
    
    static int amrPlay(void)
    {
        OS_STATUS status;
        unsigned char first_entry = 0;
        while(1){
            
            OSAFlagSet(amr_dec_pcm_flag_Ref, AMR_DEC_PCM_CLEAR, OSA_FLAG_AND);
            if(0 != first_entry) {
                is_amr_playing = AMR_PLAY_STOP;
            }
            else {
                first_entry = !first_entry;
            }
            status = OSASemaphoreAcquire(amr_Sema, OSA_SUSPEND);
            ASSERT(status == OS_SUCCESS); 
            if(AMR_PLAY_SENDING_MESSAGE == is_amr_playing) {
                is_amr_playing = AMR_PLAY_PLAYING;
                amrnb_dec();
            }
            is_amr_playing = AMR_PLAY_STOPPING;
        }
        return 0;
    }
    
    static void amrdec_task_init(void)
    {
        static int inited = 0;
        OS_STATUS status;
        size_t amr_stackSize = 1024*8;
        int amr_thread_priority = 75;
        
        if(!inited){
            status = OSAFlagCreate(&amr_dec_pcm_flag_Ref);
            ASSERT(status == OS_SUCCESS);       
            
            amr_stack_ptr = malloc(amr_stackSize);  
            ASSERT(amr_stack_ptr);
            
            status = OSATaskCreate(&amr_Decoder, amr_stack_ptr, amr_stackSize, amr_thread_priority, "amrPlay", amrPlay, NULL);
            ASSERT(status == OS_SUCCESS);       
    
            inited = 1;
        }
    }
    
    static BOOL findFile(char *name)
    {
        if (ql_access(name, 0) == 0) {
            return TRUE;
        } else {
            return FALSE;
        }
    }
    
    int amrStart(const char *file_name) 
    {
        OSA_STATUS status;
        if(!file_name){
            uart_printf("amrStart_nameNULL\n");
            return -1;
        }
        
        if((0 == strlen(file_name))
            || (strlen(file_name) > 250)
            || (!findFile(file_name))) {
            uart_printf("file_name error\n");
            return -1;
        }
    
        if(AMR_PLAY_STOP != is_amr_playing) {
            uart_printf("error, amr is in playing2\n");
            return -2;
        }
        is_amr_playing = AMR_PLAY_SENDING_MESSAGE;
        
        amr_file_play_mode = 0;
        memcpy(amr_file_name, file_name, strlen(file_name) + 1);
        if(amr_Decoder == NULL) {
            status = OSASemaphoreCreate(&amr_Sema, 1, OSA_FIFO); 
            ASSERT(status == OS_SUCCESS); 
            uart_printf("amrdec_task_init\n");
            amrdec_task_init();
        } else {
            status = OSASemaphoreRelease(amr_Sema);
    //      uart_printf("amrdec_task_init error\n");
            ASSERT(status == OS_SUCCESS); 
        }
        return 0;
    }
    
    int amrStart2(const char *data, unsigned int size) 
    {
        OSA_STATUS status;
        
        if(data == NULL){
            return -1;
        }
        
        if(AMR_PLAY_STOP != is_amr_playing) {
            uart_printf("error, amr is in playing2\n");
            return -2;
        }
        is_amr_playing = AMR_PLAY_SENDING_MESSAGE;
        
        quec_ring_buffer_init(&amr_ring_buffer, size);
        amr_file_play_mode = 1;
    
        quec_ring_buffer_write(amr_ring_buffer, data, size);
    
    
        if(amr_Decoder == NULL) {
            status = OSASemaphoreCreate(&amr_Sema, 1, OSA_FIFO); 
            ASSERT(status == OS_SUCCESS); 
            uart_printf("amrdec_task_init\n");
            amrdec_task_init();
        } else {
            status = OSASemaphoreRelease(amr_Sema);
            ASSERT(status == OS_SUCCESS); 
        }
        return 0;
        
    }
    
    
    int amrStop(void)
    {  
        int count = 0;
        if(AMR_PLAY_STOP == is_amr_playing) {
    //      uart_printf("error, amr is not playing");
            return -1;
        }
    
        if(AMR_PLAY_SENDING_MESSAGE == is_amr_playing) {
            is_amr_playing = AMR_PLAY_STOPPING;
        }
        
        OSAFlagSet(amr_dec_pcm_flag_Ref, AMR_DEC_PCM_STOP, OSA_FLAG_OR);
        while ((AMR_PLAY_STOP != is_amr_playing) && count<100) {
            OSATaskSleep(3);
            count++;
        }
        return 0;
    }


    ////ECNCODER
    typedef enum{
        AMR_RECORD_STOP,
        AMR_RECORD_RELEASE_SEMA,
        AMR_RECORD_RECORDING,
        AMR_RECORD_STOPPING
    }AMR_RECORD_STATUS;

    typedef enum
    {
    	AUD_RECORD_ERROR = -1,
    	AUD_RECORD_START = 0,
    	AUD_RECORD_DATA,
    	AUD_RECORD_PAUSE,
    	AUD_RECORD_FINISHED,
    	AUD_RECORD_DISK_FULL,
    }enum_aud_record_state;
    
    #define AMR_ENC_PCM_TASK_CONTINUE   (0x01)
    #define AMR_ENC_PCM_STOP            (0x02)
    #define AMR_ENC_PCM_TASK_MASK       (AMR_ENC_PCM_STOP | AMR_ENC_PCM_TASK_CONTINUE)

    typedef int(*cb_on_record)(char *p_data, int len, enum_aud_record_state state);
    extern int ql_find_file(char *name);
    static int is_amr_recording = AMR_RECORD_STOP;
    static int amr_record_dtx_enable = 0;
    static char amr_record_name[256];
    static OSATaskRef amr_Encoder = NULL;
    static OSAFlagRef   amr_enc_pcm_flag_Ref;
    static void* amr_record_stack_ptr = NULL;
    static OSASemaRef amr_record_Sema;
    
    static int   amr_enc_pingpong_index = 0;
    static AUDIOHAL_STREAM_T amr_enc_pcm_record; 
    static unsigned short amr_enc_pcm_pingpong_buffer[320]; // two frame
    
    ring_buffer_handler_t amr_record_buffer = NULL;
    cb_on_record cb_on_amr_record = NULL;
    static void amr_enc_pcm_half_handler(void)
    {
    	char* bufferstart = (char*)amr_enc_pcm_record.startAddress;
    	unsigned int length = amr_enc_pcm_record.length/2;
    	int ret = 0;
    	if(amr_enc_pingpong_index % 2)
    	{
    		bufferstart += length;
    	}

    	ret = quec_ring_buffer_write(amr_record_buffer, bufferstart, length);
    	amr_enc_pingpong_index++;
    	OSAFlagSet(amr_enc_pcm_flag_Ref, AMR_ENC_PCM_TASK_CONTINUE, OSA_FLAG_OR);
    }

    static AUDIOHAL_ERR_T amr_enc_pcm_start(unsigned int rate, unsigned int ch, unsigned int frameByteSize)
    {
      	AUDIOHAL_ERR_T rc = AUDIOHAL_ERR_NO;

        amr_enc_pingpong_index = 0;
        
        amr_enc_pcm_record.channelNb = ch;
        amr_enc_pcm_record.length = frameByteSize * 2;
        
        memset(amr_enc_pcm_pingpong_buffer, 0, amr_enc_pcm_record.length);
        
        amr_enc_pcm_record.playSyncWithRecord = 0;
        amr_enc_pcm_record.voiceQuality = 0;
        amr_enc_pcm_record.sampleRate = rate;
        amr_enc_pcm_record.startAddress = (UINT32 *)amr_enc_pcm_pingpong_buffer;
        amr_enc_pcm_record.halfHandler = amr_enc_pcm_half_handler;
    	
        rc = AudioHAL_AifRecordStream(&amr_enc_pcm_record);
    	return rc;
    }


    static void amr_enc_pcm_stop(void)
    {
        AudioHAL_AifStopRecord(); 
    }

    static int get_amr_record_ringbuf(void)
    {
    	unsigned int usd_size = 0;
    	if(amr_record_buffer){
    		quec_ring_buffer_get_used_size(amr_record_buffer, &usd_size);
    	}
    	return usd_size;
    }

    //forrest.liu@20211102 add for audio record stream
    static int amrnb_stream_enc(void)
    {
        int				ret = 0;
    	unsigned short	input_data[320] = {0};
    	unsigned char	output_data[320] = {0};
    	amrnb_enc_handle record_handle = 0;
    	amrnb_enc_config config = { 0 };
    	int 			first_time 	= 1;
    	uint32_t        retVal = 0;
    	int  			rc = 0;
    	unsigned int    event = 0; 
    	int             bytesRead = 0;
    	
    	int 			bytesReadCount = 0;	

    	config.dtx_mode = amr_record_dtx_enable;
    	amrnb_encode_open(&config, &record_handle);

        while (1) {
    		while(get_amr_record_ringbuf()){
    			quec_ring_buffer_read_ex(amr_record_buffer, input_data, 320, &bytesRead);
    			if(bytesRead != 320){
    				uart_printf("bytesRead:%d \n", bytesRead);
    				break;
     			}
    			
    			amrnb_encode_set(record_handle, input_data, bytesRead);

    			ret = amrnb_encode_do(record_handle);
    	        if (ret != 0){
    				uart_printf("amrnb encode error \n");
    	        }
    			amrnb_encode_get(record_handle, output_data, &bytesRead);

    			if(cb_on_amr_record) 
    			{
    				cb_on_amr_record(output_data, bytesRead, AUD_RECORD_DATA);
    			}

    			bytesReadCount += bytesRead;
    		}
    		
    		if(first_time){
                first_time = 0;
                rc = amr_enc_pcm_start(8000, 1, 320);
    			if(AUDIOHAL_ERR_NO != rc){
                	retVal = EXIT_FAILURE;
    				break;
    			}
//    			if(cb_on_amr_record) 
//    			{
//    				cb_on_amr_record("#!AMR\n", 6, AUD_RECORD_DATA);//amr header
//    			}
            }

            OSAFlagWait(amr_enc_pcm_flag_Ref, AMR_ENC_PCM_TASK_MASK, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND); 
    		

            if(AMR_ENC_PCM_STOP == (AMR_ENC_PCM_STOP & event) ){
    			uart_printf("enc_amr_stop");
                break;
            }
            
        }

    end:

    	if(cb_on_amr_record) {
    			cb_on_amr_record(output_data, bytesReadCount, AUD_RECORD_FINISHED);
    	}

    	if(AUDIOHAL_ERR_NO == rc){
    		amr_enc_pcm_stop();
    	}	
        // Close decoder instance
    	if(record_handle){
    		amrnb_encode_close(record_handle);
    		record_handle = false;
    	}

    	if(amr_record_buffer){
    		quec_ring_buffer_deinit(&amr_record_buffer);
    		amr_record_buffer = NULL;
    	}

        return retVal;
    }

    static int amrnb_enc(void)
    {
        QFILE *         fpInput = NULL;
        char*           name = amr_record_name;
        int				ret = 0;
    	unsigned short	input_data[320] = {0};
    	unsigned char	output_data[320] = {0};
    	amrnb_enc_handle record_handle = 0;
    	amrnb_enc_config config = { 0 };
    	int 			first_time 	= 1;
    	uint32_t        retVal = 0;
    	int  			rc = 0;
    	unsigned int    event = 0; 
    	int             bytesRead = 0;
    	bool 			success 	= true;
    	
    	int 			bytesReadCount = 0;	
    	
    	if(ql_find_file(name)){
    		uart_printf("%s is exist!!\n",name);
    		return -1;
    	}
    	config.dtx_mode = amr_record_dtx_enable;
    	amrnb_encode_open(&config, &record_handle);
    	fpInput = ql_fopen(name, "wb+");
    	if (!fpInput) {
    			return -2;
    	}
    	ret = ql_fseek(fpInput, 0, SEEK_SET);
    	ret = ql_fwrite("#!AMR\n", 6, 1, fpInput);
    	if(ret < 0){
    		uart_printf("Write amr header failed\n");
    		return -2;
    	}
    	
    	if(cb_on_amr_record) {
    		cb_on_amr_record(amr_record_name, 0, AUD_RECORD_START);
    	}

        while (1) {
    		while(get_amr_record_ringbuf()){
    			quec_ring_buffer_read_ex(amr_record_buffer, input_data, 320, &bytesRead);
    			if(bytesRead != 320){
    				uart_printf("bytesRead:%d \n", bytesRead);
    				break;
     			}
    			
    			amrnb_encode_set(record_handle, input_data, bytesRead);

    			ret = amrnb_encode_do(record_handle);
    	        if (ret != 0){
    				uart_printf("amrnb encode error \n");
    	        }
    			amrnb_encode_get(record_handle, output_data, &bytesRead);

    			ret = ql_fwrite(output_data, bytesRead, 1, fpInput);
    			if(ret < 0){
    				uart_printf("Write amr data failed\n");
    				success = false;
    				break;
    			}
    			bytesReadCount += ret;
    		}
    		
    		if(first_time){
                first_time = 0;
                rc = amr_enc_pcm_start(8000, 1, 320);
    			if(AUDIOHAL_ERR_NO != rc){
                	retVal = EXIT_FAILURE;
    				break;
    			}
            }

            OSAFlagWait(amr_enc_pcm_flag_Ref, AMR_ENC_PCM_TASK_MASK, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND); 
    		

            if(AMR_ENC_PCM_STOP == (AMR_ENC_PCM_STOP & event) ){
    			uart_printf("enc_amr_stop");
                break;
            }
    		if (!success){
    			break;
    		}
            
            
        }

    end:

    	if(cb_on_amr_record) {
    		if(success) {
    			bytesReadCount+=6;		//igni add for amr head
    			cb_on_amr_record(amr_record_name, bytesReadCount, AUD_RECORD_FINISHED);
    		} else {
    			cb_on_amr_record(amr_record_name, 0, AUD_RECORD_ERROR);
    		}
    	}

    	if(AUDIOHAL_ERR_NO == rc){
    		amr_enc_pcm_stop();
    	}	
        // Close decoder instance
    	if(record_handle){
    		amrnb_encode_close(record_handle);
    		record_handle = false;
    	}
        if(fpInput){
    		ql_fclose(fpInput);
    		fpInput = NULL;
    	}
    	if(amr_record_buffer){
    		quec_ring_buffer_deinit(&amr_record_buffer);
    		amr_record_buffer = NULL;
    	}

        return retVal;
    }


    static int amrRecord(void)
    {
        OS_STATUS status;
    	unsigned char first_entry = 0;
        while(1){
            if(0 != first_entry){
    			is_amr_recording = AMR_RECORD_STOP;
    		}
    		else{
    			first_entry = !first_entry;
    		}
            status = OSASemaphoreAcquire(amr_record_Sema, OSA_SUSPEND);
            ASSERT(status == OS_SUCCESS); 

            if(AMR_RECORD_RELEASE_SEMA == is_amr_recording){
    			is_amr_recording = AMR_RECORD_RECORDING;
    			if(0 == strncmp(amr_record_name, "stream", 6))//forrest.liu@20211102 add for audio record stream
    			{
    				amrnb_stream_enc();
    			}
    			else
    			{
            		amrnb_enc();
    			}
    		}
            is_amr_recording = AMR_RECORD_STOPPING;
        }
        return 0;
    }

    static void amrenc_task_init(void)
    {
        static int inited = 0;
        OS_STATUS status;
        size_t amr_stackSize = 1024*16;
        int amr_thread_priority = 75;
        
        if(!inited){
            status = OSAFlagCreate(&amr_enc_pcm_flag_Ref);
            ASSERT(status == OS_SUCCESS);       
            
            amr_record_stack_ptr = malloc(amr_stackSize);  
    		ASSERT(amr_record_stack_ptr);
    		
            status = OSATaskCreate(&amr_Encoder, amr_record_stack_ptr, amr_stackSize, amr_thread_priority, "amrRecord", amrRecord, NULL);
            ASSERT(status == OS_SUCCESS);       

            inited = 1;
        }
    }

    int amrStartrecord(const char *file_name, void* amr_cb) 
    {
    	OSA_STATUS status;
    	if(!file_name){
    		uart_printf("amrStart_nameNULL\n");
    		return -1;
    	}
        
        if((0 == strlen(file_name))
    		|| (strlen(file_name) > 250)){
    		uart_printf("file_name error\n");
            return -1;
        }
        
        if(AMR_RECORD_STOP != is_amr_recording) {
    		uart_printf("error, amr is in recording\n");
            return -2;
        }
    	is_amr_recording = AMR_RECORD_RELEASE_SEMA;
    	
        cb_on_amr_record = (cb_on_record)amr_cb;
    	quec_ring_buffer_init(&amr_record_buffer, 1600);
        memcpy(amr_record_name, file_name, strlen(file_name) + 1);
    	
        if(amr_Encoder == NULL) {
            status = OSASemaphoreCreate(&amr_record_Sema, 1, OSA_FIFO); 
            ASSERT(status == OS_SUCCESS); 
    		uart_printf("amrenc_task_init\n");
            amrenc_task_init();
        } else {
            status = OSASemaphoreRelease(amr_record_Sema);
    //		uart_printf("amrdec_task_init error\n");
            ASSERT(status == OS_SUCCESS); 
        }
        return 0;
    }

    int amrStoprecored(void)
    {  
        if(AMR_RECORD_STOP == is_amr_recording) {
    //		uart_printf("error, amr is not playing");
            return -1;
        }

        if(AMR_RECORD_RELEASE_SEMA == is_amr_recording) {
    		 is_amr_recording  = AMR_RECORD_STOPPING;
        }
        
        OSAFlagSet(amr_enc_pcm_flag_Ref, AMR_ENC_PCM_STOP, OSA_FLAG_OR);
        return 0;
    }

    int amrRecord_dtx_enable(int *on_off, int read_flag)
    {
        if(!!read_flag) {
            *on_off = !!amr_record_dtx_enable;
        } else {
            amr_record_dtx_enable = !!(*on_off);
        }
        return 0;
    }

    //add encode data api
    amrnb_enc_config enc_config = { 0 };
    amrnb_enc_handle enc_handle = 0;
    int amrencode_data_init(unsigned int mode, unsigned int format)
    {
    	int ret = 0;
    	if(enc_handle){
    		uart_printf("amrnb encode is opening !\n");
    		return -1;
    	}
    	enc_config.mode = mode;
    	enc_config.output_format = format;
    	enc_config.dtx_mode = amr_record_dtx_enable;
    	ret = amrnb_encode_open(&enc_config, &enc_handle);
     	if(ret < 0){
    		uart_printf("amrnb encode open failed !\n");
    		return -1;
    	}
    	return 0;
    }

    int amrencode_data(unsigned char *input_buff, unsigned char *output_buff)
    {
    	int ret = 0, output_size = 0;
    	
    	if(enc_handle == NULL){
    		return -1;
    	}
    	ret = amrnb_encode_set(enc_handle, input_buff, 320);
    	if(ret < 0){
    		uart_printf("amrnb encode set error !\n");
    		return -2;
    	}
    	ret = amrnb_encode_do(enc_handle);
    	if (ret != 0){
    		uart_printf("amrnb encode error \n");
    		return -3;
    	}
    	ret = amrnb_encode_get(enc_handle, output_buff, &output_size);
    	if(ret < 0){
    		uart_printf("amrnb encode get error !\n");
    		return -4;
    	}

    	return output_size;
    }

    int amrencode_data_deinit(void)
    {
    	int ret = 0;
    	if(!enc_handle){
    		uart_printf("amrnb encode is not open !\n");
    		return -1;
    	}
    	ret = amrnb_encode_close(enc_handle);
     	if(ret < 0){
    		uart_printf("amrnb encode close failed !\n");
    		return -1;
    	}
    	enc_handle = false;
    	return 0;
    }
#endif
}

