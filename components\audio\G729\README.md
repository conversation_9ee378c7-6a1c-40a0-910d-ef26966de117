[![pipeline status](https://gitlab.linphone.org/BC/public/bcg729/badges/master/pipeline.svg)](https://gitlab.linphone.org/BC/public/bcg729/commits/master)

Bcg729
======

About bcg729
------------

Bcg729 is an opensource implementation of both encoder and decoder of the ITU G729 Annex A/B speech codec. 

The library written in C 99 is fully portable and can be executed on many platforms including both ARM and x86 processors. 
libbcg729 supports concurrent channels encoding/decoding for multi call application such as conferencing. 

For more information, [please visit bcg729's homepage on **linphone.org**](https://linphone.org/technical-corner/bcg729).
 
Licensing
---------

Copyright © Belledonne Communications

bcg729 is dual licensed, and is available either :

 - under a [GNU/GPLv3 license](https://www.gnu.org/licenses/gpl-3.0.en.html), for free (open source). Please make sure that you understand and agree with the terms of this license before using it (see LICENSE.txt file for details).

 - under a proprietary license, for a fee, to be used in closed source applications. Contact [Belledonne Communications](https://www.linphone.org/contact) for any question about costs and services.

Patents
-------

ITU G729 Annex A/B were offically released October/November 1996 (https://www.itu.int/rec/T-REC-G.729),
hence all patents covering these specifications shall have expired in November 2016.
Patent pool administrator confirmed most licensed patents under the G.729 Consortium have expired (<http://www.sipro.com/G729.html>).

Compilation
-----------

### Dependencies

No dependency is requested.


### Build procedure

Building by Autotools way is deprecated. Use [CMake][cmake-website] to configure the source code.

	cmake . -DCMAKE_INSTALL_PREFIX=<prefix> -DCMAKE_PREFIX_PATH=<search_prefixes>
	
	make
	make install


### Supported build options

* `CMAKE_INSTALL_PREFIX=<string>` : install prefix
* `CMAKE_PREFIX_PATH=<string>`    : column-separated list of prefixes where to look for dependencies
* `ENABLE_SHARED=NO`              : do not build the shared library
* `ENABLE_STATIC=NO`              : do not build the static library
* `ENABLE_TESTS=NO`               : do not build non-regression tests


### Note for packagers

Our CMake scripts may automatically add some paths into research paths of generated binaries.
To ensure that the installed binaries are striped of any rpath, use `-DCMAKE_SKIP_INSTALL_RPATH=ON`
while you invoke cmake.

Tests suite
-----------

- Tests are defined for each functional bloc (more or less matching a source file)
  and for global encoding/decoding

- Use `-DENABLE_TESTS=YES` to compile with tests To run all tests available in the test directory, run testCampaignAll from test directory

- Input tests pattern have been generated by ITU code using ITU tests patterns.
  The test patterns are not part of this repository but can be downloaded [here][bcg729-patern]. However, the first
  run of `make check` will get them for you.

- To run partial test, use perl executable `testCampaign` in the test directory.
  `./testCampaign <functional bloc name>`
  You must first download the tests patterns using `make check` or manually


---------------------------------------


[bcg729-patern]: http://www.belledonne-communications.com/bc-downloads/bcg729-patterns.zip
[cmake-website]: https://cmake.org/
