# Copyright (C) 2020 QUECTEL Technologies Limited and/or its affiliates("QUECTEL").
# All rights reserved.
#

set(target ql_decode)

add_app_libraries($<TARGET_FILE:${target}>)
add_library(${target} STATIC)
#set_target_properties(${target} PROPERTIES ARCHIVE_OUTPUT_DIRECTORY ${out_quec_lib_dir})
set_target_properties(${target} PROPERTIES ARCHIVE_OUTPUT_DIRECTORY ${SOURCE_TOP_DIR}/components/newlib/armca5)
target_compile_definitions(${target} PRIVATE OSI_LOG_TAG=LOG_TAG_QUEC)
target_include_directories(${target} PUBLIC inc opencv/core/inc opencv/imgproc/inc ../../../../atr/include iconv/inc iconv/lib iconv/libcharset/include iconv/libcharset decode/inc)
target_link_libraries(${target} PRIVATE kernel driver hal ql_api_common ql_bsp ql_iconv ql_urc)

target_sources(${target} PRIVATE
	decode/src/ql_decoder.c
	decoder/config.c
	decoder/decoder.c
	decoder/image.c
	decoder/img_scanner.c
	decoder/refcnt.c
	decoder/scanner.c
	decoder/symbol.c
	decoder_code/code39.c
	decoder_code/code128.c
	decoder_code/ean.c
	decoder_code/i25.c
	#decoder_code/pdf417.c
	decoder_code/qr_finder.c
	decoder_qrcode/bch15_5.c
	decoder_qrcode/binarize.c
	decoder_qrcode/isaac.c
	decoder_qrcode/qrdec.c
	#decoder_qrcode/qrdectxt.c
	decoder_qrcode/rs.c
	decoder_qrcode/util.c
	opencv/core/src/alloc.c
	#opencv/core/src/approx.c
	#opencv/core/src/array.c
	#opencv/core/src/datastructs.c
	#opencv/imgproc/src/contours.c
	#opencv/imgproc/src/shapedescr.c
	#opencv/imgproc/src/thresh.c
	#opencv/imgproc/src/utils.c
	opencv/imgproc/src/newcheck.c
	iconv/lib/iconv.c
	iconv/libcharset/lib/localcharset.c
)

relative_glob(srcs include/*.h src/*.c inc/*.h)
beautify_c_code(${target} ${srcs})

#add_subdirectory_if_exist(iconv)