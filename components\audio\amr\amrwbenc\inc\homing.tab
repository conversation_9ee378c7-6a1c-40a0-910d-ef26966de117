
/*
 ** Copyright 2003-2010, VisualOn, Inc.
 **
 ** Licensed under the Apache License, Version 2.0 (the "License");
 ** you may not use this file except in compliance with the License.
 ** You may obtain a copy of the License at
 **
 **     http://www.apache.org/licenses/LICENSE-2.0
 **
 ** Unless required by applicable law or agreed to in writing, software
 ** distributed under the License is distributed on an "AS IS" BASIS,
 ** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ** See the License for the specific language governing permissions and
 ** limitations under the License.
 */



#define DHF_PARMS_MAX 32               /* homing frame pattern  */
#define NUM_OF_SPMODES 9

#define PRML 15
#define PRMN_7k (NBBITS_7k/PRML + 1)
#define PRMN_9k (NBBITS_9k/PRML + 1)
#define PRMN_12k (NBBITS_12k/PRML + 1)
#define PRMN_14k (NBBITS_14k/PRML + 1)
#define PRMN_16k (NBBITS_16k/PRML + 1)
#define PRMN_18k (NBBITS_18k/PRML + 1)
#define PRMN_20k (NBBITS_20k/PRML + 1)
#define PRMN_23k (NBBITS_23k/PRML + 1)
#define PRMN_24k (NBBITS_24k/PRML + 1)

static const Word16 dfh_M7k[PRMN_7k] =
{
  3168, 29954, 29213, 16121,
	64, 13440, 30624, 16430,
 19008
};

static const Word16 dfh_M9k[PRMN_9k] =
{
   3168, 31665,  9943, 9123,
  15599,  4358, 20248, 2048,
  17040, 27787, 16816, 13888
};

static const Word16 dfh_M12k[PRMN_12k] =
{
  3168, 31665,  9943,  9128,
  3647,  8129, 30930, 27926,
 18880, 12319,   496,  1042,
  4061, 20446, 25629, 28069,
 13948
};

static const Word16 dfh_M14k[PRMN_14k] =
{
    3168, 31665,  9943,  9131,
   24815,   655, 26616, 26764,
    7238, 19136,  6144,    88,
	4158, 25733, 30567, 30494,
	221, 20321, 17823
};

static const Word16 dfh_M16k[PRMN_16k] =
{
    3168, 31665,  9943,  9131,
   24815,   700,  3824,  7271,
   26400,  9528,  6594, 26112,
     108,  2068, 12867, 16317,
   23035, 24632,  7528,  1752,
    6759, 24576
};

static const Word16 dfh_M18k[PRMN_18k] =
{
     3168, 31665,  9943,  9135,
	14787, 14423, 30477, 24927,
	25345, 30154,   916,  5728,
	18978,  2048,   528, 16449,
	 2436,  3581, 23527, 29479,
	 8237, 16810, 27091, 19052,
	    0
};

static const Word16 dfh_M20k[PRMN_20k] =
{
     3168, 31665,  9943,  9129,
	 8637, 31807, 24646,   736,
	28643,  2977,  2566, 25564,
	12930, 13960,  2048,   834,
	 3270,  4100, 26920, 16237,
	31227, 17667, 15059, 20589,
	30249, 29123, 0
};

static const Word16 dfh_M23k[PRMN_23k] =
{
	 3168, 31665,  9943,  9132,
	16748,  3202, 28179, 16317,
	30590, 15857, 19960,  8818,
	21711, 21538,  4260, 16690,
	20224,  3666,  4194,  9497,
	16320, 15388,  5755, 31551,
	14080,  3574, 15932,    50,
	23392, 26053, 31216
};

static const Word16 dfh_M24k[PRMN_24k] =
{
	 3168, 31665,  9943,  9134,
	24776,  5857, 18475, 28535,
	29662, 14321, 16725,  4396,
	29353, 10003, 17068, 20504,
	  720,     0,  8465, 12581,
	28863, 24774,  9709, 26043,
	 7941, 27649, 13965, 15236,
	18026, 22047, 16681,  3968
};



