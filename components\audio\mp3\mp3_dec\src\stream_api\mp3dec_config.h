#pragma once
#ifdef __cplusplus
extern "C" {
#endif

#ifndef MP3_STREAM_SUPPORT
#define MP3_STREAM_SUPPORT 			(1)
#endif

#ifndef MP3_STREAM_API_TEST
#define MP3_STREAM_API_TEST 		(1)
#endif

#ifndef MP3PLAY_STREAM_USE_RAM_CACHE
#define MP3PLAY_STREAM_USE_RAM_CACHE 	(1)
#endif

#ifndef MP3PLAY_STREAM_USE_FLASH_CACHE
#define MP3PLAY_STREAM_USE_FLASH_CACHE	(0)
#endif

#ifndef MP3_FRAMES_READY_MIN
#define MP3_FRAMES_READY_MIN			(100) // clear auto pause 
#endif

#ifndef MP3_FRAMES_RAM_CACHE_MAX
#define MP3_FRAMES_RAM_CACHE_MAX		(800) // cache to flash threshold
#endif

#ifndef MP3_FRAMES_FLASH_CACHE_MIN
#define MP3_FRAMES_FLASH_CACHE_MIN		(400) // cache to ram threshold
#endif

#ifndef MP3_FLASH_CACHE_FRAMES
#define MP3_FLASH_CACHE_FRAMES			(30) // max frames in one flash cache file
#endif

#ifndef MP3_FLASH_CACHE_PREFIX
#define MP3_FLASH_CACHE_PREFIX 			"mp3playcache_"
#endif

#ifndef MP3PLAY_STREAM_USE_SEPERATE_TASK
#define MP3PLAY_STREAM_USE_SEPERATE_TASK (0)
#endif

#ifndef MP3PLAY_STREAM_KEEP_FLASH_CACHE
#define MP3PLAY_STREAM_KEEP_FLASH_CACHE	 (0)
#endif

#ifndef MP3PLAY_STREAM_TRACE
#define MP3PLAY_STREAM_TRACE			 (0)
#endif

#ifndef MP3_CACHE_MSGQ_SIZE
#define MP3_CACHE_MSGQ_SIZE				(100)
#endif

#ifndef MP3_SERIALIZE_MSGQ_SIZE
#define MP3_SERIALIZE_MSGQ_SIZE			(100)
#endif

#ifndef MP3_DESERIALIZE_MSGQ_SIZE
#define MP3_DESERIALIZE_MSGQ_SIZE		(200)
#endif

#ifndef MP3_STREAM_CACHE_SIZE
#define MP3_STREAM_CACHE_SIZE			(120)
#endif

#ifndef MP3_DEC_CALCULATE_TIME
#define MP3_DEC_CALCULATE_TIME				(1)
#endif

#ifndef FLASH_CACHE_WRITE_CALCULATE_TIME
#define FLASH_CACHE_WRITE_CALCULATE_TIME 	(1)
#endif

#ifndef FLASH_CACHE_READ_CALCULATE_TIME
#define FLASH_CACHE_READ_CALCULATE_TIME		(1)
#endif

#ifndef MP3PLAY_STREAM_RAWDATA_SUPPORT
#define MP3PLAY_STREAM_RAWDATA_SUPPORT 		(1)
#endif

#ifndef MP3PLAY_STREAM_RESYNC
#define MP3PLAY_STREAM_RESYNC				(0)
#endif

#ifdef __cplusplus
}
#endif
