   
config AUDI<PERSON> 
    bool "Enable AUDIO" 
    default n 
help 
    Audio is placed in the python SDK. Enable Audio switch.

choice AUDIO_PHY
   bool "Select audio physical interface"
   default AUDIO_CODEC
   depends on AUDIO
   help
       Select audio physical interface.

config AUDIO_CODEC
   bool "Audio decoding chip"
config AUDIO_PWM
   bool "Use pwm for audio decoding"
endchoice

config AUDIO_PWM_PIN
    int "Which pin is used for audio"
    depends on AUDIO_PWM
	range 0 100
    default 33

config AUDIO_WAV
    bool "Enable wav format"
    depends on AUDIO
    default n
help 
    Enable wav format, which is pcm data.

config AUDIO_SND
    bool "Enable SND format"
    depends on AUDIO
    default n
help 
    Enable snd format, which is pcm data.

config AUDIO_MP3
    bool "Enable mp3 format"
    depends on AUDIO
    default n
help 
    Enable mp3 format.

config AUDIO_AMR
    bool "Enable amr format"
    depends on AUDIO
    default n
help 
    Enable amrnb format.
    

config AUDIO_G711_COMPRESS
    bool "Enable G711 compression"
    default n
help 
    Enable G711 compression function.

config AUDIO_G729_COMPRESS
    bool "Enable G729 compression"
    default n
help 
    Enable G729(BGG729) compression function.

config AUDIO_G722_COMPRESS
    bool "Enable G722 compression"
    default n
help 
    Enable G722 compression function.

config AUD_RECORD 
    bool "Enable Audio Record" 
    default n 
help 
    Audio Record is placed in the python SDK. Enable Audio_Record switch.

config AUDIO_RECORD_AMRNB
    bool "Enable AMRNB format recording"
    depends on AUD_RECORD
    default n
help 
    Enable AMRNB format recording.

config AUDIO_RECORD_WAV
    bool "Enable WAV format recording"
    depends on AUD_RECORD
    default n
help 
    Enable WAV format recording.

config AUDIO_RECORD_MP3
    bool "Enable MP3 format recording"
    depends on AUD_RECORD
    default n
help 
    Enable MP3 format recording.
    
