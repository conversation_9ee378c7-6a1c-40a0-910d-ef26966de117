#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include "audio_def.h"

extern "C" {
#include "mediaPlayer.h"
#include "acm_comm.h"
#include "diag.h"
#if FEATURE_AUDIO_HAL == 0 || FEATURE_AUDIO_16K_STREAMING == 1
#include "audio_resample.h"
#include "tx_port.h" //for TX_INTERRUPT_SAVE_AREA, TX_DISABLE and TX_RESTORE

#define MAX_EXPAND			(48000 / 8000)
#define MAX_FRAMES			(10)
#define MIN_FRAME_LENGTH	(160) //8k
#define MAX_FRAME_LENGTH	(320) //16k
    typedef void(*request_trigger)(int);
    typedef struct media_play_instance {
        int32_t rate_in;
        int32_t rate_out;
        int16_t channel_num;
        int16_t frame_in_size;
        int16_t frame_out_inedx;
        int16_t drain;
        int16_t latency;
        int16_t latency_set;
        int16_t water_level;
        int16_t delay_count;
        int16_t resample_in_size;
        int16_t resample_active;
        int16_t resample_in_samples;
        int16_t resample_out_frames;
        int16_t resample_buf_in[MIN_FRAME_LENGTH * MAX_EXPAND + 1];
        int16_t resample_buf_out[MAX_FRAME_LENGTH * MAX_FRAMES];
        int16_t resample_temp_buf[MAX_FRAME_LENGTH];
        int16_t zero_buf[MAX_FRAME_LENGTH];
        int16_t paused;
        int16_t started;
        resampler resample;
        request_trigger cb;
    }media_play_instance;

    static media_play_instance ins = { 0 };
    static media_play_instance* pMediaPlayInstance = NULL;
    static void media_play_out_trigger(media_play_instance* p) {
        if (!p->paused) {
            if (!p->latency) {
                int16_t residual_frames = p->resample_out_frames - p->frame_out_inedx;
                if (residual_frames > 0) {
                    memmove(&p->resample_buf_out[0], &p->resample_buf_out[MAX_FRAME_LENGTH * p->frame_out_inedx], residual_frames * sizeof(int16_t) * MAX_FRAME_LENGTH);
                }

                p->resample_out_frames = residual_frames;
                p->frame_out_inedx = 0;
            }

            if ((p->resample_out_frames < p->latency_set) && p->cb) {
                p->cb(media_play_event_underrun);
            }
        }
    }

    static void media_play_out_fetch(media_play_instance* p, int16_t** data, uint32_t* size, int16_t fill_zero) {
        *size = p->frame_in_size * sizeof(int16_t);
        if (!fill_zero) {
            *data = &p->resample_buf_out[MAX_FRAME_LENGTH * p->frame_out_inedx];
            p->frame_out_inedx++;
        }
        else {
            *data = &p->zero_buf[0];
        }

        //DIAG_FILTER(AUDIO, media_play_out_fetch, play_out_dump, DIAG_INFORMATION)
        diagStructPrintf("play_out_dump", *data, *size);
    }

    static void dlstream(DOWNLINKSTREAM_REQUEST* request) {
        int32_t frames = pMediaPlayInstance->resample_out_frames;
        int32_t i = 0;
        int32_t frames_out = 1;
        //DIAG_FILTER(AUDIO, Media, dlstream, DIAG_INFORMATION)
        AUDLOGI("request:0x%lx,frames:%d,latency:%d,paused:%d,water_level:%d", request, frames, pMediaPlayInstance->latency, pMediaPlayInstance->paused, pMediaPlayInstance->water_level);

        if (!pMediaPlayInstance->paused && !pMediaPlayInstance->latency) {
            if (frames > 0) {
                if (pMediaPlayInstance->water_level < 0) {
                    if (frames >= 2) {
                        frames_out = 2;
                        pMediaPlayInstance->water_level++;
                    }
                }
                request->handle_num = frames_out;
                for (; i < frames_out; i++) {
                    request->streamInd[i].streamHandle = 0;
                    media_play_out_fetch(pMediaPlayInstance, (int16_t**)&request->streamInd[i].data, &request->streamInd[i].dataSize, 0);
                }
            }
            else {
                if (pMediaPlayInstance->water_level <= -4) {
                    request->handle_num = 1;
                    request->streamInd[0].streamHandle = 0;
                    media_play_out_fetch(pMediaPlayInstance, (int16_t**)&request->streamInd[0].data, &request->streamInd[0].dataSize, 1);
                }
                else {
                    pMediaPlayInstance->water_level--;
                }
            }
        }
        else {
            request->handle_num = 1;
            request->streamInd[0].streamHandle = 0;
            media_play_out_fetch(pMediaPlayInstance, (int16_t**)&request->streamInd[0].data, &request->streamInd[0].dataSize, 1);
        }

        if (pMediaPlayInstance->drain && frames == 0 && pMediaPlayInstance->started == 0) {
            if (++pMediaPlayInstance->delay_count > 6) {
                ATCPCMPlayCtrl(0, 0, 0, 0, (UINT32)dlstream, 0);
                if (pMediaPlayInstance->cb)
                    pMediaPlayInstance->cb(media_play_event_end);
            }
        }

        media_play_out_trigger(pMediaPlayInstance);
        if (pMediaPlayInstance->cb)
            pMediaPlayInstance->cb(media_play_event_tick);
    }

    static void media_play_start(media_play_instance* p, int32_t option, int32_t rate, int32_t ch, void(*trigger)(int)) {
        int32_t rate_in = rate;
        int32_t rate_out = option & 0x1 ? 16000 : 8000;

        if (rate_in % 50) {
            rate_in -= (rate_in % 50);
        }
        p->rate_in = rate_in;
        p->rate_out = rate_out;
        p->channel_num = ch <= 0 ? 1 : ch;
        p->frame_in_size = rate_out * 20 / 1000;
        p->drain = (option & 0x4);
        p->latency_set = p->latency = (MAX_FRAMES - 3);
        p->started = 1;
        p->paused = 0;
        p->cb = trigger;
        if (rate_in != rate_out) {
            resampler_create(1, (option & 0x2) ? 1 : 0, rate_in, rate_out, rate_in * 20 / 1000, &p->resample);
            p->resample_active = 1;
            p->resample_in_size = p->resample.frame_size;
        }
        else {
            p->resample_active = 0;
            p->resample_in_size = p->frame_in_size;
        }
    }

    static void stream_on_media_play_start(int32_t rate, int32_t ch, int32_t option, void(*trigger)(int)) {
        int dest_end = ((option & 0x30) >> 4);
        ACM_SrcDst playto = ACM_NO_END;
        if (dest_end == 0)
            playto = ACM_NEAR_END;
        else if (dest_end == 1)
            playto = ACM_FAR_END;
        else if (dest_end == 2)
            playto = ACM_BOTH_ENDS;

        option |= 0x1;
        //DIAG_FILTER(AUDIO, Media, on_media_play_start, DIAG_INFORMATION)
        AUDLOGI("trigger:0x%lx,channel:%ld,rate:%ld,option:0x%lx", trigger, ch, rate, option);
        pMediaPlayInstance = &ins;
        if (!pMediaPlayInstance->started) {
            memset(&ins, 0, sizeof(ins));
            media_play_start(pMediaPlayInstance, option, rate, ch, trigger);
            ATCPCMPlayCtrl((option & 0x1) ? 0x7 : 0x3, playto, (option & 0x80) ? 0x1 : 0x2, (option & 0x40) ? ACM_NOT_COMB_WITH_CALL : ACM_COMB_WITH_CALL, (UINT32)dlstream, 0);
        }
    }

    static int media_play_in_trigger(media_play_instance* p, const int16_t* buf, int32_t size) {
        int status = -1;
        int32_t i = 0;
        TX_INTERRUPT_SAVE_AREA;
        for (; i < size / sizeof(int16_t); i += p->channel_num) {
            p->resample_buf_in[p->resample_in_samples++] = buf[i];
            if (p->resample_in_samples >= p->resample_in_size) {
                p->resample_in_samples = 0;
                if (p->resample_out_frames < MAX_FRAMES) {
                    int16_t* src = 0;
                    if (p->resample_active) {
                        resampler_run(&p->resample, p->resample_buf_in, &p->resample_temp_buf[0]);
                        src = &p->resample_temp_buf[0];
                    }
                    else {
                        src = &p->resample_buf_in[0];
                    }
                    //DIAG_FILTER(AUDIO, media_play_in_trigger, resampler_run, DIAG_INFORMATION)
                    diagStructPrintf("resampler_dump", src, p->frame_in_size * sizeof(int16_t));

                    /* Disable interrupts.	*/
                    TX_DISABLE;
                    memcpy(&p->resample_buf_out[p->resample_out_frames * MAX_FRAME_LENGTH], src, p->frame_in_size * sizeof(int16_t));
                    if (p->latency > 0) {
                        p->latency--;
                    }
                    p->resample_out_frames++;
                    /* Restore interrupts.  */
                    TX_RESTORE;
                    status = 0;
                }
            }

        }
        if (p->resample_out_frames >= MAX_FRAMES - 2 && p->cb) {
            p->cb(media_play_event_overrun);
        }

        return status;
    }

    static void stream_on_media_play_update(const int16_t* buf, int32_t size) {
        if (pMediaPlayInstance->started) {
            int status = media_play_in_trigger(pMediaPlayInstance, buf, size);
            //DIAG_FILTER(AUDIO, Media, on_media_play_update, DIAG_INFORMATION)
            AUDLOGI("buf:0x%lx,size:%d,resample_out_frames:%d,status:%d", buf, size, pMediaPlayInstance->resample_out_frames, status);
        }
    }



    static void stream_on_media_play_drainstop(void) {

    }

    static void stream_on_media_play_stop(void) {
        pMediaPlayInstance->started = 0;
        if (!pMediaPlayInstance->drain) {
            ATCPCMPlayCtrl(0, 0, 0, 0, (UINT32)dlstream, 0);
            if (pMediaPlayInstance->cb)
                pMediaPlayInstance->cb(media_play_event_end);
        }
        if (pMediaPlayInstance->resample_active)
            resampler_destroy(&pMediaPlayInstance->resample);


    }

    static int stream_media_play_pause(media_play_instance* p, int16_t onoff) {
        if (p->started) {
            p->paused = (onoff == 0 ? 0 : 1);
            return p->paused;
        }

        return -1;
    }

    static int stream_on_media_play_suspend(void) {
        int paused = stream_media_play_pause(pMediaPlayInstance, 1);
        //DIAG_FILTER(AUDIO, Media, on_media_play_suspend, DIAG_INFORMATION)
        AUDLOGI("paused:%d", paused);
        return paused;
    }

    static int stream_on_media_play_resume(void) {
        int paused = stream_media_play_pause(pMediaPlayInstance, 0);
        //DIAG_FILTER(AUDIO, Media, on_media_play_resume, DIAG_INFORMATION)
        AUDLOGI("paused:%d", paused);
        return paused;
    }

    static void stream_on_media_play_drain(int drain) {
        if (pMediaPlayInstance)
            pMediaPlayInstance->drain = (drain == 0 ? 0 : 1);
    }
#endif

#if FEATURE_AUDIO_HAL == 1
#include "helios_include.h"
#include "osa_old_api.h"
#include "AudioHAL.h"

    typedef void(*request_trigger)(int);
    extern AUDIOHAL_ERR_T AudioHAL_SetResBufCnt(unsigned int bufCnt);
    enum {
        kInputBufferSize = 10 * 1024,
        kOutputBufferSize = 4608 * 2,
    };

#define DEFAULT_CACHE_SIZE    (9216)  /* 4 frame*/
    static unsigned short dec_pcm_pingpong_buffer[kOutputBufferSize]; // two frame
    static unsigned short cache_buffer_default[DEFAULT_CACHE_SIZE];
    static unsigned short* cache_buffer = cache_buffer_default;
    static int cacheSize = DEFAULT_CACHE_SIZE;
    static int mp3FrameSize = 0;
    static int cacheReadItem = 0;
    static int cacheWriteItem = 0;
    static int cacheItemCnt = 0;
    static int samplerate = 0;
    static int channels = 0;
    static request_trigger cb = 0;
    static int paused = 0;
    static AUDIOHAL_STREAM_T dec_pcm_stream;
    static int dec_pingpong_index = 0;
    static int first_time = 1;
    static int current_option = 0;
    static int stopable = 0;
    static int started = 0;

    static int isCacheFull(void)
    {
        return (cacheWriteItem >= (cacheReadItem + cacheItemCnt));
    }

    static int isCacheEmpty(void)
    {
        return (cacheWriteItem == cacheReadItem);
    }


    // return value   -1: error.   1: full 
    static int fillCache(const int16_t* buf, int32_t size)
    {
        unsigned short* start = NULL;

        if (0 == mp3FrameSize) {
            //DIAG_FILTER(AUDIO, MP3_DEC, fillCache_error, DIAG_INFORMATION)
            AUDLOGI("mp3FrameSize is 0");
            return -1;
        }
        if (0 == cacheItemCnt) {
            cacheItemCnt = cacheSize / mp3FrameSize;
        }

        if (isCacheFull()) {
            //DIAG_FILTER(AUDIO, MP3_DEC, fillCache_full, DIAG_INFORMATION)
            AUDLOGI("cacheWriteItem:0x%lx,cacheReadItem:0x%lx, cacheItemCnt:0x%lx",
                cacheWriteItem, cacheReadItem, cacheItemCnt);
            return 1;
        }

        start = cache_buffer + (cacheWriteItem % cacheItemCnt) * mp3FrameSize;
        memcpy(start, buf, size);
        cacheWriteItem++;
        return 0;
    }

    void mp3_pcm_half_handler(void)
    {
        char* bufferstart = (char*)dec_pcm_stream.startAddress;
        unsigned short* input = NULL;

        if (dec_pingpong_index % 2) { bufferstart += dec_pcm_stream.length / 2; }

        if (!isCacheEmpty()) {
            input = cache_buffer + (cacheReadItem % cacheItemCnt) * mp3FrameSize;
        }

        //DIAG_FILTER(AUDIO, HAL, mp3_pcm_half_handler_print, DIAG_INFORMATION)
        AUDLOGI("cacheWriteItem=0x%lx,cacheReadItem=0x%lx,cacheItemCnt=0x%lx, mp3FrameSize=0x%lx",
            cacheWriteItem, cacheReadItem, cacheItemCnt, mp3FrameSize);

        //DIAG_FILTER(AUDIO, HAL, mp3_pcm_half_handler, DIAG_INFORMATION)
        AUDLOGI("bufferstart=0x%lx,input=0x%lx,", bufferstart, input);

        if (input) {
            memcpy(bufferstart, input, dec_pcm_stream.length / 2);
            cacheReadItem++;
        }

        dec_pingpong_index++;

        if (cb && !paused)
            cb(media_play_event_underrun);

        if (stopable && isCacheEmpty()) {
            if (cb) {
                cb(media_play_event_drain_end);
            }
        }
        return;
    }

    static AUDIOHAL_ERR_T dec_pcm_start(unsigned int rate, unsigned int ch, unsigned int frameByteSize)
    {
        AUDIOHAL_ERR_T rc = AUDIOHAL_ERR_NO;
        //DIAG_FILTER(AUDIO, MP3_DEC, pcm_start, DIAG_INFORMATION)
        AUDLOGI("pcm_start, rate:0x%lx, ch:0x%lx, frameByteSize:0x%lx", rate, ch, frameByteSize);

        AudioHAL_set_close_delay(0);
        dec_pingpong_index = 0;
        stopable = 0;

        dec_pcm_stream.channelNb = (AUDIOHAL_CH_NB_T)ch;
        dec_pcm_stream.length = frameByteSize * 2;

        memset(dec_pcm_pingpong_buffer, 0, dec_pcm_stream.length);

        dec_pcm_stream.playSyncWithRecord = 0;
        dec_pcm_stream.voiceQuality = 0;
        dec_pcm_stream.sampleRate = (AUDIOHAL_FREQ_T)(rate);
        dec_pcm_stream.startAddress = (UINT32 *)dec_pcm_pingpong_buffer;
        dec_pcm_stream.halfHandler = mp3_pcm_half_handler;

        rc = AudioHAL_AifPlayStream(&dec_pcm_stream);
        return rc;
    }

    static void dec_pcm_stop(void)
    {
        //DIAG_FILTER(AUDIO, MP3_DEC, pcm_stop, DIAG_INFORMATION)
        AUDLOGI("pcm_stop,option:0x%lx", current_option);

        if ((current_option & 0x4) == 0) {
            stopable = 0;
            //DIAG_FILTER(AUDIO, MP3_DEC, pcm_stop_return, DIAG_INFORMATION)
            AUDLOGI("pcm_stop_return");
            AudioHAL_AifStopPlay();
            AudioHAL_SetResBufCnt(4);
            if (cb)
                cb(media_play_event_end);
            started = 0;
            cb = 0;
        }
        else {
            stopable = 1;
        }
    }

    static void hal_on_media_play_start(int32_t rate, int32_t ch, int32_t option, void(*trigger)(int)) {
        //DIAG_FILTER(AUDIO, MP3_DEC, on_media_play_start, DIAG_INFORMATION)
        AUDLOGI("rate:%ld,ch:%ld,option:0x%lx,trigger:0x%lx", rate, ch, option, trigger);
        if (started == 0) {
		
            started = 1;
            cb = trigger;
            samplerate = rate;
            channels = ch;
            mp3FrameSize = 0;

            paused = 0;
            cacheReadItem = 0;
            cacheWriteItem = 0;
            cacheItemCnt = 0;
            first_time = 1;
            current_option = option;
            if (cb)
                cb(media_play_event_underrun);
        }
    }

    static void hal_on_media_play_update(const int16_t* buf, int32_t size) {
        //DIAG_FILTER(AUDIO, MP3_DEC, on_media_play_update, DIAG_INFORMATION)
        AUDLOGI("buf:0x%lx,size:%ld", buf, size);
        mp3FrameSize = size / sizeof(short);
        fillCache(buf, size);
        if (!isCacheFull()) {
            if (cb)
                cb(media_play_event_underrun);
        }

        if (first_time) {
            first_time = 0;
            int resBufCnt = cacheItemCnt * 26 / 20 + 1;
            if (resBufCnt > 4) {
                if (resBufCnt > 20) { resBufCnt = 20; }
                AudioHAL_SetResBufCnt(resBufCnt);
            }
            dec_pcm_start(samplerate, channels, mp3FrameSize * sizeof(int16_t));
        }
    }

    static void hal_on_media_play_stop(void) {
        //DIAG_FILTER(AUDIO, MP3_DEC, on_media_play_stop, DIAG_INFORMATION)
        AUDLOGI("mp3FrameSize:%ld,paused:%ld", mp3FrameSize, paused);
        dec_pcm_stop();
    }

    static void hal_on_media_play_drainstop(void) {
        //DIAG_FILTER(AUDIO, MP3_DEC, pcm_stop_drain_start, DIAG_INFORMATION)
        AUDLOGI("pcm_stop_drain_start");
        AudioHAL_AifDrain();
        AudioHAL_AifStopPlay();
        AudioHAL_SetResBufCnt(4);
        if (cb)
            cb(media_play_event_end);
        cb = 0;
        started = 0;
        //DIAG_FILTER(AUDIO, MP3_DEC, pcm_stop_drain_return, DIAG_INFORMATION)
        AUDLOGI("pcm_stop_drain_return");
    }

    static int hal_media_play_pause(int16_t onoff) {
        AUDIOHAL_ERR_T ret = AudioHAL_AifPause(onoff ? 1 : 0);
        if (ret == AUDIOHAL_ERR_NO) {
            paused = onoff ? 1 : 0;
            if (!paused && cb)
                cb(media_play_event_underrun);
            //DIAG_FILTER(AUDIO, MP3_DEC, media_play_pause, DIAG_INFORMATION)
            AUDLOGI("paused:%d!", paused);
            return (paused);
        }
        return -1;
    }

    static int hal_on_media_play_suspend(void) {
        int paused = hal_media_play_pause(1);
        //DIAG_FILTER(AUDIO, Media, on_media_play_suspend, DIAG_INFORMATION)
        AUDLOGI("paused:%d", paused);
        return paused;
    }

    static int hal_on_media_play_resume(void) {
        int paused = hal_media_play_pause(0);
        //DIAG_FILTER(AUDIO, Media, on_media_play_resume, DIAG_INFORMATION)
        AUDLOGI("paused:%d", paused);
        return paused;
    }

    static void hal_on_media_play_drain(int drain) {
        if (drain)
            current_option |= 0x4;
        else
            current_option &= (~0x4);
    }
#endif

    typedef struct media_play_handler {
        void(*start)(int32_t rate, int32_t ch, int32_t option, void(*trigger)(int));
        void(*update)(const int16_t* buf, int32_t size);
        void(*stop)(void);
        void(*drainstop)(void);
        void(*drain)(int drain);
        int(*suspend)(void);
        int(*resume)(void);
        int mode;
    }media_play_handler;

    static media_play_handler handler = { 0 };
    static void update_media_play_ops(media_play_handler* mp, int use_hal) {
        if (mp && !mp->mode) {
            memset(mp, 0, sizeof(media_play_handler));
            if (use_hal) {
#if FEATURE_AUDIO_HAL == 1
                mp->drain = hal_on_media_play_drain;
                mp->drainstop = hal_on_media_play_drainstop;
                mp->resume = hal_on_media_play_resume;
                mp->start = hal_on_media_play_start;
                mp->stop = hal_on_media_play_stop;
                mp->suspend = hal_on_media_play_suspend;
                mp->update = hal_on_media_play_update;
                mp->mode = 1;
#endif
            }

            if (!mp->mode) {
#if FEATURE_AUDIO_HAL == 0 || FEATURE_AUDIO_16K_STREAMING == 1
                mp->drain = stream_on_media_play_drain;
                mp->drainstop = stream_on_media_play_drainstop;
                mp->resume = stream_on_media_play_resume;
                mp->start = stream_on_media_play_start;
                mp->stop = stream_on_media_play_stop;
                mp->suspend = stream_on_media_play_suspend;
                mp->update = stream_on_media_play_update;
                mp->mode = 2;
#endif
            }
        }
    }

    void on_media_play_start(int32_t rate, int32_t ch, int32_t option, void(*trigger)(int)) {
        // consider playing file case, start is always before update
        if (!handler.mode) {
            int play_in_voicecall = isVoiceEnabled();
            int play_use_hal = 0;
            if (!play_in_voicecall)
                play_use_hal = 1;
            update_media_play_ops(&handler, play_use_hal);
        }

        if (handler.mode)
            return handler.start(rate, ch, option, trigger);
    }

    void on_media_play_update(const int16_t* buf, int32_t size) {
        if (!handler.mode) {
            int play_in_voicecall = isVoiceEnabled();
            int play_use_hal = 0;
            if (!play_in_voicecall) {
                play_use_hal = 1;
            }
            update_media_play_ops(&handler, play_use_hal);
        }

        if (handler.mode)
            return handler.update(buf, size);
    }

    void on_media_play_stop(void) {
        if (handler.mode) {
            handler.stop();
            handler.mode = -1;
        }
    }

    void on_media_play_drainstop(void) {
        if (handler.mode) {
            handler.drainstop();
            handler.mode = 0;
        }
    }

    int on_media_play_suspend(void) {
        if (handler.mode)
            return handler.suspend();

        return -1;
    }

    int on_media_play_resume(void) {
        if (handler.mode)
            return handler.resume();

        return -1;
    }

    void on_media_play_drain(int drain) {
        if (handler.mode)
            return handler.drain(drain);
    }

    int mp3dec_set_cache_buffer(void* start, int size)
    {
        //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_set_cache_buffer, DIAG_INFORMATION)
        AUDLOGI("start:0x%lx, size:0x%lx", start, size);
#if FEATURE_AUDIO_HAL == 1 	
        if (start) {
            cache_buffer = (unsigned short*)start;
            cacheSize = size / 2;
        }
        else {
            cache_buffer = cache_buffer_default;
            cacheSize = DEFAULT_CACHE_SIZE;
        }

        //DIAG_FILTER(AUDIO, MP3_DEC, mp3dec_set_cache_buffer_done, DIAG_INFORMATION)
        AUDLOGI("cache_buffer:%lx, cacheSize:%lx", cache_buffer, cacheSize);
#endif
        return 0;
    }
}
