#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include "newcheck.h"
#include "ql_imgdeal.h"
#include "ql_zo_malloc.h"

#include "helios_debug.h"
#define DECODE_LOG(msg, ...)      custom_log(decode, msg, ##__VA_ARGS__)

/*
1.图像分块
2.分块后寻找符合标准的块
	a.一个块分为横竖两种查法
	b.找到每个符合波形的块
	c.通过查找最大连通域获取定位
3.通过排查得到的块获取坐标
4.通过
*/


typedef struct imgwavedata
{
	Pt minPt;
	Pt maxPt;
}ImgWaveData;

typedef struct checkwave
{
	unsigned char wave_count;
	unsigned char con_flag;
}CheckWave;

#define HAVE_WAVE 1
#define NO_WAVE 0

#define NOT_FLAG 0
#define WAVE_FLAGED 1
#define NO_WAVE_FLAG 255

//#define ABS(x) (x>0)?(x):(-x)
#define DECODER_MAX(x,y) (x>y)?(x):(y)
//#define DECODER_MIN(x,y) (x<y)?(x):(y)

int BLOCK_H = 0;
int BLOCK_W = 0;
int IMG_H = 0;
int IMG_W = 0;
ConBlockData cur_block_count = { 0 };

/*旋转取图
1.连通域外廓1个格
2.换算4个最外坐标点
3.4个最外坐标点换算成4个可搭建成正四边形的坐标点
	a.取一组最长邻边
	b.计算其中一个边的对边
	c.直线取图
*/

int ql_YuvImageGetY(unsigned char *inBuf, unsigned char *outBuf, unsigned int  size)
 {
	unsigned int i;
     if(inBuf == NULL)
     {
         return false;
     }
     if(outBuf == NULL)
     {
         return false;
     }
     
     for(i=0; i<size; i++)
     {
         outBuf[i] = inBuf[2*i+1];
     }
     return true;
 }

void ql_gety_convert(unsigned char *inBuf, unsigned char *outBuf, int width, int height, int out_width, int out_height, int value)
 {
     int d;
     int i,j;
     d = (width / out_width);
     if(value == 0)
     {
        for (i = 0; i < out_height; i++)
        {
            for (j = 0;j < out_width;j++)
            {
                //outBuf[i*out_width+j] = inBuf[2 * 2 *j+(2 * 2 * i * width) + 1];
                outBuf[i*out_width + j] = inBuf[d * (i*width + j)];
            }
        }
     }
     else
     {
        for (i = 0; i < out_height; i++)
        {
            for (j = 0;j < out_width;j++)
            {
                //outBuf[i*out_width+j] = inBuf[2 * 2 *j+(2 * 2 * i * width) + 1];
                //outBuf[i*out_width + j] = inBuf[2 * d * (i*width + j) + 1];
                outBuf[i*out_width + j] = ((inBuf[d * (i*width + j)] < value)?(0):(255));
            }
        }
     }
 }

/*旋转抠图 + 邻域缩放*/
void RotAndCut(unsigned char *raw, unsigned char *outdata, int imgWidth, AreaData areadata, int uiZoomInWidth, int uiZoomInHigh)
{
	int x = 0, y = 0, i = 0, j = 0;
	double  degree = 0;
	int  dx = 0, dy = 0, longSinAx16 = 0, longCosAx16 = 0, ddx = 0, ddy = 0;
	int xx = 0, yy = 0, xstart = 0, ystart = 0;
	int val = 0;
	degree = areadata.degree * PI / 180;
	longSinAx16 = (int)(sin(degree) * (1 << 16));
	longCosAx16 = (int)(cos(degree) * (1 << 16));

	if (areadata.pt[0].y > areadata.pt[2].y)
	{
		xstart = areadata.pt[2].x * (1 << 16);
		ystart = areadata.pt[2].y * (1 << 16);
	}
	else
	{
		xstart = areadata.pt[0].x * (1 << 16);
		ystart = areadata.pt[0].y * (1 << 16);
	}

	dx = 0;
	dy = 0;
	ddx = longCosAx16;
	ddy = longSinAx16;

	for (y = 0; y < areadata.wide / uiZoomInHigh; y++)
	{
		for (j = 0; j < uiZoomInHigh; j++)
		{
			dx += ((-longSinAx16));
			dy += ((longCosAx16));
			xx = xstart + dx;
			yy = ystart + dy;
			for (x = 0; x < areadata.length / uiZoomInWidth; x++)
			{
				val = 0;
				for (i = 0; i < uiZoomInWidth; i++)
				{
					xx += ddx;
					yy += ddy;
					val += raw[(yy >> 16) * imgWidth + (xx >> 16)];
				}
				outdata[(y) * (areadata.length / uiZoomInWidth) + x] = val / uiZoomInWidth;
			}
		}
	}
}

/*旋转抠图不缩放*/
void RotAndCut_1(unsigned char *raw, unsigned char *outdata, int imgWidth, AreaData areadata)
{
	DECODE_LOG("RotAndCut \r\n");
	int x, y;
	double  degree = 0;
	int  dx = 0, dy = 0, longSinAx16 = 0, longCosAx16 = 0, ddx = 0, ddy = 0;
	int xx = 0, yy = 0, xstart = 0, ystart = 0;
    
	degree = areadata.degree * PI / 180;
	longSinAx16 = (int)(sin(degree) * (1 << 16));
	longCosAx16 = (int)(cos(degree) * (1 << 16));

	if (areadata.pt[0].y > areadata.pt[2].y)
	{
		xstart = areadata.pt[2].x * (1 << 16);
		ystart = areadata.pt[2].y * (1 << 16);
	}
	else
	{
		xstart = areadata.pt[0].x * (1 << 16);
		ystart = areadata.pt[0].y * (1 << 16);
	}

	dx = 0;
	dy = 0;
	ddx = longCosAx16;
	ddy = longSinAx16;
	for (y = 0; y < areadata.wide; y++)
	{
		dx += ((-longSinAx16));
		dy += ((longCosAx16));
		xx = xstart + dx;
		yy = ystart + dy;

		for (x = 0; x < areadata.length; x++)
		{
			xx += ddx;
			yy += ddy;

			outdata[y * areadata.length + x] = raw[(yy >> 16) * imgWidth + (xx >> 16)];
		}
	}	
}

/*判断是条形码还是二维码*/
unsigned char isBarCode(AreaData areadata)
{
	int width = 0, height = 0;
	width = areadata.length;
	height = areadata.wide;
	DECODE_LOG("width = %d, height = %d", width, height);
	if (width == height)
	{
		DECODE_LOG("check is qr code!");
		return 0;
	}
	else if (width > height)
	{
		if (width > (height + height / 2))
		{
			DECODE_LOG("check is bar code!");
			return 1;
		}
		
	}
	else
	{
		if (height > (width + width / 2))
		{
			DECODE_LOG("check is bar code!");
			return 1;
		}
	}
	DECODE_LOG("check is qr code!");
	return 0;
}

/*单行像素重组条形码，带角度版本*/
void singlePixBuildBarCode_degree(unsigned char *raw, unsigned char *outdata, int rawWidth, int width, int height, Pt startPt, int sin, int cos, int direction)
{
	int x = 0, y = 0;
	int ddx = 0, ddy = 0;
	int xx = 0, yy = 0;
	
	if (direction == VERTICAL) //垂直方向
	{
		for (x = 0; x < width; x++)
		{
			outdata[x] = raw[(startPt.y + x) * rawWidth + startPt.x];
		}
	}
	else if (direction == HORIZONTAL)  //水平方向
	{
		for (x = 0; x < width; x++)
		{
			outdata[x] = raw[startPt.y * rawWidth + startPt.x + x];
		}
	}
	else if (direction == NORMAL) //倾斜方向
	{
		ddx = cos;
		ddy = sin;
		xx = startPt.x;
		yy = startPt.y;

		for (x = 0; x < width; x++)
		{
			xx += ddx;
			yy += ddy;

			outdata[x] = raw[(yy >> 16) * rawWidth + (xx >> 16)];
		}
	}
	else
	{
		return;
	}

	for (y= 1; y < height; y++)
	{
		for (x = 0; x < width; x++)
		{
			outdata[y*width + x] = outdata[x];
		}
	}
}

/*抠图判断  旋转抠图/传统大小坐标抠图*/
ImgData getDecoderImg(unsigned char *raw, int rawWidth, AreaData areadata)
{
	Pt minPt = { 0 };
	Pt maxPt = { 0 };
	int i = 0;
	double degree = 0;
	ImgData imgdata = { 0 };
	int n = 0;
	n = rawWidth / IMG_W;
	int uiZoomInWidth = 1, uiZoomInHigh = 1;
	unsigned char ret = 0;

	for (i = 0; i < 4; i++)
	{
		areadata.pt[i].x = areadata.pt[i].x * n;
		areadata.pt[i].y = areadata.pt[i].y * n;
	}
	areadata.length = areadata.length * n;
	areadata.wide = areadata.wide * n;
	/*计算矩形倾斜角度*/
	
	/*判断条形码/二维码*/
	ret = isBarCode(areadata);
	
	if (ret)
	{
		// if(areadata.length > areadata.wide)
		// {
		// 	if (0 == (areadata.pt[0].x - areadata.pt[1].x))
		// 	{
		// 		areadata.degree = PI / 2 * 180 / PI;
		// 	}
		// 	else
		// 	{
		// 		areadata.degree = atan((double)(areadata.pt[0].y - areadata.pt[1].y) / (areadata.pt[0].x - areadata.pt[1].x)) * 180 / PI;
		// 	}
		// }
		// else
		// {
		// 	if (0 == (areadata.pt[0].x - areadata.pt[2].x))
		// 	{
		// 		areadata.degree = PI / 2 * 180 / PI;
		// 	}
		// 	else
		// 	{
		// 		areadata.degree = atan((double)(areadata.pt[0].y - areadata.pt[2].y) / (areadata.pt[0].x - areadata.pt[2].x)) * 180 / PI;
		// 	}
		// }
		imgdata.code_check = BAR_CODE;
		//imgdata.degree = areadata.degree;
		imgdata.length = areadata.length;
		imgdata.wide = areadata.wide;
		for (i = 0; i < 4; i++)
		{
			imgdata.pt[i].x = areadata.pt[i].x;
			imgdata.pt[i].y = areadata.pt[i].y;
		}
		return imgdata;
	}
	else
	{
		/*根据角度判断使用哪种截图     
		0-20、70-90传统截图
		20-70旋转截图*/
		if (0 == (areadata.pt[0].x - areadata.pt[1].x))
		{
			areadata.degree = PI / 2 * 180 / PI;
		}
		else
		{
			areadata.degree = atan((double)(areadata.pt[0].y - areadata.pt[1].y) / (areadata.pt[0].x - areadata.pt[1].x)) * 180 / PI;
		}

		if (areadata.degree < 0 && areadata.degree > -90)
		{
			degree = areadata.degree + 90;
		}
		else if (areadata.degree < -90 && areadata.degree > -180)
		{
			degree = areadata.degree + 180;
		}
		else if (areadata.degree < 180 && areadata.degree > 90)
		{
			degree = areadata.degree - 90;
		}
		else
		{
			degree = areadata.degree;
		}

		getScalingVal(areadata.block_count, &uiZoomInWidth, &uiZoomInHigh, n);

		if (degree > 70 || degree < 20)
		{
			minPt.x = maxPt.x = areadata.pt[0].x;
			minPt.y = maxPt.y = areadata.pt[0].y;
			for (i = 1; i < 4; i++)
			{
				if (minPt.x > areadata.pt[i].x)
					minPt.x = areadata.pt[i].x;
				if (minPt.y > areadata.pt[i].y)
					minPt.y = areadata.pt[i].y;
				if (maxPt.x < areadata.pt[i].x)
					maxPt.x = areadata.pt[i].x;
				if (maxPt.y < areadata.pt[i].y)
					maxPt.y = areadata.pt[i].y;
			}

			imgdata.length = (maxPt.x - minPt.x) / uiZoomInWidth;
			imgdata.wide = (maxPt.y - minPt.y) / uiZoomInHigh;

			imgdata.data = (unsigned char *)zo_mymalloc(imgdata.length * imgdata.wide);
			//imgdata.data = (unsigned char *)malloc(imgdata.length * imgdata.wide);
			if (imgdata.data == NULL)
			{
				imgdata.code_check = NO_CODE;
				return imgdata;
			}
			neighbourScaling(raw, imgdata.data, rawWidth, imgdata.length, imgdata.wide, minPt.y, minPt.x, uiZoomInWidth, uiZoomInHigh);
		}
		else
		{
			areadata.wide = areadata.wide + uiZoomInHigh - areadata.wide % uiZoomInHigh;
			areadata.length = areadata.length + uiZoomInWidth - areadata.length % uiZoomInWidth;
			imgdata.length = areadata.length / uiZoomInWidth;
			imgdata.wide = areadata.wide / uiZoomInHigh;

			imgdata.data = (unsigned char *)zo_mymalloc((areadata.length / uiZoomInWidth) * (areadata.wide / uiZoomInHigh));
			//imgdata.data = (unsigned char *)malloc((areadata.length / uiZoomInWidth) * (areadata.wide / uiZoomInHigh));
			if (imgdata.data == NULL)
			{
				imgdata.code_check = NO_CODE;
				return imgdata;
			}
			RotAndCut(raw, imgdata.data, rawWidth, areadata, uiZoomInWidth, uiZoomInHigh);
		}
	}
	imgdata.code_check = QR_CODE;
	return imgdata;
}

/*缩放倍数判断*/
void getScalingVal(int count, int *uizoominwidth, int *uizoominhigh, int n)
{
	if (n == 2)
	{
		if (count < 280) //280
		{
			*uizoominwidth = 1;
			*uizoominhigh = 1;
		}
		else if (count >= 280 && count < 900)  //280\625
		{
			*uizoominwidth = 2;
			*uizoominhigh = 2;
		}
		else if (count >= 900 && count <= 2025)  //625\1000
		{
			*uizoominwidth = 3;
			*uizoominhigh = 3;
		}
		else
		{
			*uizoominwidth = 4;
			*uizoominhigh = 4;
		}
	}
	else
	{
		if (count < 900)
		{
			*uizoominwidth = 1;
			*uizoominhigh = 1;
		}
		else if (count >= 900)
		{
			*uizoominwidth = 2;
			*uizoominhigh = 2;
		}
		*uizoominwidth = 1;
		*uizoominhigh = 1;
	}
}

/*领域缩放*/
void neighbourScaling(unsigned char *indata, unsigned char *outdata, int imgWidth, int width, int height, int min_pt_y, int min_pt_x, int uiZoomInWidth, int uiZoomInHigh)
{
	int i = 0, j = 0, k = 0, h = 0;
	int uiZoomIn = 0, uiAvg = 0, uiWidth = 0, uiHigh = 0;
	//uiWidth = width / uiZoomInWidth;
	//uiHigh = height / uiZoomInHigh;
	uiWidth = width;
	uiHigh = height;
	uiZoomIn = uiZoomInHigh * uiZoomInWidth;
	for (i = 0; i < (uiHigh); i++)
	{
		for (j = 0; j < (uiWidth); j++)
		{
			for (k = 0; k < uiZoomInHigh; k++)
			{
				for (h = 0; h < uiZoomInWidth; h++)
				{
					uiAvg += indata[(min_pt_y + i * uiZoomInHigh + k) * (imgWidth)+min_pt_x + j * uiZoomInWidth + h];
				}
			}
			outdata[i*(uiWidth)+j] = uiAvg / uiZoomIn;
			uiAvg = 0;
			//outdata[i*(width)+j] = indata[(min_pt_y + i * uiZoomInHigh) * (imgWidth) + min_pt_x + j * uiZoomInWidth];
		}
	}
}

/*块坐标点转换点坐标点*/
ConBlockData BlockChancePot(ConBlockData blockdata)
{
	ConBlockData output_blockdata = { 0 };
	//output_blockdata.min_h_pt.x = blockdata.min_h_pt.x * BLOCK_SIZE;
	//output_blockdata.min_h_pt.y = blockdata.min_h_pt.y * BLOCK_SIZE;
	//output_blockdata.min_w_pt.x = blockdata.min_w_pt.x * BLOCK_SIZE;
	//output_blockdata.min_w_pt.y = blockdata.min_w_pt.y * BLOCK_SIZE;

	//output_blockdata.max_h_pt.x = (blockdata.max_h_pt.x + 1) * BLOCK_SIZE;
	//output_blockdata.max_h_pt.y = (blockdata.max_h_pt.y + 1) * BLOCK_SIZE;
	//output_blockdata.max_w_pt.x = (blockdata.max_w_pt.x + 1) * BLOCK_SIZE;
	//output_blockdata.max_w_pt.y = (blockdata.max_w_pt.y + 1) * BLOCK_SIZE;

	return output_blockdata;
}

ConBlockData BlockChancePotAgv(ConBlockData blockdata)
{
	ConBlockData output_blockdata = { 0 };
	output_blockdata.block_count = blockdata.block_count;
	//output_blockdata.min_h_pt.x = blockdata.min_h_pt.x * BLOCK_SIZE * 2;
	//output_blockdata.min_h_pt.y = blockdata.min_h_pt.y * BLOCK_SIZE * 2;
	//output_blockdata.min_w_pt.x = blockdata.min_w_pt.x * BLOCK_SIZE * 2;
	//output_blockdata.min_w_pt.y = blockdata.min_w_pt.y * BLOCK_SIZE * 2;

	//output_blockdata.max_h_pt.x = (blockdata.max_h_pt.x + 1) * BLOCK_SIZE * 2;
	//output_blockdata.max_h_pt.y = (blockdata.max_h_pt.y + 1) * BLOCK_SIZE * 2;
	//output_blockdata.max_w_pt.x = (blockdata.max_w_pt.x + 1) * BLOCK_SIZE * 2;
	//output_blockdata.max_w_pt.y = (blockdata.max_w_pt.y + 1) * BLOCK_SIZE * 2;

	return output_blockdata;
}

/*点与直线交点*/
Pt getPoint(Pt pt1, Pt pt2, Pt pt3)
{
	double A = 0;
	double B = 0;
	double m = 0;
	Pt ptCross = { 0 };
	A = (double)(pt1.y - pt2.y) / (pt1.x - pt2.x);
	B = (double)(pt1.y - A * pt1.x);
	/// > 0 = ax +b -y;  对应垂线方程为 -x -ay + m = 0;(mm为系数)
	/// > A = a; B = b;

	m = (double)(pt3.x + A * pt3.y);

	/// 求两直线交点坐标
	ptCross.x = ((m - A * B) / (A*A + 1));
	ptCross.y = (A*ptCross.x + B);

	return ptCross;
}

/*点与直线交点*/
Pt getPoint1(Pt pt, double A, double B)
{
	int m = 0;
	Pt ptCross = { 0 };

	m = pt.x + A * pt.y;
	ptCross.x = ((m - A * B) / (A*A + 1));
	ptCross.y = (A*ptCross.x + B);

	return ptCross;
}

/*2点距离*/
int getLengthFrom2Point(Pt pt1, Pt pt2)
{
	int length = 0;
	int dx = 0, dy = 0;

	dx = abs(pt1.x - pt2.x);
	dy = abs(pt1.y - pt2.y);

	length = sqrt(dx*dx + dy * dy);

	return length;
}

/*点到线最长距离*/
int getMaxLength(Pt pt1, Pt pt2, Pt pt3, Pt pt4)
{
	double A = 0;
	double A_B = 0;
	int B = 0;
	int d1, d2;
	A = (pt1.y - pt2.y) / (pt1.x - pt2.x);
	B = (pt1.y - A * pt1.x);
	A_B = (sqrt(A*A + 1));
	/// > 0 = ax +b -y;
	d1 = abs((A*pt3.x + B - pt3.y)) / A_B;
	d2 = abs((A*pt4.x + B - pt4.y)) / A_B;

	return DECODER_MAX(d1, d2);
}

int getMaxLength1(Pt pt, double A, double A_B, double B)
{
	int d1;
	/// > 0 = ax +b -y;
	d1 = abs((A*pt.x + B - pt.y)) / A_B;

	return d1;
}

/*4点求最长邻点线*/
int getMaxLine(Pt *pt)
{
	int length = 0, maxlength = 0, maxNo = 0;
	int dx = 0, dy = 0;
	int i = 0;
	for (i = 0; i < 4; i++)
	{
		dx = pt[i].x - pt[i + 1].x;
		dy = pt[i].y - pt[i + 1].y;

		if (dx == 0)
		{
			length = dy;
		}
		else if (dy == 0)
		{
			length = dx;
		}
		else
		{
			length = sqrt(dx*dx + dy * dy);
		}

		if (maxlength < length)
		{
			maxlength = length;
			maxNo = i;
		}
	}

	return maxNo;
}

/*多边形求最小矩形面积，返回相关信息*/
AreaData getAreaPercent(ConBlockData blockdata)
{
	AreaData a = { 0 };
	PtNode *p = blockdata.head;
	PtNode *p_next = NULL;
	PtNode *p_cur = NULL;
	Pt maxPt = { 0 };
	Pt minPt = { 0 };
	Pt elsePt = { 0 };
	Pt maxWidePt[2] = { 0 };
	int i = 0, j = 0;
	int maxLength = 0, maxWide = 0, wide = 0;
	int blockArea = 0, area = 0, minArea = 0;
	int x = 0, y = 0;
	double A = 0;
	double A_B = 0;
	double B = 0;

	if (blockdata.total < 3)
	{
		return a;
	}
		

	blockArea = BLOCK_SIZE * BLOCK_SIZE * cur_block_count.block_count;

	for (i = 0; i < blockdata.total; i++)
	{
		//求面积
		if (p->next != NULL)
			p_next = p->next;
		else
			p_next = blockdata.head;

		if (p_next->next != NULL)
			p_cur = p_next->next;
		else
			p_cur = blockdata.head;

		if (p->pt.x == p_next->pt.x) //垂直，线垂直，不代表矩形是竖的
		{
			if (p->pt.y > p_next->pt.y)
			{
				minPt.x = p_next->pt.x;
				minPt.y = p_next->pt.y;
				maxPt.x = p->pt.x;
				maxPt.y = p->pt.y;
			}
			else
			{
				minPt.x = p->pt.x;
				minPt.y = p->pt.y;
				maxPt.x = p_next->pt.x;
				maxPt.y = p_next->pt.y;
			}
			maxWide = 0;
			for (j = 0; j < (blockdata.total - 2); j++)
			{
				if (p_cur->pt.y < minPt.y)
					minPt.y = p_cur->pt.y;
				if (p_cur->pt.y > maxPt.y)
					maxPt.y = p_cur->pt.y;

				wide = abs(p_cur->pt.x - p->pt.x);
				if (wide > maxWide)
				{
					maxWide = wide;
					maxWidePt[0].x = p_cur->pt.x;
					maxWidePt[0].y = p_cur->pt.y;
					maxWidePt[1].x = maxPt.x;
					maxWidePt[1].y = p_cur->pt.y;
				}

				if (p_cur->next != NULL)
					p_cur = p_cur->next;
				else
					p_cur = blockdata.head;
			}

			maxLength = maxPt.y - minPt.y;
			area = maxLength * maxWide;
		}
		else if (p->pt.y == p_next->pt.y) //水平，线水平，不代表矩形是横的
		{
			if (p->pt.x > p_next->pt.x)
			{
				minPt.x = p_next->pt.x;
				minPt.y = p_next->pt.y;
				maxPt.x = p->pt.x;
				maxPt.y = p->pt.y;
			}
			else
			{
				minPt.x = p->pt.x;
				minPt.y = p->pt.y;
				maxPt.x = p_next->pt.x;
				maxPt.y = p_next->pt.y;
			}
			maxWide = 0;
			for (j = 0; j < (blockdata.total - 2); j++)
			{
				if (p_cur->pt.x < minPt.x)
					minPt.x = p_cur->pt.x;
				if (p_cur->pt.x > maxPt.x)
					maxPt.x = p_cur->pt.x;

				wide = abs(p_cur->pt.y - p->pt.y);
				if (wide > maxWide)
				{
					maxWide = wide;
					maxWidePt[0].x = p_cur->pt.x;
					maxWidePt[0].y = p_cur->pt.y;
					maxWidePt[1].x = p_cur->pt.x;
					maxWidePt[1].y = minPt.y;
				}

				if (p_cur->next != NULL)
					p_cur = p_cur->next;
				else
					p_cur = blockdata.head;
			}

			maxLength = maxPt.x - minPt.x;
			area = maxLength * maxWide;
		}
		else //普通斜线
		{
			if (p->pt.x > p_next->pt.x)
			{
				minPt.x = p_next->pt.x;
				minPt.y = p_next->pt.y;
				maxPt.x = p->pt.x;
				maxPt.y = p->pt.y;
			}
			else
			{
				minPt.x = p->pt.x;
				minPt.y = p->pt.y;
				maxPt.x = p_next->pt.x;
				maxPt.y = p_next->pt.y;
			}

			A = (double)(maxPt.y - minPt.y) / (maxPt.x - minPt.x);
			B = (double)(maxPt.y - A * maxPt.x);
			A_B = (double)(sqrt(A*A + 1));

			maxWide = 0;
			for (j = 0; j < (blockdata.total - 2); j++)
			{
				elsePt = getPoint1(p_cur->pt, A, B);
				if (elsePt.x < minPt.x)
				{
					minPt.x = elsePt.x;
					minPt.y = elsePt.y;
				}
				if (elsePt.x > maxPt.x)
				{
					maxPt.x = elsePt.x;
					maxPt.y = elsePt.y;
				}


				wide = getMaxLength1(p_cur->pt, A, A_B, B);
				if (wide > maxWide)
				{
					maxWidePt[0].x = p_cur->pt.x;
					maxWidePt[0].y = p_cur->pt.y;
					maxWidePt[1].x = elsePt.x;
					maxWidePt[1].y = elsePt.y;
					maxWide = wide;
				}

				x = maxPt.x - minPt.x;
				y = maxPt.y - minPt.y;

				if (p_cur->next != NULL)
					p_cur = p_cur->next;
				else
					p_cur = blockdata.head;
			}

			maxLength = sqrt(x * x + y * y);
			area = maxLength * maxWide;
		}
		//percent = blockArea * 100 / area;
		//DECODE_LOG("no.%d  area = %d   percent =  %d\r\n", i, area, percent);
		if (minArea == 0)
		{
			a.pt[0].x = minPt.x;
			a.pt[0].y = minPt.y;
			a.pt[1].x = maxPt.x;
			a.pt[1].y = maxPt.y;
			a.pt[2].x = minPt.x + maxWidePt[0].x - maxWidePt[1].x;
			a.pt[2].y = minPt.y + maxWidePt[0].y - maxWidePt[1].y;
			a.pt[3].x = maxPt.x + maxWidePt[0].x - maxWidePt[1].x;
			a.pt[3].y = maxPt.y + maxWidePt[0].y - maxWidePt[1].y;
			a.wide = maxWide;
			a.length = maxLength;
			a.block_count = cur_block_count.block_count;
			minArea = area;
		}
		else if (minArea > area)
		{
			a.pt[0].x = minPt.x;
			a.pt[0].y = minPt.y;
			a.pt[1].x = maxPt.x;
			a.pt[1].y = maxPt.y;
			a.pt[2].x = minPt.x + maxWidePt[0].x - maxWidePt[1].x;
			a.pt[2].y = minPt.y + maxWidePt[0].y - maxWidePt[1].y;
			a.pt[3].x = maxPt.x + maxWidePt[0].x - maxWidePt[1].x;
			a.pt[3].y = maxPt.y + maxWidePt[0].y - maxWidePt[1].y;
			a.wide = maxWide;
			a.length = maxLength;
			a.block_count = cur_block_count.block_count;
			minArea = area;
		}

		p = p->next;
	}
	a.area_percent = blockArea * 100 / minArea;
	return a;
}

/*单块检测波形*/
int SingleBlockCheckWave(unsigned char *indata, int scan_size, int waveform_check)
{
	int h = 0, w = 0;
	int cros_wave = 0, rows_wave = 0;
	int cros_max = 0, rows_max = 0;
	
	/*横扫*/
	for (h = 0; h < scan_size; h++)
	{
		for (w = 0; w < (scan_size - 1); w++)
		{
			if (abs(indata[(scan_size * h) + w] - indata[(scan_size * h) + w + 1]) > waveform_check)
			// if ((indata[(scan_size * h) + w] - indata[(scan_size * h) + w + 1]) > waveform_check || 
			// (indata[(scan_size * h) + w + 1] - indata[(scan_size * h) + w]) > waveform_check)
				cros_wave++;
		}
		if (cros_max < cros_wave)
			cros_max = cros_wave;
		cros_wave = 0;
	}
	/*竖扫*/
	for (w = 0; w < scan_size; w++)
	{
		for (h = 0; h < (scan_size - 1); h++)
		{
			if (abs(indata[(scan_size * h) + w] - indata[(scan_size * (h + 1)) + w]) > waveform_check)
			// if ((indata[(scan_size * h) + w] - indata[(scan_size * (h + 1)) + w]) > waveform_check || 
			// (indata[(scan_size * (h + 1)) + w] - indata[(scan_size * h) + w]) > waveform_check)
				rows_wave++;
		}
		if (rows_max < rows_wave)
			rows_max = rows_wave;
		rows_wave = 0;
	}
	if (cros_max > rows_max)
		return cros_max;
	return rows_max;
}

/*根据角点位置取点*/
Pt getPointFromAngular(unsigned char Angular, int x, int y)
{
	Pt retPt = { 0 };
	switch (Angular)
	{
	case LEFT_TOP:
		retPt.x = (x - 1) * BLOCK_SIZE;
		retPt.y = (y - 1) * BLOCK_SIZE;
		break;
	case LEFT_BOTTOM:
		retPt.x = (x - 1) * BLOCK_SIZE;
		retPt.y = (y + 2) * BLOCK_SIZE;
		break;
	case RIGHT_TOP:
		retPt.x = (x + 2) * BLOCK_SIZE;
		retPt.y = (y - 1) * BLOCK_SIZE;
		break;
	case RIGHT_BOTTOM:
		retPt.x = (x + 2) * BLOCK_SIZE;
		retPt.y = (y + 2) * BLOCK_SIZE;
		break;
	case LEFT:
		retPt.x = (x - 1) * BLOCK_SIZE;
		retPt.y = y * BLOCK_SIZE + BLOCK_SIZE / 2;
		break;
	case BOTTOM:
		retPt.x = x * BLOCK_SIZE + BLOCK_SIZE / 2;
		retPt.y = (y + 2) * BLOCK_SIZE;
		break;
	case RIGHT:
		retPt.x = (x + 2) * BLOCK_SIZE;
		retPt.y = y * BLOCK_SIZE + BLOCK_SIZE / 2;
		break;
	case TOP:
		retPt.x = x * BLOCK_SIZE + BLOCK_SIZE / 2;
		retPt.y = (y - 1) * BLOCK_SIZE;
		break;
	}

	if (retPt.x < 0)
		retPt.x = 0;
	if (retPt.x >= IMG_W)
		retPt.x = IMG_W - 1;
	if (retPt.y < 0)
		retPt.y = 0;
	if (retPt.y >= IMG_H)
		retPt.y = IMG_H - 1;

	return retPt;
}

/*标记角点*/
unsigned char isAngularPoint(CheckWave indata[BLOCK_COUNT][BLOCK_COUNT], int x, int y)
{
	unsigned char flag = 0, ret = 0;
	if (x == 0)//左列
	{
		flag |= LEFT_POINT;
	}
	if (y == (BLOCK_H - 1))//下行
	{
		flag |= BOTTOM_POINT;
	}
	if (x == (BLOCK_W - 1))//右列
	{
		flag |= RIGHT_POINT;
	}
	if (y == 0)//上行
	{
		flag |= TOP_POINT;
	}

	switch (flag)
	{
	case NORMAL_POINT:
		if (indata[y][x - 1].wave_count == NO_WAVE)
		{
			if (indata[y - 1][x - 1].wave_count == NO_WAVE && indata[y - 1][x].wave_count == NO_WAVE)//标记左上
			{
				ret |= LEFT_TOP;
			}
			if (indata[y + 1][x - 1].wave_count == NO_WAVE && indata[y + 1][x].wave_count == NO_WAVE)//标记左下
			{
				ret |= LEFT_BOTTOM;
			}
		}
		if (indata[y][x + 1].wave_count == NO_WAVE)
		{
			if (indata[y - 1][x + 1].wave_count == NO_WAVE && indata[y - 1][x].wave_count == NO_WAVE)//标记右上
			{
				ret |= RIGHT_TOP;
			}
			if (indata[y + 1][x + 1].wave_count == NO_WAVE && indata[y + 1][x].wave_count == NO_WAVE)//标记右下
			{
				ret |= RIGHT_BOTTOM;
			}
		}
		break;
	case LEFT_POINT:
		if (indata[y - 1][x].wave_count == NO_WAVE)
		{
			ret |= LEFT_TOP;
		}
		if (indata[y + 1][x].wave_count == NO_WAVE)
		{
			ret |= LEFT_BOTTOM;
		}
		break;
	case BOTTOM_POINT:
		if (indata[y][x - 1].wave_count == NO_WAVE)
		{
			ret |= LEFT_BOTTOM;
		}
		if (indata[y][x + 1].wave_count == NO_WAVE)
		{
			ret |= RIGHT_BOTTOM;
		}
		break;
	case RIGHT_POINT:
		if (indata[y - 1][x].wave_count == NO_WAVE)
		{
			ret |= RIGHT_TOP;
		}
		if (indata[y + 1][x].wave_count == NO_WAVE)
		{
			ret |= RIGHT_BOTTOM;
		}
		break;
	case TOP_POINT:
		if (indata[y][x - 1].wave_count == NO_WAVE)
		{
			ret |= LEFT_TOP;
		}
		if (indata[y][x + 1].wave_count == NO_WAVE)
		{
			ret |= RIGHT_TOP;
		}
		break;
	case LEFT_TOP_POINT:
		ret |= LEFT_TOP;
		break;
	case LEFT_BOTTOM_POINT:
		ret |= LEFT_BOTTOM;
		break;
	case RIGHT_TOP_POINT:
		ret |= RIGHT_TOP;
		break;
	case RIGHT_BOTTOM_POINT:
		ret |= RIGHT_BOTTOM;
		break;
	default:
		break;
	}

	return ret;
}

/*角点链表在最后插入角点*/
void pushPt(ConBlockData *data, Pt inPt)
{
	PtNode *ptnode = (PtNode *)malloc(sizeof(PtNode));
	ptnode->next = ptnode->prev = NULL;
	ptnode->pt.x = inPt.x;
	ptnode->pt.y = inPt.y;

	if ((data->min_y_x.x > inPt.x) || ((data->min_y_x.x == inPt.x) && (data->min_y_x.y > inPt.y)))
	{
		data->min_y_x.y = inPt.y;
		data->min_y_x.x = inPt.x;
	}
	if ((data->max_y_x.x < inPt.x) || ((data->max_y_x.x == inPt.x) && (data->max_y_x.y < inPt.y)))
	{
		data->max_y_x.y = inPt.y;
		data->max_y_x.x = inPt.x;
	}
	if (data->head == NULL)
	{
		data->head = data->end = ptnode;
	}
	else
	{
		data->end->next = ptnode;
		ptnode->prev = data->end;
		data->end = ptnode;
	}
	data->total++;
}

/*角点链表删除当前角点*/
void deletePt(ConBlockData *data, PtNode *ptnode)
{
	PtNode *p = ptnode;
	if (ptnode->prev == NULL)
	{
		data->head = ptnode->next;
		ptnode->next->prev = NULL;
	}
	else if (ptnode->next == NULL)
	{
		data->end = ptnode->prev;
		ptnode->prev->next = NULL;
	}
	else
	{
		ptnode->prev->next = ptnode->next;
		ptnode->next->prev = ptnode->prev;
	}
	free(p);
	data->total--;
}

/*释放角点链表*/
void releasePt(ConBlockData *data)
{
	PtNode *p = NULL;
	while (data->head)
	{
		p = data->head;
		data->head = data->head->next;
		free(p);
	}
	data->end = data->head = NULL;
}

/*按顺时针插入节点*/
void sortPushPt(PtNode **p_head, PtNode *p_cur, int isup, int count)
{
	PtNode *p = NULL;
	PtNode *ptnode = (PtNode *)malloc(sizeof(PtNode));
	ptnode->next = NULL;
	ptnode->prev = NULL;
	ptnode->pt.x = p_cur->pt.x;
	ptnode->pt.y = p_cur->pt.y;

	if (isup == UP_PORT)
	{
		if (*p_head == NULL)
		{
			*p_head = ptnode;
		}
		else
		{
			p = *p_head;
			while (count--)
			{
				//if ((ptnode->pt.y) < (p->pt.y))
				if ((ptnode->pt.x) < (p->pt.x))
				{
					if (p->prev == NULL)
					{
						*p_head = ptnode;
						p->prev = ptnode;
						ptnode->next = p;
					}
					else
					{
						p->prev->next = ptnode;
						ptnode->next = p;
						ptnode->prev = p->prev;
						p->prev = ptnode;
					}
					break;
				}
				//else if ((ptnode->pt.y) == (p->pt.y))
				else if ((ptnode->pt.x) == (p->pt.x))
				{
					//if ((ptnode->pt.x) > (p->pt.x))
					if ((ptnode->pt.y) > (p->pt.y))
					{
						if (p->next != NULL)
						{
							p = p->next;
						}
						else
						{
							p->next = ptnode;
							ptnode->prev = p;
							break;
						}
					}
					else
					{
						if (p->prev == NULL)
						{
							*p_head = ptnode;
							ptnode->next = p;
							p->prev = ptnode;
						}
						else
						{
							p->prev->next = ptnode;
							ptnode->next = p;
							ptnode->prev = p->prev;
							p->prev = ptnode;
						}
						break;
					}
				}
				else
				{
					if (p->next == NULL)
					{
						p->next = ptnode;
						ptnode->prev = p;
						break;
					}
					else
					{
						p = p->next;
					}
				}
			}
		}

	}
	else
	{
		if (*p_head == NULL)
		{
			*p_head = ptnode;
		}
		else
		{
			p = *p_head;
			while (count--)
			{
				//if ((ptnode->pt.y) > (p->pt.y))
				if ((ptnode->pt.x) > (p->pt.x))
				{
					if (p->prev == NULL)
					{
						*p_head = ptnode;
						p->prev = ptnode;
						ptnode->next = p;
					}
					else
					{
						p->prev->next = ptnode;
						ptnode->next = p;
						ptnode->prev = p->prev;
						p->prev = ptnode;
					}
					break;
				}
				//else if ((ptnode->pt.y) == (p->pt.y))
				else if ((ptnode->pt.x) == (p->pt.x))
				{
					//if ((ptnode->pt.x) < (p->pt.x))
					if ((ptnode->pt.y) < (p->pt.y))
					{
						if (p->next != NULL)
						{
							p = p->next;
						}
						else
						{
							p->next = ptnode;
							ptnode->prev = p;
							break;
						}
					}
					else
					{
						if (p->prev == NULL)
						{
							*p_head = ptnode;
							ptnode->next = p;
							p->prev = ptnode;
						}
						else
						{
							p->prev->next = ptnode;
							ptnode->next = p;
							ptnode->prev = p->prev;
							p->prev = ptnode;
						}
						break;
					}
				}
				else
				{
					if (p->next == NULL)
					{
						p->next = ptnode;
						ptnode->prev = p;
						break;
					}
					else
					{
						p = p->next;
					}
				}
			}
		}
	}
}

/*判断点在直线上方还是下方*/
int ptUporDown(Pt pt, double k, double b, int flag, int yy)
{
	int y = 0;
	if (flag == VERTICAL)
	{
		y = yy;
		if (pt.y > y)
			return UP_PORT;
		return DOWN_PORT;
	}
	else
	{
		y = k * pt.x + b;
		if ((pt.y - y) <= 0)
			return DOWN_PORT;
		return UP_PORT;
	}

}
/*角点链表顺时针排序*/
PtNode *sortPt(ConBlockData *data)
{
	int i = 0, up_count = 0, down_count = 0;
	double k = 0, b = 0;
	int x = 0, y = 0, yy = 0, flag = 0;
	int ret = 0;
	PtNode *p = NULL;
	PtNode *p_head = NULL;
	PtNode *p_middle = NULL;

	p = data->head;

	x = data->min_y_x.x - data->max_y_x.x;
	y = data->min_y_x.y - data->max_y_x.y;

	if (y == 0)
	{
		yy = cur_block_count.min_y_x.y;
		flag = VERTICAL;
	}
	else
	{
		k = (double)y / x;
		b = cur_block_count.min_y_x.y - (k * cur_block_count.min_y_x.x);
		flag = NOT_VERTICAL;
	}

	for (i = 0; i < data->total; i++)
	{
		ret = ptUporDown(p->pt, k, b, flag, yy);
		if (ret)
		{
			//在上半部分找位置插入
			sortPushPt(&p_head, p, UP_PORT, up_count);
			up_count++;
		}
		else
		{
			//在下半部分找位置插入
			sortPushPt(&p_middle, p, DOWN_PORT, down_count);
			down_count++;
		}
		p = p->next;
	}

	if (p_head != NULL)
	{
		p = p_head;
		for (i = 0; i < (up_count - 1); i++)
		{
			p = p->next;
		}
		p->next = p_middle;
		if (p_middle != NULL)
			p_middle->prev = p;
		return p_head;
	}
	else
		return p_middle;
}

/*判断3点是否共线*/
int isPointColinear(Pt pt1, Pt pt2, Pt pt3)
{
	return(pt2.x - pt1.x)*(pt3.y - pt1.y) - (pt2.y - pt1.y)*(pt3.x - pt1.x);
}

/*筛选多边形凸点*/
void getConvexPoint(ConBlockData *data)
{
	int x1 = 0, y1 = 0, x2 = 0, y2 = 0, i = data->total;
	int ans = 0;
	PtNode *p = data->head;
	//PtNode *p_prev = p->prev;
	PtNode *p_prev = data->end;
	PtNode *p_next = p->next;
	while (i--)
	{
		if (data->total < 3)
			return;

		x1 = p_next->pt.x - p_prev->pt.x;
		x1 = CONVEXPOINT(x1);
		y1 = p->pt.y - p_prev->pt.y;
		y1 = CONVEXPOINT(y1);
		x2 = p->pt.x - p_prev->pt.x;
		x2 = CONVEXPOINT(x2);
		y2 = p_next->pt.y - p_prev->pt.y;
		y2 = CONVEXPOINT(y2);
		if (!(isPointColinear(p_prev->pt, p->pt, p_next->pt)))
		{
			ans = -1;
		}
		else
		{
			ans = (x1*y1) - (x2*y2);
		}


		if (ans > 0)
		{
			if (p->next == NULL)
			{
				p = data->head;
				p_prev = data->end;
				p_next = p->next;
			}
			else
			{
				p = p->next;
				p_prev = p->prev;
				if (p->next == NULL)
				{
					p_next = data->head;
				}
				else
				{
					p_next = p->next;
				}
			}
		}
		else
		{
			deletePt(data, p);
			p = p_prev;
			if (p->prev == NULL)
			{
				p_prev = data->end;
			}
			else
			{
				p_prev = p->prev;
			}
			if (p->next == NULL)
			{
				p_next = data->head;
			}
			else
			{
				p_next = p->next;
			}
			i++;
		}
	}
}

/*寻找有波形块的连通域*/
void SingleBLock(CheckWave indata[BLOCK_COUNT][BLOCK_COUNT], int x, int y)
{
	//cout << "(" << x << ", " << y << ")" << endl;
	unsigned char ret = 0;
	Pt retPt = { 0 };
	indata[y][x].con_flag = WAVE_FLAGED;
	cur_block_count.block_count += 1;

	ret = isAngularPoint(indata, x, y);
	if (ret != 0)
	{
		retPt = getPointFromAngular(ret, x, y);
		pushPt(&cur_block_count, retPt);

	}

	//扫描右
	if ((x + 1 <= BLOCK_W))
	{
		if ((indata[y][x + 1].con_flag == NOT_FLAG) && (indata[y][x + 1].wave_count != NO_WAVE))
			SingleBLock(indata, (x + 1), y);
	}
	//扫描下
	if ((y + 1 <= BLOCK_H))
	{
		if ((indata[y + 1][x].con_flag == NOT_FLAG) && (indata[y + 1][x].wave_count != NO_WAVE))
			SingleBLock(indata, x, (y + 1));
	}
	//扫描左
	if ((x - 1 >= 0))
	{
		if ((indata[y][x - 1].con_flag == NOT_FLAG) && (indata[y][x - 1].wave_count != NO_WAVE))
			SingleBLock(indata, (x - 1), y);
	}
	//扫描上
	if ((y - 1 >= 0))
	{
		if ((indata[y - 1][x].con_flag == NOT_FLAG) && (indata[y - 1][x].wave_count != NO_WAVE))
			SingleBLock(indata, x, (y - 1));
	}

}

//进栈函数
void stack_postpush(Stack *stack, unsigned char con_flag, unsigned char x,unsigned char y, unsigned char tag)
{
	SNode *sdata = stack->cur;
	sdata->con_flag = con_flag;
	sdata->x = x;
	sdata->y = y;
	sdata->tag = tag;

	stack->cur++;
	stack->top++;
}

//弹栈函数
void stack_pop(Stack *stack, SNode *sdata)
{
	if (stack->top == -1)
	{
		return;
	}
	stack->cur--;

	sdata->con_flag = stack->cur->con_flag;
	sdata->tag = stack->cur->tag;
	sdata->x = stack->cur->x;
	sdata->y = stack->cur->y;

	stack->top--;
}

/*寻找有波形块的连通域  递归转非递归写法  动态申请内存模拟栈*/
void SingleBLock_stack(CheckWave data[BLOCK_COUNT][BLOCK_COUNT], int x, int y, SNode* count)
{
	Stack a = { 0 };
	//a.a = (SNode*)malloc(3072 * sizeof(SNode));
	a.a = count;
	a.cur = a.a;
	int p = 0, xx = 0, yy = 0;
	unsigned char tag = 0, ret = 0;
	a.top = -1;
	xx = x;
	yy = y;
	p = data[yy][xx].wave_count;
	SNode sdata = { 0 };
	Pt retPt = { 0 };
	int max_count = 0, cur_count = 0;
	while (p || a.top != -1)
	{
		while (p)
		{
			//为该结点入栈做准备
			//sdata.wave_count = p;
			
			cur_count += 1;
			if(max_count < cur_count)
			{
				max_count = cur_count;
			}
			ret = isAngularPoint(data, xx, yy);
			if (ret != 0)
			{
				retPt = getPointFromAngular(ret, xx, yy);
				pushPt(&cur_block_count, retPt);
			}

			data[yy][xx].con_flag = 1;
			stack_postpush(&a, 1, xx, yy, 0x01);//压栈   往右边遍历，设置右边已遍历标志位为0x01
			if (xx + 1 < BLOCK_W)
			{
				if (data[yy][xx + 1].wave_count != 0 && data[yy][xx + 1].con_flag != 1)
				{
					xx += 1;
					p = data[yy][xx].wave_count; //以该结点为根结点，遍历右边
				}
				else
				{
					p = 0;
				}
			}
			else
			{
				p = 0;
			}
		}
		//ii--;
		stack_pop(&a, &sdata);//栈顶元素弹栈  取栈顶元素
		cur_count -= 1;
		if(max_count < cur_count)
		{
			max_count = cur_count;
		}
		xx = sdata.x;
		yy = sdata.y;
		//p = data[yy][xx].wave_count;
		tag = sdata.tag;
		if (!(tag & 0x01))
		{
			//右边需要遍历
			//sdata.wave_count = p;
			cur_count += 1;
			if(max_count < cur_count)
			{
				max_count = cur_count;
			}
			sdata.con_flag = 1;
			data[yy][xx].con_flag = 1;
			sdata.x = xx;
			sdata.y = yy;
			sdata.tag = tag | 0x01;//往右边遍历，设置右边已遍历标志位为0x01
			stack_postpush(&a, sdata.con_flag, sdata.x, sdata.y, sdata.tag);//压栈
			if (xx + 1 < BLOCK_W)
			{
				if (data[yy][xx + 1].wave_count != 0 && data[yy][xx + 1].con_flag != 1)
				{
					xx += 1;
					p = data[yy][xx].wave_count; //以该结点为根结点，遍历右边
				}
				else
				{
					p = 0;
				}
			}
			else
			{
				p = 0;
			}
		}
		else if (!(tag & 0x02))
		{
			//下边需要遍历
			//sdata.wave_count = p;
			cur_count += 1;
			if(max_count < cur_count)
			{
				max_count = cur_count;
			}
			sdata.con_flag = 1;
			data[yy][xx].con_flag = 1;
			sdata.x = xx;
			sdata.y = yy;
			sdata.tag = tag | 0x02;//往下边遍历，设置下边已遍历标志位为0x02
			stack_postpush(&a, sdata.con_flag, sdata.x, sdata.y, sdata.tag);//压栈
			if (yy + 1 < BLOCK_H)
			{
				if (data[yy + 1][xx].wave_count != 0 && data[yy + 1][xx].con_flag != 1)
				{
					yy += 1;
					p = data[yy][xx].wave_count; //以该结点为根结点，遍历下边
				}
				else
				{
					p = 0;
				}

			}
			else
			{
				p = 0;
			}
		}
		else if (!(tag & 0x04))
		{
			//左边需要遍历
			//sdata.wave_count = p;
			cur_count += 1;
			if(max_count < cur_count)
			{
				max_count = cur_count;
			}
			sdata.con_flag = 1;
			data[yy][xx].con_flag = 1;
			sdata.x = xx;
			sdata.y = yy;
			sdata.tag = tag | 0x04;//往左边遍历，设置左边已遍历标志位为0x04
			stack_postpush(&a, sdata.con_flag, sdata.x, sdata.y, sdata.tag);//压栈
			if (xx - 1 > 0)
			{

				if (data[yy][xx - 1].wave_count != 0 && data[yy][xx - 1].con_flag != 1)
				{
					xx -= 1;
					p = data[yy][xx].wave_count; //以该结点为根结点，遍历左边
				}
				else
				{
					p = 0;
				}
			}
			else
			{
				p = 0;
			}
		}
		else if (!(tag & 0x08))
		{
			//上边边需要遍历
			//sdata.wave_count = p;
			cur_count += 1;
			if(max_count < cur_count)
			{
				max_count = cur_count;
			}
			sdata.con_flag = 1;
			data[yy][xx].con_flag = 1;
			sdata.x = xx;
			sdata.y = yy;
			sdata.tag = tag | 0x08;//往上边遍历，设置上边已遍历标志位为0x08
			stack_postpush(&a, sdata.con_flag, sdata.x, sdata.y, sdata.tag);//压栈
			if (yy - 1 > 0)
			{
				if (data[yy - 1][xx].wave_count != 0 && data[yy - 1][xx].con_flag != 1)
				{
					yy -= 1;
					p = data[yy][xx].wave_count; //以该结点为根结点，遍历上边
				}
				else
				{
					p = 0;
				}
			}
			else
			{
				p = 0;
			}
		}
		else
		{
			//遍历完4个方向
			p = 0;
		}
	}
	//free(a.a);
	cur_block_count.block_count = max_count;
	//DECODE_LOG("MAX_COUNT = %d", max_count);
}

/*无效边缘过滤*/
void CannyFilte_x(CheckWave indata[BLOCK_COUNT][BLOCK_COUNT], int count)
{
	int flag = 1, i = 0;
	int x = 0, y = 0;
	int x1 = 0;
	for (y = 0; y < BLOCK_H; y++)
	{
		for (x = 0; x < BLOCK_W; x++)
		{
			if (indata[y][x].wave_count == 0 && (x + count)< BLOCK_W)
			{
				x1 = x + 1;
				if (indata[y][x1].wave_count == 0)
					continue;
				for (i = 0; i < count; i++)
				{
					if (flag == 1)
					{
						if (indata[y][x1].wave_count > 0)
						{
							x1 += 1;
						}
						else
						{
							x1 += 1;
							flag = 0;
							i -= 1;
						}
					}
					else
					{
						if (x1 < BLOCK_W)
						{
							if (indata[y][x1].wave_count > 0)
							{
								x1 -= 1;
								indata[y][x1].wave_count = 1;
							}
							else
							{
								x1 = x + 1;
								for (i = 0; i < count; i++)
								{
									indata[y][x1].wave_count = 0;
									x1 += 1;
								}
							}
						}
						
					}
				}
				flag = 1;
			}
		}
	}
}

void CannyFilte_y(CheckWave indata[BLOCK_COUNT][BLOCK_COUNT], int count)
{
	int flag = 1, i = 0;
	int x = 0, y = 0;
	int y1 = 0;
	for (y = 0; y < BLOCK_H; y++)
	{
		for (x = 0; x < BLOCK_W; x++)
		{
			if (indata[y][x].wave_count == 0 && (y + count) < BLOCK_H)
			{
				y1 = y + 1;
				if (indata[y1][x].wave_count == 0)
					continue;
				for (i = 0; i < count; i++)
				{
					if (flag == 1)
					{
						if (indata[y1][x].wave_count > 0)
						{
							y1 += 1;
						}
						else
						{
							y1 += 1;
							flag = 0;
							i -= 1;
						}
					}
					else
					{
						if (y1 < BLOCK_H)
						{
							if (indata[y1][x].wave_count > 0)
							{
								y1 -= 1;
								indata[y1][x].wave_count = 1;
							}
							else
							{
								y1 = y + 1;
								for (i = 0; i < count; i++)
								{
									indata[y1][x].wave_count = 0;
									y1 += 1;
								}
							}
						}

					}
				}
				flag = 1;
			}
		}
	}
}

/*二维数组噪声波形块滤波*/
void imgFilte(CheckWave indata[BLOCK_COUNT][BLOCK_COUNT])
{
	int h = 0, w = 0;
	for (h = 0; h < BLOCK_H; h++)
	{
		for (w = 0; w < BLOCK_W; w++)
		{
			if (h == 0)
			{
				indata[h][w].wave_count = 0;
			}
			else if (h == (BLOCK_H - 1))
			{
				indata[h][w].wave_count = 0;
			}
			else
			{
				if (w == 0)
				{
					indata[h][w].wave_count = 0;
				}
				else if (w == (BLOCK_W - 1))
				{
					indata[h][w].wave_count = 0;
				}
				else
				{
					//if ((indata[h][w - 1].wave_count == 0 && indata[h][w + 1].wave_count == 0) || (indata[h - 1][w].wave_count == 0 && indata[h + 1][w].wave_count == 0))
					if ((indata[h][w - 1].wave_count == 0 && indata[h][w + 1].wave_count == 0) || (indata[h - 1][w].wave_count == 0 && indata[h + 1][w].wave_count == 0) || indata[h][w].wave_count < 1)
					{
						indata[h][w].wave_count = 0;
					}
					if ((indata[h][w - 1].wave_count != 0 && indata[h][w + 1].wave_count != 0) || (indata[h - 1][w].wave_count != 0 && indata[h + 1][w].wave_count != 0))
					{
						indata[h][w].wave_count = 1;
					}
					/*if ((indata[h - 1][w - 1].wave_count == 0 && indata[h + 1][w + 1].wave_count == 0) || (indata[h - 1][w + 1].wave_count == 0 && indata[h + 1][w - 1].wave_count == 0) || indata[h][w].wave_count < 1)
					{
						indata[h][w].wave_count = 0;
					}*/
				}
			}
		}
	}
}

/*排除贴边连通域，使用后码不可再贴边，需要留出至少5像素的边*/
unsigned char checkBlockCanny()
{
	PtNode *p = NULL;
	int i = 0, x = 0, y = 0;
	p = cur_block_count.head;

	for (i = 0; i < (cur_block_count.total); i++)
	{
		x = p->pt.x / BLOCK_SIZE;
		y = p->pt.y / BLOCK_SIZE;

		if (x == 0 || x == (BLOCK_W - 1) || y == 0 || y == (BLOCK_H - 1))
			return false;
		p = p->next;
	}
	return true;
}


/*查找所有连通块*/
AreaData CheckMaxbBlock(CheckWave indata[BLOCK_COUNT][BLOCK_COUNT], int count)
{
	int h = 0, w = 0, i = 0;
	PtNode *p;
	AreaData cur_area = { 0 };
	AreaData max_area = { 0 };
	imgFilte(indata);
	imgFilte(indata);
	CannyFilte_x(indata, 4);
	CannyFilte_y(indata, 4);
	SNode *stack_count = (SNode*)malloc(BLOCK_H * BLOCK_W * sizeof(SNode));
	for (h = 0; h < BLOCK_H; h++)
	{
		for (w = 0; w < BLOCK_W; w++)
		{
			if (indata[h][w].wave_count != NO_WAVE && indata[h][w].con_flag == NOT_FLAG)
			{
				cur_block_count.max_y_x.y = cur_block_count.min_y_x.y = h * BLOCK_SIZE;
				cur_block_count.max_y_x.x = cur_block_count.min_y_x.x = w * BLOCK_SIZE;

				//SingleBLock(indata, w, h);
				SingleBLock_stack(indata, w, h, stack_count);
				// if(!checkBlockCanny())
				// {
				// 	releasePt(&cur_block_count);
				// 	cur_block_count.total = 0;
				// 	cur_block_count.max_y_x.y = cur_block_count.min_y_x.y = 0;
				// 	cur_block_count.max_y_x.x = cur_block_count.min_y_x.x = 0;
				// 	cur_block_count.block_count = 0;
				// 	continue;
				// }
				p = sortPt(&cur_block_count);
				releasePt(&cur_block_count);
				cur_block_count.head = p;
				while (p->next)
				{
					p = p->next;
				}
				cur_block_count.end = p;

				getConvexPoint(&cur_block_count);
				cur_area = getAreaPercent(cur_block_count);

				// DECODE_LOG("area percent %d", cur_area.area_percent);
				// DECODE_LOG("area length %d", cur_area.length);
				// DECODE_LOG("area wide %d", cur_area.wide);

				releasePt(&cur_block_count);
				cur_block_count.total = 0;
				cur_block_count.max_y_x.y = cur_block_count.min_y_x.y = 0;
				cur_block_count.max_y_x.x = cur_block_count.min_y_x.x = 0;
				//percent = get4PointAreaPercent(cur_block_count);
			

				if (cur_area.area_percent >= AREA_PERCENT_MIN)
				{
					if (max_area.block_count < cur_area.block_count)
					{
						max_area.block_count = cur_area.block_count;
						max_area.area_percent = cur_area.area_percent;
						max_area.degree = cur_area.degree;
						max_area.length = cur_area.length;
						max_area.wide = cur_area.wide;
						for (i = 0; i < 4; i++)
						{
							max_area.pt[i].x = cur_area.pt[i].x;
							max_area.pt[i].y = cur_area.pt[i].y;
						}
						
					}
				}
			}
			cur_block_count.block_count = 0;
		}
	}
	free(stack_count);
	return max_area;
}

/*整图拆块*/
//CheckWave *ImgCheckCode(unsigned char *indata, int width, int height, int scan_size, int waveform_check)
AreaData ImgCheckCode(unsigned char *indata, int width, int height, int scan_size, int waveform_check)
{
	int h = 0, w = 0, scan_h = 0, scan_w = 0;
	unsigned char *scan_block = (unsigned char *)malloc(scan_size * scan_size);
	int ret = 0, count = 0;
	int h_flag = 0, w_flag = 0;
	CheckWave ImgWaveCheckRet[BLOCK_COUNT][BLOCK_COUNT] = { 0 };
	AreaData maxblockdata = { 0 };

	IMG_H = height;
	IMG_W = width;
	BLOCK_H = (IMG_H / BLOCK_SIZE);
	BLOCK_W = (IMG_W / BLOCK_SIZE);
	for (h = 0; h < height; h += scan_size)
	{
		for (w = 0; w < width; w += scan_size)//
		{
			for (scan_h = 0; scan_h < scan_size; scan_h++)
			{
				for (scan_w = 0; scan_w < scan_size; scan_w++)
				{
					scan_block[scan_size * scan_h + scan_w] = indata[width * (h + scan_h) + w + scan_w];

				}
			}
			ret = SingleBlockCheckWave(scan_block, scan_size, waveform_check);
			if(ret > 0)
				count++;
			ImgWaveCheckRet[h_flag][w_flag].wave_count = ret;
			w_flag++;
		}
		w_flag = 0;
		h_flag++;
	}


	maxblockdata = CheckMaxbBlock(ImgWaveCheckRet, count);
	

	free(scan_block);
	return maxblockdata;
}
