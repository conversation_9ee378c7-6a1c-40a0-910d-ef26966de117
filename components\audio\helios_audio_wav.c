#include <stdio.h>
#include <stdlib.h>
#include <assert.h>

#include "helios_os.h"
#include "helios_audio_py.h"
#include "watermark.h"

#include "helios_include.h"
#include "helios_audio_fs.h"
#include "helios_audio_wav.h"





//////////////////////////////////////
typedef enum{
	WAV_PLAY_STOP,
	WAV_PLAY_SENDING_MESSAGE,
	WAV_PLAY_PLAYING,
	WAV_PLAY_STOPPING,
}WAV_PLAY_STATUS;

static cb_on_player aud_play_cb = NULL;


static PCM_HANDLE_T wav_pcm_whdl = NULL;


int is_wav_file_playing = WAV_PLAY_STOP;
static char wav_file_name[256];
static void* wav_file_stack_ptr = NULL;
static Helios_Sem_t wav_file_play_Sema;
static Helios_OSFlag_t wav_file_pcm_flag_Ref = 0;
static Helios_Thread_t wav_file_task_Ref = 0;
static int wav_file_play_mode = 0;
static int wav_file_pcm_pingpong_index = 0;
static unsigned short wav_file_pcm_pingpong_buffer[48*20*2*2]; // max 48K 20ms stereo * 2
static ring_buffer_handler_t wav_ring_buff = NULL;
static int file_length = 0;

#define WAV_FILE_PCM_TASK_CONTINUE   (0x01)
#define WAV_FILE_PCM_STOP   (0x02)
#define WAV_FILE_PCM_TASK_MASK       (WAV_FILE_PCM_STOP | WAV_FILE_PCM_TASK_CONTINUE)

enum{
	WAV_FILE_MODE,
	WAV_STREAM_MODE,
}WAV_PLAY_MODE;
static ring_buffer_handler_t wav_file_Ringbuf;


#define HELIOS_PCM_BLOCK_FLAG (0x01)
#define HELIOS_PCM_NONBLOCK_FLAG (0x02)
#define HELIOS_PCM_READ_FLAG (0x04)
#define HELIOS_PCM_WRITE_FLAG (0x08)

#define Helios_PCM_TASK_CONTINUE (0x01)
#define Helios_PCM_STOP (0x02)
#define Helios_PCM_TASK_MASK (Helios_PCM_STOP | Helios_PCM_TASK_CONTINUE)


#define WAV_FILE_RINGBUF_SIZE (30*1024)
//////////////////////////////////////


static int Helios_play_stream_start(unsigned int rate, unsigned int ch, unsigned short *pcm_buf, unsigned int frameByteSize)
{
	int ret = 0;
	if (wav_pcm_whdl == NULL)
	{
#ifdef CONFIG_AUDIO_PWM
		wav_pcm_whdl = Helios_Aud_PWM_Open(CONFIG_AUDIO_PWM_PIN, 1, rate, HELIOS_PCM_WRITE_FLAG | HELIOS_PCM_BLOCK_FLAG);
#else
		wav_pcm_whdl = Helios_PCM_Open(ch, rate, HELIOS_PCM_WRITE_FLAG | HELIOS_PCM_BLOCK_FLAG);
#endif
	}

	if (wav_pcm_whdl == NULL)
	{
		return -1;
	}
#ifdef CONFIG_AUDIO_PWM
	ret = Helios_Aud_PWM_Write(wav_pcm_whdl, pcm_buf, frameByteSize);
#else
	ret = Helios_PCM_Write(wav_pcm_whdl, pcm_buf, frameByteSize);
#endif
	if (ret < 0)
	{
		AUDLOGE("kOutputBufferSize[%d] ret[%d]\n", frameByteSize, ret);
		return -1;
	}
	Helios_Flag_Release(wav_file_pcm_flag_Ref, Helios_PCM_TASK_CONTINUE, Helios_FLAG_OR);
	

	return 0;
}



static void wav_file_pcm_stop(void)
{
	if (wav_pcm_whdl)
	{
#ifdef CONFIG_AUDIO_PWM
		Helios_Aud_PWM_Close(wav_pcm_whdl);
#else
		Helios_PCM_Close(wav_pcm_whdl);
#endif
		wav_pcm_whdl = NULL;
	}
}


#ifndef min
#define min(a, b) ((a) < (b) ? (a) : (b))
#endif
static uint32_t get_wav_ringbuf_free_size(void) {
	uint32_t freesize = 0;
	quec_ring_buffer_get_free_size(wav_file_Ringbuf, &freesize);
	return freesize;
}
static uint32_t get_wav_ringbuf_data_size(void) {
	uint32_t dataize = 0;
	if(wav_file_play_mode == WAV_FILE_MODE)
		quec_ring_buffer_get_used_size(wav_file_Ringbuf, &dataize);
	else
		quec_ring_buffer_get_used_size(wav_ring_buff, &dataize);
	return dataize;
}

struct riff_chunk{
	unsigned int riff;
	unsigned int file_size;
	unsigned int wave;
};

struct format_chunk{
	unsigned int format;
	unsigned int format_size;
	unsigned short aud_fmt;
	unsigned short channel;
	unsigned int sample;
	unsigned int bytes;
	unsigned short block_align;
	unsigned short bits_sample;
	unsigned short others;
};
struct data_chunk{
	unsigned int data;
	unsigned int data_size;
};

#define ID_RIFF 	0x46464952
#define ID_WAVE		0x45564157
#define ID_FMT  	0x20746d66
#define ID_DATA 	0x61746164
#define ID_IOS		0x524c4c46//wav data id by IOS
#define FORMAT_PCM 	1

static bool is_support_samplerate(unsigned int rate)
{
	switch(rate) {
	case 8000:
	case 11025:
	case 12000:
	case 16000:
	case 22050:
	case 24000:
	case 32000:
	case 44100:
	case 48000:
		return true;
	}
	return false;
}


static int check_wav_header(unsigned int temp, HeliosAudFILE *fd, ring_buffer_handler_t *wav_ring_buff, int *sample, int *channel){
	struct riff_chunk riff_hdr;
	struct format_chunk format_hdr;
	struct data_chunk data_hdr;
	int ret = 0, i = 0;
	unsigned int chunk_id = 0,chunk_size = 0,chunk_len = 0;
	unsigned int data = 0,data_size = 0;
	if((fd == NULL && temp == 0) || (wav_ring_buff == NULL && temp == 1)){
		return -1;
	}
	if(temp == 0){
		ret = Helios_Aud_fseek(fd, 0, SEEK_SET);
		if(ret < 0)
		{
			return -1;
		}
		ret = Helios_Aud_fread(&riff_hdr, sizeof(riff_hdr), 1, fd);							//riff
		if(ret < sizeof(riff_hdr))
		{
			Helios_Aud_fseek(fd, 0, SEEK_SET);
			return -1;
		}
	}else if(temp == 1){
		quec_ring_buffer_read_ex(wav_ring_buff, &riff_hdr, sizeof(riff_hdr), &ret);
		if(ret < sizeof(riff_hdr))
			return -1;
	}
	if( (riff_hdr.riff != ID_RIFF) || (riff_hdr.wave!= ID_WAVE))
	{
		return -1;
	}

	for(i = 0;i < riff_hdr.file_size;){										//format
		if(temp == 0)
			ret = Helios_Aud_fread(&chunk_id, sizeof(chunk_id), 1, fd);
		else if(temp == 1)
			quec_ring_buffer_read_ex(wav_ring_buff, &chunk_id, sizeof(chunk_id), &ret);
		if(chunk_id == ID_FMT){
			if(temp == 0)
				ret = Helios_Aud_fread(&format_hdr.format_size, sizeof(unsigned int), 1, fd);
			else if(temp == 1)
				quec_ring_buffer_read_ex(wav_ring_buff, &format_hdr.format_size, sizeof(unsigned int), &ret);
			format_hdr.format = chunk_id;
			break;
		}else{
			if(temp == 0)
				ret = Helios_Aud_fread(&chunk_size, sizeof(chunk_size), 1, fd);
			else if(temp == 1)
				quec_ring_buffer_read_ex(wav_ring_buff, &chunk_size, sizeof(chunk_size), &ret);	
			i += (chunk_size + 8);
			if(i >= riff_hdr.file_size)
				return -1;
			if(temp == 0)
				Helios_Aud_fseek(fd, chunk_size, 1);
			else if(temp == 1)
				quec_ring_buffer_jump(wav_ring_buff, chunk_size);
			chunk_len = i;
		}
	}
	
	if(temp == 0)
		ret = Helios_Aud_fread(&format_hdr.aud_fmt, format_hdr.format_size, 1, fd);
	else if(temp == 1)
		quec_ring_buffer_read_ex(wav_ring_buff, &format_hdr.aud_fmt, format_hdr.format_size, &ret);
	if(is_support_samplerate(format_hdr.sample) == false){
		return -1;
	}


	for(i = 0;i < (riff_hdr.file_size-chunk_len-format_hdr.format_size);){			//data
		if(temp == 0)
			ret = Helios_Aud_fread(&data, sizeof(data), 1, fd);
		else if(temp == 1)
			quec_ring_buffer_read_ex(wav_ring_buff, &data, sizeof(data), &ret);
		if(data == ID_DATA){
			if(temp == 0)
				ret = Helios_Aud_fread(&data_hdr.data_size, sizeof(unsigned int), 1, fd);
			else if(temp == 1)
				quec_ring_buffer_read_ex(wav_ring_buff, &data_hdr.data_size, sizeof(unsigned int), &ret);
			data_hdr.data = data;
			break;
		}else{
			if(temp == 0)
				ret = Helios_Aud_fread(&data_size, sizeof(data_size), 1, fd);
			else if(temp == 1)
				quec_ring_buffer_read_ex(wav_ring_buff, &data_size, sizeof(data_size), &ret);	
			i += (data_size + 8);
			if(i >= riff_hdr.file_size-chunk_len-format_hdr.format_size)
				return -1;
			if(temp == 0)
				Helios_Aud_fseek(fd, data_size, 1);
			else if(temp == 1)
				quec_ring_buffer_jump(wav_ring_buff, data_size);
		}
	}
	*sample = format_hdr.sample;
	*channel = format_hdr.channel;
	AUDLOGI("samplerate %d,  channels %d, file_length %d\n",*sample, *channel, data_hdr.data_size);
	return data_hdr.data_size;
}
static int wav_file_play_func() 
{	
    int 					first_time 	= 1;
    unsigned int   			event 		= 0;  
	AUDIOHAL_ERR_T 			rc 			= AUDIOHAL_ERR_ENUM_32_BIT;    
	int 					retVal 		= -1;
	
	void*					tempBuf 	= NULL;
	
    int 					channels 	= 0;
    int 					samplerate 	= 0;
	uint32_t 				bytesRead 	= 0;
	bool 					isReadEnd = false;
	int						data = 0;
	HeliosAudFILE 					*fd = NULL;
    // Open the file.
    if(wav_file_play_mode == WAV_FILE_MODE){
    	fd = Helios_Aud_fopen(wav_file_name, "r");
		if(fd == NULL) {
			 AUDLOGI("%s:open file fail\r\n", __func__);
			 goto end;
		}
    }
	file_length =  check_wav_header(wav_file_play_mode, fd, wav_ring_buff, &samplerate, &channels);
	if(file_length < 0){
		AUDLOGI("check wav header error! \n");
		goto end;
	}
	if(wav_file_play_mode == WAV_FILE_MODE)
		tempBuf = malloc(WAV_FILE_RINGBUF_SIZE/2);
	
    while (1) {
		if(wav_file_play_mode == WAV_FILE_MODE){
			while (!isReadEnd && get_wav_ringbuf_free_size() >= WAV_FILE_RINGBUF_SIZE/2)  {
		        // Read input from the file.
		        uint32_t freesize = 0;
				freesize = get_wav_ringbuf_free_size();
				bytesRead = min(freesize, WAV_FILE_RINGBUF_SIZE/2);
		        retVal = Helios_Aud_fread(tempBuf, bytesRead, 1, fd);
				if (retVal <= 0) {
					isReadEnd = true;
					break;
				}
		        //quec_ring_buffer_write(wav_file_Ringbuf, tempBuf, retVal);
		        rc = Helios_play_stream_start(samplerate, channels, tempBuf, retVal);
			}
		}else
			isReadEnd = true;
        
        Helios_Flag_Wait(wav_file_pcm_flag_Ref, WAV_FILE_PCM_TASK_MASK, Helios_FLAG_OR_CLEAR, &event, HELIOS_WAIT_FOREVER); 
		if(isReadEnd){
			while((0 != get_wav_ringbuf_data_size()) 
					&& (WAV_FILE_PCM_STOP != (WAV_FILE_PCM_STOP & event))){
				Helios_Flag_Wait(wav_file_pcm_flag_Ref, WAV_FILE_PCM_TASK_MASK, Helios_FLAG_OR_CLEAR, &event, HELIOS_WAIT_FOREVER); 
			}
		}

//		if(wav_file_play_mode && (get_wav_ringbuf_data_size() == 0)){
//			break;
//		}
			
        if(WAV_FILE_PCM_STOP == (WAV_FILE_PCM_STOP & event) || isReadEnd){
            break;
        }
		
    }
	
end:	
//	if(AUDIOHAL_ERR_NO == rc){
    	wav_file_pcm_stop();
//	}
    // Free allocated memory.
    if(tempBuf)	free(tempBuf);
	if(fd) {
		Helios_Aud_fclose(fd);
	}
	if(wav_file_play_mode == WAV_STREAM_MODE){
		quec_ring_buffer_deinit(&wav_ring_buff);
		wav_ring_buff = NULL;
	}else
    	quec_ring_buffer_reset(wav_file_Ringbuf);
	return retVal;
}


static int wav_file_play(void)
{
    int status;
	unsigned char first_entry = 0;
	while(1){

        if(0 != first_entry) {
			is_wav_file_playing = WAV_PLAY_STOP;
		}
		else {
			first_entry = !first_entry;
		}
		AUDLOGI("XXXXXX1\n");
        status = Helios_Semaphore_Acquire(wav_file_play_Sema, HELIOS_WAIT_FOREVER);
//        assert(status != 0); 
		AUDLOGI("XXXXXX2\n");

		if (aud_play_cb) {
			aud_play_cb(wav_file_name, 0, HELIOS_AUD_PLAYER_START);
		}
		
		if(WAV_PLAY_SENDING_MESSAGE == is_wav_file_playing) {
			is_wav_file_playing = WAV_PLAY_PLAYING;
			wav_file_play_func();
		}
		is_wav_file_playing = WAV_PLAY_STOPPING;
		
		if (aud_play_cb) {
			aud_play_cb(wav_file_name, 0, HELIOS_AUD_PLAYER_FINISHED);
		}	
	}
	return 0;
}


static void wav_file_play_task_init(void)
{
    int status;
    static int inited = 0;	
    size_t wav_stackSize = 1024*10;
    int wav_thread_priority = 90;	
		
    if(!inited){
		quec_ring_buffer_init(&wav_file_Ringbuf, WAV_FILE_RINGBUF_SIZE);
	
        wav_file_pcm_flag_Ref = Helios_Flag_Create();
        assert(wav_file_pcm_flag_Ref != 0);
		
        wav_file_stack_ptr = malloc(wav_stackSize);
		assert(wav_file_stack_ptr != NULL);

		
		Helios_ThreadAttr attr = {0};
		attr.name = "Helios_wav_stream_play";
		attr.stack_size = wav_stackSize;
		attr.priority = wav_thread_priority;
		attr.entry = wav_file_play;

		wav_file_task_Ref = Helios_Thread_Create(&attr);
		
        assert(wav_file_task_Ref != 0);       

        inited = 1;
    }
}

typedef unsigned char           BOOL;

static BOOL helios_find_file(char *name)
{
	HeliosAudFILE *fileID = NULL;

	fileID = Helios_Aud_fopen(name, "r");
	if (fileID)
	{
		Helios_Aud_fclose(fileID);

		return TRUE;
	}
	return FALSE;
}


int Helios_wav_file_start(char *file_name, cb_on_player aud_cb)
{
	OSA_STATUS status;
	uint32_t ret = 0;

	if(!file_name){
	    AUDLOGI("%s:file_name NULL\r\n", __func__);
		return -1;
	}

	AUDLOGI("%s:play file_name %s\r\n", __func__, file_name);
	
    if((0 == strlen(file_name))
		|| (strlen(file_name) > 250)
		|| (!helios_find_file(file_name))) {
		AUDLOGI("%s:file_name error!!\r\n", __func__);
        return -1;
    }

	if (aud_cb)
	{
		aud_play_cb = aud_cb;
	}

    if(WAV_PLAY_STOP != is_wav_file_playing) {
		AUDLOGI("%s:wav file is in playing\r\n", __func__);
        return -2;
    }
    is_wav_file_playing = WAV_PLAY_SENDING_MESSAGE;
    
	wav_file_play_mode = WAV_FILE_MODE;
	memset(wav_file_name, 0, sizeof(wav_file_name));
    memcpy(wav_file_name, file_name, strlen(file_name) + 1);
		
    if(wav_file_task_Ref == 0) {
		
		wav_file_play_Sema = Helios_Semaphore_Create(1, 0);
		assert(wav_file_play_Sema != 0);
		
        wav_file_play_task_init();

		
		Helios_Semaphore_Poll(wav_file_play_Sema, &ret);
		if (ret == 0)
		{
			Helios_Semaphore_Release(wav_file_play_Sema);
		}
    } else {
        Helios_Semaphore_Release(wav_file_play_Sema);
    }
    return 0;
}

int Helios_play_wav_stream_start(char *data, unsigned int size)
{
	OSA_STATUS status;

	if(data == NULL){
	    AUDLOGI("%s:data error\r\n", __func__);
		return -1;
	}
	
    if(WAV_PLAY_STOP != is_wav_file_playing) {
		AUDLOGI("%s:wav file is in playing\r\n", __func__);
        return -2;
    }
    is_wav_file_playing = WAV_PLAY_SENDING_MESSAGE;
	
	wav_file_play_mode = WAV_STREAM_MODE;

	quec_ring_buffer_init(&wav_ring_buff, size);
	quec_ring_buffer_write(wav_ring_buff, (uint8 *)data, size);
		
    if(wav_file_task_Ref == 0) {
		wav_file_play_Sema = Helios_Semaphore_Create(1, 0);
		assert(wav_file_play_Sema != 0);
		
        wav_file_play_task_init();
    } else {
        Helios_Semaphore_Release(wav_file_play_Sema);
    }
    return 0;
}

int Helios_wav_file_stop(void)
{  
    AUDLOGI("%s:stop\r\n", __func__);
    int count = 0;
    if(WAV_PLAY_STOP == is_wav_file_playing) {
		AUDLOGI("%s:error, wav is not playing\r\n", __func__);
        return -1;
    }

    if(WAV_PLAY_SENDING_MESSAGE == is_wav_file_playing) {
		is_wav_file_playing = WAV_PLAY_STOPPING;
    }
	
    Helios_Flag_Release(wav_file_pcm_flag_Ref, WAV_FILE_PCM_STOP, Helios_FLAG_OR);
	while ((WAV_PLAY_STOP != is_wav_file_playing) && count < 100) {
		Helios_msleep(15);
		count++;
	}
    return 0;
}

