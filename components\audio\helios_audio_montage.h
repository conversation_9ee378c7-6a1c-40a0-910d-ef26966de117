#ifndef __HELIOS_AUDIO_MONTAGE_H__
#define __HELIOS_AUDIO_MONTAGE_H__

#include "helios_audio_py.h"

typedef struct
{
    char *montage_name;
    char *file_name;
    Helios_AudPlayerType type;
    Helios_AudOutputType outputtype;
    helios_cb_on_player usr_cb;
} montage_play_elem;

typedef struct
{
    char file_name[128];
    uint32_t offset;
    uint32_t size;
} montage_elem;

typedef struct
{
    char is_used;
    char montage_name[128];
    uint32_t file_num;
    montage_elem *file_info_buf;
} montage_info;

enum
{
    PLAY_MOREFILES_ERR_OK = 0,
    PLAY_MOREFILES_ERR_SINGLEFILE = -1,
    PLAY_MOREFILES_ERR_FILENOTEXIST = -2,
    PLAY_MOREFILES_ERR_FILENFSFAIL = -3,
};

int Helios_Audio_MontageFilePlayStart(char *montage_name, char *file_name, Helios_AudPlayerType type, helios_cb_on_player usr_cb, uint8_t device);
int helios_set_montage_break_flag(char set);
#endif
