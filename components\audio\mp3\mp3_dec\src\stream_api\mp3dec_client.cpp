/*
 * Copyright (C) 2014 The Android Open Source Project
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *  * Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *  * Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

#include <stdlib.h>
#include <stdio.h>
#include <assert.h>
#include <string.h>
#include "gbl_types.h"
#include "pvmp3decoder_api.h"
#include "mp3reader.h"
#include "helios_include.h"
#include "osa_old_api.h"
#include "mp3dec_api.h"
#include "mp3dec_config.h"

#if MP3_STREAM_SUPPORT == 1 && MP3_STREAM_API_TEST == 1

extern "C"
{
static OSAFlagRef	dec_pcm_flag_Ref;
static OSATaskRef	mp3_Decoder 	= NULL;
static void*		mp3_stack_ptr	= NULL;

static Mp3Reader 				mp3Reader;
static tPVMP3DecoderExternal 	decoderConfig;
static void*					decoderBuf 	= NULL;
static uint8_t *				inputBuf 	= NULL;
static uint32_t				    bytesRead   = 0;
static int 						readBlockSize = 256;
static void(*mp3_read_compleletd)(void) 	= 0;

#define kInputBufferSize 			(2 * 1024)
#define MP3_DEC_PCM_READ_FRAME			(0x01)
#define MP3_DEC_PCM_READ_BUFFER			(0x02)

#define MP3_DEC_PCM_TASK_MASK       (MP3_DEC_PCM_READ_FRAME | MP3_DEC_PCM_READ_BUFFER)

static int parse_header = 0;
//ICAT EXPORTED FUNCTION - Audio,Voice,switch_stream_parse_header
void switch_stream_parse_header(void)
{
    parse_header ^= 1;
    //DIAG_FILTER(AUDIO, MP3_STREAM_TEST, switch_stream_parse_header, DIAG_INFORMATION)
    AUDLOGI("parse_header:%d!!!", parse_header);
}

static int mp3_dec_deinit(void){
	//DIAG_FILTER(AUDIO, MP3_DEC_CLIENT, mp3Reader_deinit, DIAG_INFORMATION)
    AUDLOGI("mp3Reader_deinit");
	// Close input reader and output writer.
	mp3Reader.close();

	// Free allocated memory.
	if(inputBuf)	
		free(inputBuf);
	if(decoderBuf)	
		free(decoderBuf);
	
	return 0;
}

static int mp3_dec_init(const char* name, Mp3Reader* reader, tPVMP3DecoderExternal* config){	
	bool 					success 	= false;
	uint32_t 				memRequirements = 0;
	uint32_t				sampleRate = 0;
	uint32_t				numChannel = 0;
	
	//DIAG_FILTER(AUDIO, MP3_DEC_CLIENT, mp3_dec_init_enter, DIAG_INFORMATION)
    AUDLOGI("%s, file name %s", __FUNCTION__, name);

	if(!reader || !config)
		return -1;

	config->equalizerType = flat;
	config->crcEnabled = false;
	
	// Allocate the decoder memory.
	memRequirements = pvmp3_decoderMemRequirements();
	decoderBuf = malloc(memRequirements);
	ASSERT(decoderBuf != NULL);

	// Initialize the decoder.
	pvmp3_InitDecoder(config, decoderBuf);

	// Open the input file.
    if(parse_header)
        success = reader->init(name);
    else
        success = reader->init(name, false);
	if (!success) {
		return -2;
	}

	// Allocate input buffer.
	inputBuf = static_cast<uint8_t*>(malloc(kInputBufferSize));
	ASSERT(inputBuf != NULL);

	sampleRate = reader->getSampleRate();
	numChannel = reader->getNumChannels();
	//DIAG_FILTER(AUDIO, MP3_DEC_CLIENT, mp3Reader_init, DIAG_INFORMATION)
	AUDLOGI("mp3Reader.init:%d, sampleRate:%ld, numChannel:%ld", success, sampleRate, numChannel);

	return 0;
}

static int mp3_dec_read(bool frame_mode){
	// Read input from the file.
	bool success = false;
	if(frame_mode && parse_header){
		success = mp3Reader.getFrame(inputBuf, &bytesRead);
	}
	else{
		success = mp3Reader.getBuffer(readBlockSize, inputBuf, &bytesRead);
	}
	
	//DIAG_FILTER(AUDIO, MP3_DEC_CLIENT, mp3_dec_read, DIAG_INFORMATION)
	AUDLOGI("mp3_read_compleletd:0x%lx,success:%d,inputBuf:0x%lx,bytesRead:%d", mp3_read_compleletd,success,inputBuf,bytesRead);
	if (!success) {
		//DIAG_FILTER(AUDIO, MP3_DEC_CLIENT, frameReadError, DIAG_INFORMATION)
		AUDLOGI("frameReadError");
		return -1;
	}

	if(mp3_read_compleletd)
		mp3_read_compleletd();
	return 0;
}

static void mp3_dec_main_loop(void* p) 
{	
    unsigned int   			event 		= 0;  
    while (1) {
        OSAFlagWait(dec_pcm_flag_Ref, MP3_DEC_PCM_TASK_MASK, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND); 
		//DIAG_FILTER(AUDIO, MP3_DEC_CLIENT, mp3_dec_event, DIAG_INFORMATION)
		AUDLOGI("mp3_dec_event:%x", event);
		
		if(MP3_DEC_PCM_READ_FRAME & event){
			mp3_dec_read(1);
		}

		if(MP3_DEC_PCM_READ_BUFFER & event){
			mp3_dec_read(0);
		}
    }
}

static void mp3dec_task_init(void)
{
    OS_STATUS status;
    size_t mp3_stackSize = 1024*2;
    int mp3_thread_priority = 80;	
	static bool inited = false; 
		
    if(!inited){
        status = OSAFlagCreate(&dec_pcm_flag_Ref);
        ASSERT(status == OS_SUCCESS);
		
        mp3_stack_ptr = malloc(mp3_stackSize);
		ASSERT(mp3_stack_ptr);
        status = OSATaskCreate(&mp3_Decoder, mp3_stack_ptr, mp3_stackSize, mp3_thread_priority, "mp3PlayClient", mp3_dec_main_loop, NULL);
        ASSERT(status == OS_SUCCESS);       

        inited = 1;
    }
}

static int usedrain= 1;
//ICAT EXPORTED FUNCTION - Audio,Voice,switch_stream_drainmode
void switch_stream_drainmode(void)
{
	usedrain^=1;	
    //DIAG_FILTER(AUDIO, MP3_STREAM_TEST, switch_stream_drainmode, DIAG_INFORMATION)
    AUDLOGI("usedrain:%d!!!", usedrain);
}


static int usedatablock = 1;
//ICAT EXPORTED FUNCTION - Audio,Voice,switch_stream_blockmode
void switch_stream_blockmode(void)
{
	usedatablock^=1;	
    //DIAG_FILTER(AUDIO, MP3_STREAM_TEST, switch_stream_blocksize, DIAG_INFORMATION)
    AUDLOGI("usedatablock:%d!!!", usedatablock);
}

#define MP3_FRAME_TEST_INTV_MIN	(2)
#define MP3_FRAME_TEST_INTV_MAX	(7)
static OSATimerRef	timer_ForMp3StreamTest;
static int mp3StreamReadInterval = MP3_FRAME_TEST_INTV_MIN;
static int last_event = MP3_STREAM_EVENT_FAST_UP;
static void mp3StreamTest_Expire (UINT32 cookie){
	//DIAG_FILTER(AUDIO, MP3_STREAM_TEST, mp3StreamTest_Expire, DIAG_INFORMATION)
	AUDLOGI("mp3StreamReadInterval:%d", mp3StreamReadInterval);
	
	if (last_event == MP3_STREAM_EVENT_FAST_UP) {
		if (usedatablock || !parse_header)
			OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_READ_BUFFER, OSA_FLAG_OR);
		else
			OSAFlagSet(dec_pcm_flag_Ref, MP3_DEC_PCM_READ_FRAME, OSA_FLAG_OR);
	}
}

//ICAT EXPORTED FUNCTION - Audio,Voice,switch_stream_intv
void switch_stream_intv()
{
	if(mp3StreamReadInterval == MP3_FRAME_TEST_INTV_MAX)
		mp3StreamReadInterval = MP3_FRAME_TEST_INTV_MIN;
	else
		mp3StreamReadInterval = MP3_FRAME_TEST_INTV_MAX;
    //DIAG_FILTER(AUDIO, MP3_STREAM_TEST, switch_stream_intv, DIAG_INFORMATION)
    AUDLOGI("mp3StreamReadInterval:%d!!!", mp3StreamReadInterval);

	if(timer_ForMp3StreamTest){
		OSATimerStop(timer_ForMp3StreamTest);
		OSATimerStart(timer_ForMp3StreamTest, mp3StreamReadInterval, mp3StreamReadInterval, mp3StreamTest_Expire, 0);
	}
}

static const int readBlockSizeTable[] = {1, 2, 150, 293, 571, 1024, 2048};
static int readBlockSizeIndex = 0;
//ICAT EXPORTED FUNCTION - Audio,Voice,switch_stream_blocksize
void switch_stream_blocksize(void)
{
	readBlockSizeIndex++;
	if(readBlockSizeIndex >= sizeof(readBlockSizeTable)/sizeof(readBlockSizeTable[0]))
		readBlockSizeIndex = 0;
	readBlockSize = readBlockSizeTable[readBlockSizeIndex];
	
    //DIAG_FILTER(AUDIO, MP3_STREAM_TEST, switch_stream_blocksize, DIAG_INFORMATION)
    AUDLOGI("readBlockSize:%d!!!", readBlockSize);
}

static void stream_test_trigger_user(int event){
	//DIAG_FILTER(AUDIO, MP3_STREAM_TEST, stream_test_trigger_user, DIAG_INFORMATION)
	AUDLOGI("event:%d!", event);
	if (event == MP3_STREAM_EVENT_SLOW_DOWN || event == MP3_STREAM_EVENT_FAST_UP)
		last_event = event;
}

static void stream_test_readed(void){
	Mp3StreamDataInfo streamInfo = {0};
	streamInfo.buf = inputBuf;
	streamInfo.size = bytesRead;
	int ret = mp3StreamPlayFrame(&streamInfo);	
	//DIAG_FILTER(AUDIO, MP3_STREAM_TEST, stream_test_readed, DIAG_INFORMATION)
    AUDLOGI("ret:%d", ret);
}

static void stream_test_readed_buffermode(void){
	Mp3StreamDataInfo streamInfo = {0};
	streamInfo.buf = inputBuf;
	streamInfo.size = bytesRead;
	int ret = mp3StreamPlayBuffer(&streamInfo);	
	//DIAG_FILTER(AUDIO, MP3_STREAM_TEST, stream_test_readed_buffermode, DIAG_INFORMATION)
    AUDLOGI("ret:%d", ret);
}

int mp3streamdec_test2(char* name, int option) {
	bool flag = mp3_dec_init(name, &mp3Reader, &decoderConfig);
	if (!flag) {
		Mp3StreamConfigInfo configInfo = { 0 };
		configInfo.option = option;
		configInfo.trigger = stream_test_trigger_user;

		if (usedatablock || !parse_header)
			mp3_read_compleletd = stream_test_readed_buffermode;
		else
			mp3_read_compleletd = stream_test_readed;

		if (parse_header)
			configInfo.header_parsed = 1;

		if (mp3_Decoder == NULL) {
			mp3dec_task_init();
		}
		mp3StreamStart(&configInfo);
		OSATimerCreate(&timer_ForMp3StreamTest);
		OSATimerStart(timer_ForMp3StreamTest, 1, mp3StreamReadInterval, mp3StreamTest_Expire, 0);
	}
	return 0;
}

//ICAT EXPORTED FUNCTION - Audio,Voice,mp3streamdec_test
int mp3streamdec_test(void) {
    int option = 0;
    if (usedrain)
        option = 0x4;
    return mp3streamdec_test2("stream_test.mp3", option);
}

//ICAT EXPORTED FUNCTION - Audio,Voice,mp3streamdec_test_pause
int mp3streamdec_test_pause(void) {  
	mp3StreamPause();
    return 0;
}

//ICAT EXPORTED FUNCTION - Audio,Voice,mp3streamdec_test_resume
int mp3streamdec_test_resume(void) {  
	mp3StreamResume();
    return 0;
}

//ICAT EXPORTED FUNCTION - Audio,Voice,mp3streamdec_test_stop
int mp3streamdec_test_stop(void) {
	if(timer_ForMp3StreamTest){
		OSATimerStop(timer_ForMp3StreamTest);
		OSATimerDelete(timer_ForMp3StreamTest);
		timer_ForMp3StreamTest = NULL;
		mp3StreamStop();		
	    mp3_dec_deinit();
	}
    return 0;
}

} // end of extern C

#endif

