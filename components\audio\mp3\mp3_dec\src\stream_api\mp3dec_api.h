#pragma once
#ifdef __cplusplus
extern "C" {
#endif

#define MEDIA_PLAY_MASK_RATE_OUT		(1 << 0)
#define MEDIA_PLAY_MASK_HIGH_QUALITY	(1 << 1)
#define MEDIA_PLAY_MASK_DRAIN			(1 << 2)
#define MEDIA_PLAY_MASK_LATENCY			(1 << 3)
#define MEDIA_PLAY_MASK_DEST_END		(3 << 4)
#define MEDIA_PLAY_MASK_OVERRIDE		(1 << 6)
#define MEDIA_PLAY_MASK_BYPASS			(1 << 7)
#define MEDIA_PLAY_MASK_CYCLIC			(1 << 8)

    typedef enum Mp3FileStatus {
        MP3_FILE_STATUS_ENDED = 0,
    }Mp3FileStatus;

    typedef enum Mp3FileEventType {
        MP3_FILE_EVENT_STATUS,
        MP3_FILE_EVENT_ID3_OFFSET,
        MP3_FILE_EVENT_SAMPLERATE,
        MP3_FILE_EVENT_CHANNAL,
        MP3_FILE_EVENT_BITRATE,
    }Mp3FileEventType;

    typedef struct Mp3FileConfigInfo {
        char name[200];                         // specify file name
        int option;                             // specify option in bitmap format, refer MEDIA_PLAY_OPTION for definition
        int skip_frame;                         // specify how many mp3 data frames will be skipped before playback
        void(*trigger)(Mp3FileEventType, int);  // get mp3 file playback event type and value
    }Mp3FileConfigInfo;

    typedef struct Mp3StreamDataInfo {
        void* buf;
        int size;
    }Mp3StreamDataInfo;

    typedef struct Mp3StreamConfigInfo {
        int rate;                               // specify samplerate, fill 0 if not sure
        int channel;                            // specify channel, fill 0 if not sure
        int option;                             // specify option in bitmap format, refer MEDIA_PLAY_OPTION for definiton
        int header_parsed;                      // set to 0 if id3 header is contained or not sure
        int cache_frames;                       // how many mp3 frames(1 frame->26ms) are held before decoding and sending to output device 
        void(*trigger)(int);                    // get mp3 stream playback event type, refer Mp3StreamEvent for event type definition
    }Mp3StreamConfigInfo;

    typedef enum Mp3StreamEvent {
        MP3_STREAM_EVENT_STARTED = 100,
        MP3_STREAM_EVENT_LOST,
        MP3_STREAM_EVENT_SLOW_DOWN,
        MP3_STREAM_EVENT_FAST_UP,
        MP3_STREAM_EVENT_CACHING,
        MP3_STREAM_EVENT_CACHED,
        MP3_STREAM_EVENT_ENDED,
    }Mp3StreamEvent;

    typedef struct MEDIA_PLAY_OPTION {
        union {
            int option;
            struct {
                int rate_out : 1;		//0x1,play out sample rate,0->8k,1->16k			
                int high_quality : 1; 	//0x2,resample option,0->low, 1->high			
                int drain : 1;			//0x4,drain option,0->disable,1->enable			
                int latency : 1;		//0x8,use deep buffer,0->disable,1->enable 			
                int dest_end : 2;		//0x30,dest play end,0->near,1->far,2->both			
                int override : 1;		//0x40,override voice if any,0->false,1->true			
                int bypass : 1;			//0x80,bypass volume etc enhancement alg,0->false,1->true
                int cyclic : 1;			//0x100,play current file in cyclic mode,0->false,1->true	
                int resv : 23;
            };
        };
    }MEDIA_PLAY_OPTION;

    int mp3Start(const char *file_name);
    int mp3Pause(void);
    int mp3Resume(void);
    int mp3Stop(void);

    int mp3PlayStart(const Mp3FileConfigInfo* configInfo);
    int mp3PlayPause(void);
    int mp3PlayResume(void);
    int mp3PlayStop(int drain);

    int mp3StreamStart(const Mp3StreamConfigInfo* configInfo);
    int mp3StreamPlayFrame(const Mp3StreamDataInfo* dataInfo);
    int mp3StreamPlayBuffer(const Mp3StreamDataInfo* dataInfo);
    int mp3StreamPause(void);
    int mp3StreamResume(void);
    int mp3StreamStop(void);
    int mp3StreamStopWithDrain(int drain);

#ifdef __cplusplus
}
#endif
