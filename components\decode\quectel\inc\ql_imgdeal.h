/**  @file
  ql_imgdeal.h

  @brief
  This file is image deal api.

*/

/*================================================================
  Copyright (c) 2020 Quectel Wireless Solution, Co., Ltd.  All Rights Reserved.
  Quectel Wireless Solution Proprietary and Confidential.
=================================================================*/
/*=================================================================

                        EDIT HISTORY FOR MODULE

This section contains comments describing changes made to the module.
Notice that changes are listed in reverse chronological order.

WHEN              WHO         WHAT, WHERE, WHY
------------     -------     -------------------------------------------------------------------------------

=================================================================*/

#ifndef _QL_IMGDEAL_H
#define _QL_IMGDEAL_H

#ifdef __cplusplus
extern "C" {
#endif

/*===========================================================================
 * include files
 ===========================================================================*/
#include "ql_decoder.h"
/*===========================================================================
 * Macro Definition
 ===========================================================================*/

#define VGA 307200
#define QVGA 76800

#define false 0
#define true 1

#define QL_DECODER_DEBUG_EN 1

#define ASR 0
#define UNISOC 0

#if QL_DECODER_DEBUG_EN

#if UNISOC
#include "ql_api_osi.h"
#include "ql_log.h"
#include "ql_fs.h"
#define DEBUG(msg, ...) QL_LOG(QL_LOG_LEVEL_INFO, "quec_decoder", msg, ##__VA_ARGS__)

void saveImg(unsigned char *raw, int width, int height, unsigned char flag);
#endif

#if ASR
#include "quec_debug.h"
#include "UART.h"
#include <utlTime.h>
#include "FreqChange.h"
extern int gettimeofday(struct timeval *tv, void* dummy);
#define DEBUG(msg, ...) RTI_LOG(msg, ##__VA_ARGS__)
//#define DEBUG(msg, ...) CPUartLogPrintf(msg, ##__VA_ARGS__)
#endif

#else
#define DEBUG(...)
#endif

/*===========================================================================
 * Struct
 ===========================================================================*/

// typedef struct pot
// {
// 	int x;
// 	int y;
// }Pot;

// typedef struct
// {
//     int line;
//     Pot min_pt;
//     Pot max_pt;
//     int width;
//     int height;
//     int scaling;
//     int d;
//     int scaling_width;
//     int scaling_height;
//     int cam_config;
// }cvImg;


// /*===========================================================================
//  * Enum
//  ===========================================================================*/

// typedef enum
// {
//     NONE_CODE = 0,
//     QR_CODE,
//     BAR_CODE_X,
//     BAR_CODE_Y,
//     MAYBE_CODE
// }SCAN_CODE_STATE_E;

// enum
// {
//     CV_INTER_NN        =0,
//     CV_INTER_LINEAR    =1,
//     CV_INTER_CUBIC     =2,
//     CV_INTER_AREA      =3,
//     CV_INTER_LANCZOS4  =4
// };

// enum
// {
//     CV_WARP_FILL_OUTLIERS =8,
//     CV_WARP_INVERSE_MAP  =16
// };

// enum
// {
//     INTER_NEAREST=CV_INTER_NN, //!< nearest neighbor interpolation
//     INTER_LINEAR=CV_INTER_LINEAR, //!< bilinear interpolation
//     INTER_CUBIC=CV_INTER_CUBIC, //!< bicubic interpolation
//     INTER_AREA=CV_INTER_AREA, //!< area-based (or super) interpolation
//     INTER_LANCZOS4=CV_INTER_LANCZOS4, //!< Lanczos interpolation over 8x8 neighborhood
//     INTER_MAX=7,
//     WARP_INVERSE_MAP=CV_WARP_INVERSE_MAP
// };

// /*===========================================================================
//  * Variate
//  ===========================================================================*/
 
// /*===========================================================================
//  * Functions
//  ===========================================================================*/

// //int ql_image_decoder(void* raw, int width, int height);



// SCAN_CODE_STATE_E ql_deal_img(void* raw, int width, int height, cvImg *img);
// int process_img(unsigned char *indata, unsigned char *outdata, cvImg *img);
// int process_img_bar_code_yb(unsigned char *indata, unsigned char *outdata1, cvImg *img);
// int process_img_bar_code_xb(unsigned char *indata, unsigned char *outdata1, cvImg *img);
// void ql_gety_convert(unsigned char *inBuf, unsigned char *outBuf, int width, int height, int out_width, int out_height, int value);
// void ql_quickAdaptiveThreshold(unsigned char *grayImage, unsigned char *binImage, int width, int height);
// int ql_YuvImageGetY(unsigned char *inBuf, unsigned char *outBuf, unsigned int size);
// int ql_YuvImagePrinY(unsigned char *inBuf, unsigned char *outBuf, unsigned int size);
// int ql_YuvImagePrinThreshold(unsigned char *inBuf, unsigned char *outBuf, unsigned int size, int value);
// void ql_thresholdAdapt(unsigned char *inbuf, unsigned char *outbuf, int in_width, int in_height, float rate);
// void ql_sharpen(unsigned char *inbuf, unsigned char *outbuf, int in_width, int in_height);
// void ql_gaussian(unsigned char *inbuf, unsigned char *outbuf, int in_width, int in_height, int size, float sigma);


// SCAN_CODE_STATE_E ql_deal_img1(void* raw, int width, int height);

int ql_YuvImageGetY(unsigned char *inBuf, unsigned char *outBuf, unsigned int  size);


#ifdef __cplusplus
    } /*"C" */
#endif
    
#endif /* _QL_DECODER_H */

