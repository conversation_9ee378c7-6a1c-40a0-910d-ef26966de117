{\rtf1\ansi\ansicpg1252\uc1 \deff0\deflang1033\deflangfe1033
{\fonttbl {\f0\froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman;}
{\f1\fswiss\fcharset0\fprq2{\*\panose 020b0604020202020204}Arial;}
{\f2\fmodern\fcharset0\fprq1{\*\panose 02070309020205020404}Courier New;}
{\f3\froman\fcharset2\fprq2{\*\panose 05050102010706020507}Symbol;}
}
{\colortbl;\red0\green0\blue0;\red0\green0\blue255;\red0\green255\blue255;\red0\green255\blue0;\red255\green0\blue255;\red255\green0\blue0;\red255\green255\blue0;\red255\green255\blue255;\red0\green0\blue128;\red0\green128\blue128;\red0\green128\blue0;\red128\green0\blue128;\red128\green0\blue0;\red128\green128\blue0;\red128\green128\blue128;\red192\green192\blue192;}
{\stylesheet
{\widctlpar\adjustright \fs20\cgrid \snext0 Normal;}
{\paperw11900\paperh16840\margl1800\margr1800\margt1440\margb1440\gutter0\ltrsect}
{\s1\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs36\kerning36\cgrid \sbasedon0 \snext0 heading 1;}
{\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid \sbasedon0 \snext0 heading 2;}
{\s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid \sbasedon0 \snext0 heading 3;}
{\s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid \sbasedon0 \snext0 heading 4;}{\*\cs10 \additive Default Paragraph Font;}
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid \sbasedon0 \snext0 heading 5;}{\*\cs10 \additive Default Paragraph Font;}
{\s15\qc\sb240\sa60\widctlpar\outlinelevel0\adjustright \b\f1\fs32\kerning28\cgrid \sbasedon0 \snext15 Title;}
{\s16\qc\sa60\widctlpar\outlinelevel1\adjustright \f1\cgrid \sbasedon0 \snext16 Subtitle;}
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid \sbasedon0 \snext17 BodyText;}
{\s18\widctlpar\fs22\cgrid \sbasedon0 \snext18 DenseText;}
{\s28\widctlpar\tqc\tx4320\tqr\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext28 header;}
{\s29\widctlpar\tqc\tx4320\tqr\tx8640\qr\adjustright \fs20\cgrid \sbasedon0 \snext29 footer;}
{\s30\li360\sa60\sb120\keepn\widctlpar\adjustright \b\f1\fs20\cgrid \sbasedon0 \snext30 GroupHeader;}
{\s40\li0\widctlpar\adjustright \shading1000\cbpat8 \f2\fs16\cgrid \sbasedon0 \snext41 Code Example 0;}
{\s41\li360\widctlpar\adjustright \shading1000\cbpat8 \f2\fs16\cgrid \sbasedon0 \snext42 Code Example 1;}
{\s42\li720\widctlpar\adjustright \shading1000\cbpat8 \f2\fs16\cgrid \sbasedon0 \snext43 Code Example 2;}
{\s43\li1080\widctlpar\adjustright \shading1000\cbpat8 \f2\fs16\cgrid \sbasedon0 \snext44 Code Example 3;}
{\s44\li1440\widctlpar\adjustright \shading1000\cbpat8 \f2\fs16\cgrid \sbasedon0 \snext45 Code Example 4;}
{\s45\li1800\widctlpar\adjustright \shading1000\cbpat8 \f2\fs16\cgrid \sbasedon0 \snext46 Code Example 5;}
{\s46\li2160\widctlpar\adjustright \shading1000\cbpat8 \f2\fs16\cgrid \sbasedon0 \snext47 Code Example 6;}
{\s47\li2520\widctlpar\adjustright \shading1000\cbpat8 \f2\fs16\cgrid \sbasedon0 \snext48 Code Example 7;}
{\s48\li2880\widctlpar\adjustright \shading1000\cbpat8 \f2\fs16\cgrid \sbasedon0 \snext49 Code Example 8;}
{\s49\li3240\widctlpar\adjustright \shading1000\cbpat8 \f2\fs16\cgrid \sbasedon0 \snext49 Code Example 9;}
{\s50\li0\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid \sbasedon0 \snext51 List Continue 0;}
{\s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid \sbasedon0 \snext52 List Continue 1;}
{\s52\li720\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid \sbasedon0 \snext53 List Continue 2;}
{\s53\li1080\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid \sbasedon0 \snext54 List Continue 3;}
{\s54\li1440\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid \sbasedon0 \snext55 List Continue 4;}
{\s55\li1800\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid \sbasedon0 \snext56 List Continue 5;}
{\s56\li2160\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid \sbasedon0 \snext57 List Continue 6;}
{\s57\li2520\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid \sbasedon0 \snext58 List Continue 7;}
{\s58\li2880\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid \sbasedon0 \snext59 List Continue 8;}
{\s59\li3240\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid \sbasedon0 \snext59 List Continue 9;}
{\s60\li0\widctlpar\ql\adjustright \fs20\cgrid \sbasedon0 \snext61 DescContinue 0;}
{\s61\li360\widctlpar\ql\adjustright \fs20\cgrid \sbasedon0 \snext62 DescContinue 1;}
{\s62\li720\widctlpar\ql\adjustright \fs20\cgrid \sbasedon0 \snext63 DescContinue 2;}
{\s63\li1080\widctlpar\ql\adjustright \fs20\cgrid \sbasedon0 \snext64 DescContinue 3;}
{\s64\li1440\widctlpar\ql\adjustright \fs20\cgrid \sbasedon0 \snext65 DescContinue 4;}
{\s65\li1800\widctlpar\ql\adjustright \fs20\cgrid \sbasedon0 \snext66 DescContinue 5;}
{\s66\li2160\widctlpar\ql\adjustright \fs20\cgrid \sbasedon0 \snext67 DescContinue 6;}
{\s67\li2520\widctlpar\ql\adjustright \fs20\cgrid \sbasedon0 \snext68 DescContinue 7;}
{\s68\li2880\widctlpar\ql\adjustright \fs20\cgrid \sbasedon0 \snext69 DescContinue 8;}
{\s69\li3240\widctlpar\ql\adjustright \fs20\cgrid \sbasedon0 \snext69 DescContinue 9;}
{\s70\li0\sa30\sb30\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext81 LatexTOC 0;}
{\s71\li360\sa27\sb27\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext82 LatexTOC 1;}
{\s72\li720\sa24\sb24\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext83 LatexTOC 2;}
{\s73\li1080\sa21\sb21\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext84 LatexTOC 3;}
{\s74\li1440\sa18\sb18\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext85 LatexTOC 4;}
{\s75\li1800\sa15\sb15\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext86 LatexTOC 5;}
{\s76\li2160\sa12\sb12\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext87 LatexTOC 6;}
{\s77\li2520\sa9\sb9\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext88 LatexTOC 7;}
{\s78\li2880\sa6\sb6\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext89 LatexTOC 8;}
{\s79\li3240\sa3\sb3\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid \sbasedon0 \snext89 LatexTOC 9;}
{\s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid \sbasedon0 \snext81 \sautoupd List Bullet 0;}
{\s81\fi-360\li720\widctlpar\jclisttab\tx720{\*\pn \pnlvlbody\ilvl0\ls2\pnrnot0\pndec }\ls2\adjustright \fs20\cgrid \sbasedon0 \snext82 \sautoupd List Bullet 1;}
{\s82\fi-360\li1080\widctlpar\jclisttab\tx1080{\*\pn \pnlvlbody\ilvl0\ls3\pnrnot0\pndec }\ls3\adjustright \fs20\cgrid \sbasedon0 \snext83 \sautoupd List Bullet 2;}
{\s83\fi-360\li1440\widctlpar\jclisttab\tx1440{\*\pn \pnlvlbody\ilvl0\ls4\pnrnot0\pndec }\ls4\adjustright \fs20\cgrid \sbasedon0 \snext84 \sautoupd List Bullet 3;}
{\s84\fi-360\li1800\widctlpar\jclisttab\tx1800{\*\pn \pnlvlbody\ilvl0\ls5\pnrnot0\pndec }\ls5\adjustright \fs20\cgrid \sbasedon0 \snext85 \sautoupd List Bullet 4;}
{\s85\fi-360\li2160\widctlpar\jclisttab\tx2160{\*\pn \pnlvlbody\ilvl0\ls6\pnrnot0\pndec }\ls6\adjustright \fs20\cgrid \sbasedon0 \snext86 \sautoupd List Bullet 5;}
{\s86\fi-360\li2520\widctlpar\jclisttab\tx2520{\*\pn \pnlvlbody\ilvl0\ls7\pnrnot0\pndec }\ls7\adjustright \fs20\cgrid \sbasedon0 \snext87 \sautoupd List Bullet 6;}
{\s87\fi-360\li2880\widctlpar\jclisttab\tx2880{\*\pn \pnlvlbody\ilvl0\ls8\pnrnot0\pndec }\ls8\adjustright \fs20\cgrid \sbasedon0 \snext88 \sautoupd List Bullet 7;}
{\s88\fi-360\li3240\widctlpar\jclisttab\tx3240{\*\pn \pnlvlbody\ilvl0\ls9\pnrnot0\pndec }\ls9\adjustright \fs20\cgrid \sbasedon0 \snext89 \sautoupd List Bullet 8;}
{\s89\fi-360\li3600\widctlpar\jclisttab\tx3600{\*\pn \pnlvlbody\ilvl0\ls10\pnrnot0\pndec }\ls10\adjustright \fs20\cgrid \sbasedon0 \snext89 \sautoupd List Bullet 9;}
{\s90\fi-360\li360\widctlpar\fs20\cgrid \sbasedon0 \snext91 \sautoupd List Enum 0;}
{\s91\fi-360\li720\widctlpar\fs20\cgrid \sbasedon0 \snext92 \sautoupd List Enum 1;}
{\s92\fi-360\li1080\widctlpar\fs20\cgrid \sbasedon0 \snext93 \sautoupd List Enum 2;}
{\s93\fi-360\li1440\widctlpar\fs20\cgrid \sbasedon0 \snext94 \sautoupd List Enum 3;}
{\s94\fi-360\li1800\widctlpar\fs20\cgrid \sbasedon0 \snext95 \sautoupd List Enum 4;}
{\s95\fi-360\li2160\widctlpar\fs20\cgrid \sbasedon0 \snext96 \sautoupd List Enum 5;}
{\s96\fi-360\li2520\widctlpar\fs20\cgrid \sbasedon0 \snext96 \sautoupd List Enum 5;}
{\s97\fi-360\li2880\widctlpar\fs20\cgrid \sbasedon0 \snext98 \sautoupd List Enum 7;}
{\s98\fi-360\li3240\widctlpar\fs20\cgrid \sbasedon0 \snext99 \sautoupd List Enum 8;}
{\s99\fi-360\li3600\widctlpar\fs20\cgrid \sbasedon0 \snext99 \sautoupd List Enum 9;}
}
{\info 
{\title {\comment ASR Audio API: AMR  {\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
0.2.0 \par
}}ASR Audio API: AMR}
{\comment Generated byDoxgyen. }
{\creatim \yr2020\mo12\dy10\hr12\min31\sec13}
}\pard\plain 
\sectd\pgnlcrm
{\footer \s29\widctlpar\tqc\tx4320\tqr\tx8640\qr\adjustright \fs20\cgrid {\chpgn}}
\pard\plain \s16\qc\sa60\widctlpar\outlinelevel1\adjustright \f1\cgrid 
\vertalc\qc\par\par\par\par\par\par\par
\pard\plain \s15\qc\sb240\sa60\widctlpar\outlinelevel0\adjustright \b\f1\fs32\kerning28\cgrid 
{\field\fldedit {\*\fldinst TITLE \\*MERGEFORMAT}{\fldrslt ASR Audio API: AMR}}\par
\pard\plain \s16\qc\sa60\widctlpar\outlinelevel1\adjustright \f1\cgrid 
\par
\par\par\par\par\par\par\par\par\par\par\par\par
\pard\plain \s16\qc\sa60\widctlpar\outlinelevel1\adjustright \f1\cgrid 
{\field\fldedit {\*\fldinst AUTHOR \\*MERGEFORMAT}{\fldrslt AUTHOR}}\par
Version 0.2.0\par{\field\fldedit {\*\fldinst CREATEDATE \\*MERGEFORMAT}{\fldrslt Thu Dec 10 2020 }}\par
\page\page\vertalt
\pard\plain 
\s1\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs36\kerning36\cgrid Table of Contents\par
\pard\plain \par
{\field\fldedit {\*\fldinst TOC \\f \\*MERGEFORMAT}{\fldrslt Table of contents}}\par
\pard\plain 
\sect \sbkpage \pgndec \pgnrestart
\sect \sectd \sbknone
{\footer \s29\widctlpar\tqc\tx4320\tqr\tx8640\qr\adjustright \fs20\cgrid {\chpgn}}

\pard\plain \sect\sbkpage
\s1\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs36\kerning36\cgrid 
Class Index\par \pard\plain 
{\tc \v Class Index}
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
Class List\par \pard\plain 
{
\pard\plain \s17\sa60\sb30\widctlpar\qj \fs22\cgrid Here are the classes, structs, unions and interfaces with brief descriptions:}
{
\par
\pard\plain \s71\li360\sa27\sb27\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid 
{\b {\b AmrEncConfigInfo} } \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAACM \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b {\b AmrFileConfigInfo} } \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAACR \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b {\b amrnb_dec_config} } \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAACU \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b {\b amrnb_dec_state} } \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAACX \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b {\b amrnb_enc_config} } \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAADG \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b {\b amrnb_enc_state} } \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAADL \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b {\b AmrNbEncState} } \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAADU \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b {\b amrwb_dec_config} } \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAADX \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b {\b amrwb_dec_state} } \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAAEA \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
\par}
\pard\plain \sect\sbkpage
\s1\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs36\kerning36\cgrid 
File Index\par \pard\plain 
{\tc \v File Index}
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
File List\par \pard\plain 
{
\pard\plain \s17\sa60\sb30\widctlpar\qj \fs22\cgrid Here is a list of all documented files with brief descriptions:}
{
\par
\pard\plain \s71\li360\sa27\sb27\widctlpar\tqr\tldot\tx8640\adjustright \fs20\cgrid 
{\b pcac/amr/amrnb/dec/api/{\b amrnb_dec_api.h} ({\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR nb-amr decoder related API describes the process and functions used to decode nb-amr from file|memory to file|memory on ASR RTOS platform })} \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAAAA \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b pcac/amr/amrnb/enc/api/{\b amrnb_enc_api.h} ({\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR nb-amr encoder related API describes the process and functions used to encode pcm from file|memory to file|memory on ASR RTOS platform })} \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAAAN \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b pcac/amr/amrwb/api/{\b amrwb_dec_api.h} ({\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR wb-amr decoder related API describes the process and functions used to decode wb-amr from file|memory to file|memory on ASR RTOS platform })} \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAAAZ \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b pcac/amr/include/{\b amr_vocoder_api.h} ({\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR amr related API describes the process and functions used to play from and encode into amr file on ASR RTOS platform })} \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAABM \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
{\b pcac/amr/include/{\b amr_vocoder_config.h} } \tab {\field\fldedit {\*\fldinst PAGEREF AAAAAAAAEX \\*MERGEFORMAT}{\fldrslt pagenum}}
\par
\par}
\pard\plain \sect\sbkpage
\s1\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs36\kerning36\cgrid 
Class Documentation{\tc \v Class Documentation}
\par \pard\plain 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
AmrEncConfigInfo Struct Reference\par \pard\plain 
{\tc\tcl2 \v AmrEncConfigInfo}
{\xe \v AmrEncConfigInfo}
{\bkmkstart AAAAAAAACM}
{\bkmkend AAAAAAAACM}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Public Attributes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b mode}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b gain}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b rate}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b dtx}\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Member Data Documentation\par
\pard\plain 
{\xe \v dtx\:AmrEncConfigInfo}
{\xe \v AmrEncConfigInfo\:dtx}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int AmrEncConfigInfo::dtx}}
\par
{\bkmkstart AAAAAAAACN}
{\bkmkend AAAAAAAACN}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
dtx on or off, 0:off, 1:on \par
}}
{\xe \v gain\:AmrEncConfigInfo}
{\xe \v AmrEncConfigInfo\:gain}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int AmrEncConfigInfo::gain}}
\par
{\bkmkstart AAAAAAAACO}
{\bkmkend AAAAAAAACO}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
extra digital gain for recorded pcm before encoding, unit:dB, default:0 \par
}}
{\xe \v mode\:AmrEncConfigInfo}
{\xe \v AmrEncConfigInfo\:mode}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int AmrEncConfigInfo::mode}}
\par
{\bkmkstart AAAAAAAACP}
{\bkmkend AAAAAAAACP}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
encoder mode, refer {\f2 acm_audio_record_mode_t}  \par
}}
{\xe \v rate\:AmrEncConfigInfo}
{\xe \v AmrEncConfigInfo\:rate}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int AmrEncConfigInfo::rate}}
\par
{\bkmkstart AAAAAAAACQ}
{\bkmkend AAAAAAAACQ}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
encoder rate, 0:MR475, 1:MR515, 2:MR59, 3:MR67, 4:MR74, 5:MR795, 6:MR102, 7:MR122 \par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
The documentation for this struct was generated from the following file:{\par
\pard\plain \s81\fi-360\li720\widctlpar\jclisttab\tx720{\*\pn \pnlvlbody\ilvl0\ls2\pnrnot0\pndec }\ls2\adjustright \fs20\cgrid 
pcac/amr/include/{\b amr_vocoder_api.h}\par
}\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
AmrFileConfigInfo Struct Reference\par \pard\plain 
{\tc\tcl2 \v AmrFileConfigInfo}
{\xe \v AmrFileConfigInfo}
{\bkmkstart AAAAAAAACR}
{\bkmkend AAAAAAAACR}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Public Attributes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b option}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
AmrPlaybackEventCallback {\b trigger}\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Member Data Documentation\par
\pard\plain 
{\xe \v option\:AmrFileConfigInfo}
{\xe \v AmrFileConfigInfo\:option}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int AmrFileConfigInfo::option}}
\par
{\bkmkstart AAAAAAAACS}
{\bkmkend AAAAAAAACS}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
playback option in bitmap format, refer {\f2 AUDIO_PLAY_OPTION}  in acm_audio_def.h \par
}}
{\xe \v trigger\:AmrFileConfigInfo}
{\xe \v AmrFileConfigInfo\:trigger}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
AmrPlaybackEventCallback AmrFileConfigInfo::trigger}}
\par
{\bkmkstart AAAAAAAACT}
{\bkmkend AAAAAAAACT}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
user registers callback to get playback event such as file end etc \par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
The documentation for this struct was generated from the following file:{\par
\pard\plain \s81\fi-360\li720\widctlpar\jclisttab\tx720{\*\pn \pnlvlbody\ilvl0\ls2\pnrnot0\pndec }\ls2\adjustright \fs20\cgrid 
pcac/amr/include/{\b amr_vocoder_api.h}\par
}\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
amrnb_dec_config Struct Reference\par \pard\plain 
{\tc\tcl2 \v amrnb_dec_config}
{\xe \v amrnb_dec_config}
{\bkmkstart AAAAAAAACU}
{\bkmkend AAAAAAAACU}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Public Attributes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
const char * {\b name}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
const char * {\b out_name}\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Member Data Documentation\par
\pard\plain 
{\xe \v name\:amrnb_dec_config}
{\xe \v amrnb_dec_config\:name}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
const char* amrnb_dec_config::name}}
\par
{\bkmkstart AAAAAAAACV}
{\bkmkend AAAAAAAACV}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
input nb-amr file name, specify it when reading from file is needed \par
}}
{\xe \v out_name\:amrnb_dec_config}
{\xe \v amrnb_dec_config\:out_name}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
const char* amrnb_dec_config::out_name}}
\par
{\bkmkstart AAAAAAAACW}
{\bkmkend AAAAAAAACW}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
output pcm file name, specify it when writing to file is needed \par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
The documentation for this struct was generated from the following file:{\par
\pard\plain \s81\fi-360\li720\widctlpar\jclisttab\tx720{\*\pn \pnlvlbody\ilvl0\ls2\pnrnot0\pndec }\ls2\adjustright \fs20\cgrid 
pcac/amr/amrnb/dec/api/{\b amrnb_dec_api.h}\par
}\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
amrnb_dec_state Struct Reference\par \pard\plain 
{\tc\tcl2 \v amrnb_dec_state}
{\xe \v amrnb_dec_state}
{\bkmkstart AAAAAAAACX}
{\bkmkend AAAAAAAACX}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Public Attributes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

AUDIO_FILE_ID {\b fpInput}{\bkmkstart AAAAAAAACY}
{\bkmkend AAAAAAAACY}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

AUDIO_FILE_ID {\b fpOutput}{\bkmkstart AAAAAAAACZ}
{\bkmkend AAAAAAAACZ}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

void * {\b amrHandle}{\bkmkstart AAAAAAAADA}
{\bkmkend AAAAAAAADA}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

void * {\b inputBuf}{\bkmkstart AAAAAAAADB}
{\bkmkend AAAAAAAADB}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

void * {\b outputBuf}{\bkmkstart AAAAAAAADC}
{\bkmkend AAAAAAAADC}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

uint32_t {\b amrFrameSize}{\bkmkstart AAAAAAAADD}
{\bkmkend AAAAAAAADD}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

uint32_t {\b pcmFrameSize}{\bkmkstart AAAAAAAADE}
{\bkmkend AAAAAAAADE}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

Frame_Type_3GPP {\b frameType}{\bkmkstart AAAAAAAADF}
{\bkmkend AAAAAAAADF}
\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
The documentation for this struct was generated from the following file:{\par
\pard\plain \s81\fi-360\li720\widctlpar\jclisttab\tx720{\*\pn \pnlvlbody\ilvl0\ls2\pnrnot0\pndec }\ls2\adjustright \fs20\cgrid 
pcac/amr/amrnb/dec/api/amrnb_dec_api.cpp\par
}\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
amrnb_enc_config Struct Reference\par \pard\plain 
{\tc\tcl2 \v amrnb_enc_config}
{\xe \v amrnb_enc_config}
{\bkmkstart AAAAAAAADG}
{\bkmkend AAAAAAAADG}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Public Attributes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
const char * {\b name}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
const char * {\b out_name}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int32_t {\b mode}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int32_t {\b output_format}\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Member Data Documentation\par
\pard\plain 
{\xe \v mode\:amrnb_enc_config}
{\xe \v amrnb_enc_config\:mode}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int32_t amrnb_enc_config::mode}}
\par
{\bkmkstart AAAAAAAADH}
{\bkmkend AAAAAAAADH}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
nb-amr rate, default 0 for AMR 4.75kbps \par
}}
{\xe \v name\:amrnb_enc_config}
{\xe \v amrnb_enc_config\:name}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
const char* amrnb_enc_config::name}}
\par
{\bkmkstart AAAAAAAADI}
{\bkmkend AAAAAAAADI}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
input pcm file name, specify it when reading from file is needed \par
}}
{\xe \v out_name\:amrnb_enc_config}
{\xe \v amrnb_enc_config\:out_name}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
const char* amrnb_enc_config::out_name}}
\par
{\bkmkstart AAAAAAAADJ}
{\bkmkend AAAAAAAADJ}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
output nb-amr file name, specify it when writing to file is needed \par
}}
{\xe \v output_format\:amrnb_enc_config}
{\xe \v amrnb_enc_config\:output_format}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int32_t amrnb_enc_config::output_format}}
\par
{\bkmkstart AAAAAAAADK}
{\bkmkend AAAAAAAADK}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
nb-amr format, default 0 for AMR_TX_WMF \par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
The documentation for this struct was generated from the following file:{\par
\pard\plain \s81\fi-360\li720\widctlpar\jclisttab\tx720{\*\pn \pnlvlbody\ilvl0\ls2\pnrnot0\pndec }\ls2\adjustright \fs20\cgrid 
pcac/amr/amrnb/enc/api/{\b amrnb_enc_api.h}\par
}\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
amrnb_enc_state Struct Reference\par \pard\plain 
{\tc\tcl2 \v amrnb_enc_state}
{\xe \v amrnb_enc_state}
{\bkmkstart AAAAAAAADL}
{\bkmkend AAAAAAAADL}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Public Attributes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

AUDIO_FILE_ID {\b fSrc}{\bkmkstart AAAAAAAADM}
{\bkmkend AAAAAAAADM}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

AUDIO_FILE_ID {\b fDst}{\bkmkstart AAAAAAAADN}
{\bkmkend AAAAAAAADN}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

uint16_t * {\b inputBuf}{\bkmkstart AAAAAAAADO}
{\bkmkend AAAAAAAADO}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

uint8_t * {\b outputBuf}{\bkmkstart AAAAAAAADP}
{\bkmkend AAAAAAAADP}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

{\b AmrNbEncState} * {\b amr}{\bkmkstart AAAAAAAADQ}
{\bkmkend AAAAAAAADQ}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

int {\b bytesGenerated}{\bkmkstart AAAAAAAADR}
{\bkmkend AAAAAAAADR}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

int {\b mode}{\bkmkstart AAAAAAAADS}
{\bkmkend AAAAAAAADS}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

int {\b out_format}{\bkmkstart AAAAAAAADT}
{\bkmkend AAAAAAAADT}
\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
The documentation for this struct was generated from the following file:{\par
\pard\plain \s81\fi-360\li720\widctlpar\jclisttab\tx720{\*\pn \pnlvlbody\ilvl0\ls2\pnrnot0\pndec }\ls2\adjustright \fs20\cgrid 
pcac/amr/amrnb/enc/api/amrnb_enc_api.cpp\par
}\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
AmrNbEncState Struct Reference\par \pard\plain 
{\tc\tcl2 \v AmrNbEncState}
{\xe \v AmrNbEncState}
{\bkmkstart AAAAAAAADU}
{\bkmkend AAAAAAAADU}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Public Attributes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

void * {\b encCtx}{\bkmkstart AAAAAAAADV}
{\bkmkend AAAAAAAADV}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

void * {\b pidSyncCtx}{\bkmkstart AAAAAAAADW}
{\bkmkend AAAAAAAADW}
\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
The documentation for this struct was generated from the following file:{\par
\pard\plain \s81\fi-360\li720\widctlpar\jclisttab\tx720{\*\pn \pnlvlbody\ilvl0\ls2\pnrnot0\pndec }\ls2\adjustright \fs20\cgrid 
pcac/amr/amrnb/enc/api/amrnb_enc_api.cpp\par
}\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
amrwb_dec_config Struct Reference\par \pard\plain 
{\tc\tcl2 \v amrwb_dec_config}
{\xe \v amrwb_dec_config}
{\bkmkstart AAAAAAAADX}
{\bkmkend AAAAAAAADX}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Public Attributes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
const char * {\b name}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
const char * {\b out_name}\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Member Data Documentation\par
\pard\plain 
{\xe \v name\:amrwb_dec_config}
{\xe \v amrwb_dec_config\:name}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
const char* amrwb_dec_config::name}}
\par
{\bkmkstart AAAAAAAADY}
{\bkmkend AAAAAAAADY}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
input wb-amr file name, specify it when reading from file is needed \par
}}
{\xe \v out_name\:amrwb_dec_config}
{\xe \v amrwb_dec_config\:out_name}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
const char* amrwb_dec_config::out_name}}
\par
{\bkmkstart AAAAAAAADZ}
{\bkmkend AAAAAAAADZ}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
output pcm file name, specify it when writing to file is needed \par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
The documentation for this struct was generated from the following file:{\par
\pard\plain \s81\fi-360\li720\widctlpar\jclisttab\tx720{\*\pn \pnlvlbody\ilvl0\ls2\pnrnot0\pndec }\ls2\adjustright \fs20\cgrid 
pcac/amr/amrwb/api/{\b amrwb_dec_api.h}\par
}\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
amrwb_dec_state Struct Reference\par \pard\plain 
{\tc\tcl2 \v amrwb_dec_state}
{\xe \v amrwb_dec_state}
{\bkmkstart AAAAAAAAEA}
{\bkmkend AAAAAAAAEA}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Public Attributes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

AUDIO_FILE_ID {\b fpInput}{\bkmkstart AAAAAAAAEB}
{\bkmkend AAAAAAAAEB}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

AUDIO_FILE_ID {\b fpOutput}{\bkmkstart AAAAAAAAEC}
{\bkmkend AAAAAAAAEC}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

void * {\b amrHandle}{\bkmkstart AAAAAAAAED}
{\bkmkend AAAAAAAAED}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

void * {\b decoderBuf}{\bkmkstart AAAAAAAAEE}
{\bkmkend AAAAAAAAEE}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

int16_t * {\b inputSampleBuf}{\bkmkstart AAAAAAAAEF}
{\bkmkend AAAAAAAAEF}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

int16_t * {\b decoderCookie}{\bkmkstart AAAAAAAAEG}
{\bkmkend AAAAAAAAEG}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

int16_t * {\b outputBuf}{\bkmkstart AAAAAAAAEH}
{\bkmkend AAAAAAAAEH}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

uint8_t * {\b inputBuf}{\bkmkstart AAAAAAAAEI}
{\bkmkend AAAAAAAAEI}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

int {\b frameSize}{\bkmkstart AAAAAAAAEJ}
{\bkmkend AAAAAAAAEJ}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

uint32_t {\b amrFrameSize}{\bkmkstart AAAAAAAAEK}
{\bkmkend AAAAAAAAEK}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

int16 {\b amrFrameMode}{\bkmkstart AAAAAAAAEL}
{\bkmkend AAAAAAAAEL}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

int16 {\b amrFrameType}{\bkmkstart AAAAAAAAEM}
{\bkmkend AAAAAAAAEM}
\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
The documentation for this struct was generated from the following file:{\par
\pard\plain \s81\fi-360\li720\widctlpar\jclisttab\tx720{\*\pn \pnlvlbody\ilvl0\ls2\pnrnot0\pndec }\ls2\adjustright \fs20\cgrid 
pcac/amr/amrwb/api/amrwb_dec_api.cpp\par
}
\pard\plain \sect\sbkpage
\s1\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs36\kerning36\cgrid 
File Documentation{\tc \v File Documentation}
\par \pard\plain 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
pcac/amr/amrnb/dec/api/amrnb_dec_api.h File Reference\par \pard\plain 
{\tc\tcl2 \v pcac/amr/amrnb/dec/api/amrnb_dec_api.h}
{\xe \v pcac/amr/amrnb/dec/api/amrnb_dec_api.h}
{\bkmkstart AAAAAAAAAA}
{\bkmkend AAAAAAAAAA}
\par
{
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR nb-amr decoder related API describes the process and functions used to decode nb-amr from file|memory to file|memory on ASR RTOS platform. }}\par
{
\pard\plain \s18\widctlpar\fs22\cgrid {\f2 #include <stdint.h>}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Classes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
struct {\b amrnb_dec_config}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Typedefs\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

typedef struct {\b amrnb_dec_config} {\b amrnb_dec_config}{\bkmkstart AAAAAAAAAB}
{\bkmkend AAAAAAAAAB}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
typedef uint32_t {\b amrnb_dec_handle}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Functions\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_decode_open} (const {\b amrnb_dec_config} *config, {\b amrnb_dec_handle} *handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_decode_read} ({\b amrnb_dec_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_decode_write} ({\b amrnb_dec_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_decode_loop} ({\b amrnb_dec_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_decode_do} ({\b amrnb_dec_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_decode_set} ({\b amrnb_dec_handle} handle, const uint8_t *data, uint32_t size)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_decode_get} ({\b amrnb_dec_handle} handle, int16_t *data, uint32_t *size)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_decode_get_amr} ({\b amrnb_dec_handle} handle, uint8_t *data, uint32_t *size)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_decode_get_rate} ({\b amrnb_dec_handle} handle, int32_t *rate)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_decode_close} ({\b amrnb_dec_handle} handle)\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Detailed Description\par
\pard\plain 
{
\pard\plain \s17\sa60\sb30\widctlpar\qj \fs22\cgrid {\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR nb-amr decoder related API describes the process and functions used to decode nb-amr from file|memory to file|memory on ASR RTOS platform. \par
}{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
\par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Typedef Documentation\par
\pard\plain 
{\xe \v amrnb_dec_handle\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_dec_handle}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
typedef uint32_t {\b amrnb_dec_handle}}}
\par
{\bkmkstart AAAAAAAAAC}
{\bkmkend AAAAAAAAAC}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
nb-amr decoder handle, held and used by nb-amr decoder user \par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Function Documentation\par
\pard\plain 
{\xe \v amrnb_decode_close\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_decode_close}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_decode_close ({\b amrnb_dec_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAD}
{\bkmkend AAAAAAAAAD}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Free nb-amr decoder resource. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_dec_handle} : nb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_decode_do\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_decode_do}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_decode_do ({\b amrnb_dec_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAE}
{\bkmkend AAAAAAAAAE}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Decode a nb-amr frame in memory to pcm frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_dec_handle} : nb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_decode_get\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_decode_get}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_decode_get ({\b amrnb_dec_handle}  {\i handle}, int16_t *  {\i data}, uint32_t *  {\i size})}}
\par
{\bkmkstart AAAAAAAAAF}
{\bkmkend AAAAAAAAAF}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Get a decoded pcm frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_dec_handle} : nb-amr decoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i data} \cell }{{\f2 int16_t*} : pcm frame address \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i size} \cell }{{\f2 uint32_t*} : pcm frame size \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_decode_get_amr\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_decode_get_amr}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_decode_get_amr ({\b amrnb_dec_handle}  {\i handle}, uint8_t *  {\i data}, uint32_t *  {\i size})}}
\par
{\bkmkstart AAAAAAAAAG}
{\bkmkend AAAAAAAAAG}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Get a nb-amr frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_dec_handle} : nb-amr decoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i data} \cell }{{\f2 uint8_t*} : nb-amr frame address \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i size} \cell }{{\f2 uint32_t*} : nb-amr frame size \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_decode_get_rate\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_decode_get_rate}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_decode_get_rate ({\b amrnb_dec_handle}  {\i handle}, int32_t *  {\i rate})}}
\par
{\bkmkstart AAAAAAAAAH}
{\bkmkend AAAAAAAAAH}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Get current nb-amr rate. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_dec_handle} : nb-amr decoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i rate} \cell }{{\f2 int32_t*} : nb-amr rate, 0 for 4.75 kbps \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_decode_loop\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_decode_loop}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_decode_loop ({\b amrnb_dec_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAI}
{\bkmkend AAAAAAAAAI}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Read a frame from nb-amr file, decode to pcm and write to pcm file if possible. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_dec_handle} : nb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_decode_open\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_decode_open}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_decode_open (const {\b amrnb_dec_config} *  {\i config}, {\b amrnb_dec_handle} *  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAJ}
{\bkmkend AAAAAAAAAJ}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Start nb-amr decoder with configuration. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i config} \cell }{{\f2 const amrnb_dec_config*} : nb-amr decoder configuration \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in,out\cell }{{\i handle} \cell }{{\f2 amrnb_dec_handle*} : nb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_decode_read\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_decode_read}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_decode_read ({\b amrnb_dec_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAK}
{\bkmkend AAAAAAAAAK}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Read a frame from nb-amr file into memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_dec_handle} : nb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_decode_set\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_decode_set}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_decode_set ({\b amrnb_dec_handle}  {\i handle}, const uint8_t *  {\i data}, uint32_t  {\i size})}}
\par
{\bkmkstart AAAAAAAAAL}
{\bkmkend AAAAAAAAAL}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Set a nb-amr frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_dec_handle} : nb-amr decoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i data} \cell }{{\f2 const uint8_t*} : nb-amr frame address \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i size} \cell }{{\f2 uint32_t} : nb-amr frame size \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_decode_write\:amrnb_dec_api.h}
{\xe \v amrnb_dec_api.h\:amrnb_decode_write}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_decode_write ({\b amrnb_dec_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAM}
{\bkmkend AAAAAAAAAM}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Write a frame from memory to pcm file. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_dec_handle} : nb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
pcac/amr/amrnb/enc/api/amrnb_enc_api.h File Reference\par \pard\plain 
{\tc\tcl2 \v pcac/amr/amrnb/enc/api/amrnb_enc_api.h}
{\xe \v pcac/amr/amrnb/enc/api/amrnb_enc_api.h}
{\bkmkstart AAAAAAAAAN}
{\bkmkend AAAAAAAAAN}
\par
{
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR nb-amr encoder related API describes the process and functions used to encode pcm from file|memory to file|memory on ASR RTOS platform. }}\par
{
\pard\plain \s18\widctlpar\fs22\cgrid {\f2 #include <stdint.h>}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Classes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
struct {\b amrnb_enc_config}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Typedefs\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

typedef struct {\b amrnb_enc_config} {\b amrnb_enc_config}{\bkmkstart AAAAAAAAAO}
{\bkmkend AAAAAAAAAO}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
typedef uint32_t {\b amrnb_enc_handle}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Functions\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_encode_open} (const {\b amrnb_enc_config} *config, {\b amrnb_enc_handle} *handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_encode_do} ({\b amrnb_enc_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_encode_loop} ({\b amrnb_enc_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_encode_read} ({\b amrnb_enc_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_encode_write} ({\b amrnb_enc_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_encode_set} ({\b amrnb_enc_handle} handle, const int16_t *input_data, uint32_t size)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_encode_get} ({\b amrnb_enc_handle} handle, uint8_t *output_data, uint32_t *size)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_encode_set_rate} (int32_t mode, {\b amrnb_enc_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrnb_encode_close} ({\b amrnb_enc_handle} handle)\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Detailed Description\par
\pard\plain 
{
\pard\plain \s17\sa60\sb30\widctlpar\qj \fs22\cgrid {\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR nb-amr encoder related API describes the process and functions used to encode pcm from file|memory to file|memory on ASR RTOS platform. \par
}{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
\par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Typedef Documentation\par
\pard\plain 
{\xe \v amrnb_enc_handle\:amrnb_enc_api.h}
{\xe \v amrnb_enc_api.h\:amrnb_enc_handle}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
typedef uint32_t {\b amrnb_enc_handle}}}
\par
{\bkmkstart AAAAAAAAAP}
{\bkmkend AAAAAAAAAP}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
nb-amr encoder handle, held and used by nb-amr encoder user \par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Function Documentation\par
\pard\plain 
{\xe \v amrnb_encode_close\:amrnb_enc_api.h}
{\xe \v amrnb_enc_api.h\:amrnb_encode_close}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_encode_close ({\b amrnb_enc_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAQ}
{\bkmkend AAAAAAAAAQ}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Free nb-amr encoder resource. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_enc_handle} : nb-amr encoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_encode_do\:amrnb_enc_api.h}
{\xe \v amrnb_enc_api.h\:amrnb_encode_do}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_encode_do ({\b amrnb_enc_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAR}
{\bkmkend AAAAAAAAAR}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Encode a pcm frame in memory to nb-amr frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_enc_handle} : nb-amr encoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_encode_get\:amrnb_enc_api.h}
{\xe \v amrnb_enc_api.h\:amrnb_encode_get}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_encode_get ({\b amrnb_enc_handle}  {\i handle}, uint8_t *  {\i output_data}, uint32_t *  {\i size})}}
\par
{\bkmkstart AAAAAAAAAS}
{\bkmkend AAAAAAAAAS}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Get a encoded nb-amr frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_enc_handle} : nb-amr encoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i data} \cell }{{\f2 uint8_t*} : nb-amr frame address \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i size} \cell }{{\f2 uint32_t*} : nb-amr frame size \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_encode_loop\:amrnb_enc_api.h}
{\xe \v amrnb_enc_api.h\:amrnb_encode_loop}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_encode_loop ({\b amrnb_enc_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAT}
{\bkmkend AAAAAAAAAT}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Read a frame from pcm file, encode to nb-amr frame and write to nb-amr file if possible. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_enc_handle} : nb-amr encoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_encode_open\:amrnb_enc_api.h}
{\xe \v amrnb_enc_api.h\:amrnb_encode_open}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_encode_open (const {\b amrnb_enc_config} *  {\i config}, {\b amrnb_enc_handle} *  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAU}
{\bkmkend AAAAAAAAAU}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Start nb-amr encoder with configuration. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i config} \cell }{{\f2 const amrnb_enc_config*} : nb-amr encoder configuration \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in,out\cell }{{\i handle} \cell }{{\f2 amrnb_enc_handle*} : nb-amr encoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_encode_read\:amrnb_enc_api.h}
{\xe \v amrnb_enc_api.h\:amrnb_encode_read}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_encode_read ({\b amrnb_enc_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAV}
{\bkmkend AAAAAAAAAV}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Read a frame from pcm file into memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_enc_handle} : nb-amr encoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_encode_set\:amrnb_enc_api.h}
{\xe \v amrnb_enc_api.h\:amrnb_encode_set}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_encode_set ({\b amrnb_enc_handle}  {\i handle}, const int16_t *  {\i input_data}, uint32_t  {\i size})}}
\par
{\bkmkstart AAAAAAAAAW}
{\bkmkend AAAAAAAAAW}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Set a pcm frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_enc_handle} : nb-amr encoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i data} \cell }{{\f2 const int16_t*} : pcm frame address \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i size} \cell }{{\f2 uint32_t} : pcm frame size \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_encode_set_rate\:amrnb_enc_api.h}
{\xe \v amrnb_enc_api.h\:amrnb_encode_set_rate}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_encode_set_rate (int32_t  {\i mode}, {\b amrnb_enc_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAX}
{\bkmkend AAAAAAAAAX}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Set current nb-amr rate. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_enc_handle} : nb-amr encoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i mode} \cell }{{\f2 int32_t} : nb-amr rate, 0 for 4.75 kbps \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrnb_encode_write\:amrnb_enc_api.h}
{\xe \v amrnb_enc_api.h\:amrnb_encode_write}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrnb_encode_write ({\b amrnb_enc_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAAAY}
{\bkmkend AAAAAAAAAY}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Write a frame from memory to nb-amr file. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrnb_enc_handle} : nb-amr encoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
pcac/amr/amrwb/api/amrwb_dec_api.h File Reference\par \pard\plain 
{\tc\tcl2 \v pcac/amr/amrwb/api/amrwb_dec_api.h}
{\xe \v pcac/amr/amrwb/api/amrwb_dec_api.h}
{\bkmkstart AAAAAAAAAZ}
{\bkmkend AAAAAAAAAZ}
\par
{
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR wb-amr decoder related API describes the process and functions used to decode wb-amr from file|memory to file|memory on ASR RTOS platform. }}\par
{
\pard\plain \s18\widctlpar\fs22\cgrid {\f2 #include <stdint.h>}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Classes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
struct {\b amrwb_dec_config}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Typedefs\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

typedef struct {\b amrwb_dec_config} {\b amrwb_dec_config}{\bkmkstart AAAAAAAABA}
{\bkmkend AAAAAAAABA}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
typedef uint32_t {\b amrwb_dec_handle}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Functions\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrwb_decode_open} (const {\b amrwb_dec_config} *config, {\b amrwb_dec_handle} *handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrwb_decode_read} ({\b amrwb_dec_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrwb_decode_write} ({\b amrwb_dec_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrwb_decode_loop} ({\b amrwb_dec_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrwb_decode_do} ({\b amrwb_dec_handle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrwb_decode_set} ({\b amrwb_dec_handle} handle, const uint8_t *data, uint32_t size)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrwb_decode_get} ({\b amrwb_dec_handle} handle, int16_t *output_data, uint32_t *size)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrwb_decode_get_amr} ({\b amrwb_dec_handle} handle, uint8_t *output_data, uint32_t *size)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrwb_decode_get_rate} ({\b amrwb_dec_handle} handle, int32_t *rate)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrwb_decode_close} ({\b amrwb_dec_handle} handle)\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Detailed Description\par
\pard\plain 
{
\pard\plain \s17\sa60\sb30\widctlpar\qj \fs22\cgrid {\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR wb-amr decoder related API describes the process and functions used to decode wb-amr from file|memory to file|memory on ASR RTOS platform. \par
}{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
\par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Typedef Documentation\par
\pard\plain 
{\xe \v amrwb_dec_handle\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_dec_handle}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
typedef uint32_t {\b amrwb_dec_handle}}}
\par
{\bkmkstart AAAAAAAABB}
{\bkmkend AAAAAAAABB}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
wb-amr decoder handle, held and used by wb-amr decoder user \par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Function Documentation\par
\pard\plain 
{\xe \v amrwb_decode_close\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_decode_close}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrwb_decode_close ({\b amrwb_dec_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAABC}
{\bkmkend AAAAAAAABC}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Free wb-amr decoder resource. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrwb_dec_handle} : wb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrwb_decode_do\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_decode_do}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrwb_decode_do ({\b amrwb_dec_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAABD}
{\bkmkend AAAAAAAABD}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Decode a wb-amr frame in memory to pcm frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrwb_dec_handle} : wb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrwb_decode_get\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_decode_get}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrwb_decode_get ({\b amrwb_dec_handle}  {\i handle}, int16_t *  {\i output_data}, uint32_t *  {\i size})}}
\par
{\bkmkstart AAAAAAAABE}
{\bkmkend AAAAAAAABE}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Get a decoded pcm frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrwb_dec_handle} : wb-amr decoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i data} \cell }{{\f2 int16_t*} : pcm frame address \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i size} \cell }{{\f2 uint32_t*} : pcm frame size \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrwb_decode_get_amr\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_decode_get_amr}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrwb_decode_get_amr ({\b amrwb_dec_handle}  {\i handle}, uint8_t *  {\i output_data}, uint32_t *  {\i size})}}
\par
{\bkmkstart AAAAAAAABF}
{\bkmkend AAAAAAAABF}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Get a wb-amr frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrwb_dec_handle} : wb-amr decoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i data} \cell }{{\f2 uint8_t*} : wb-amr frame address \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i size} \cell }{{\f2 uint32_t*} : wb-amr frame size \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrwb_decode_get_rate\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_decode_get_rate}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrwb_decode_get_rate ({\b amrwb_dec_handle}  {\i handle}, int32_t *  {\i rate})}}
\par
{\bkmkstart AAAAAAAABG}
{\bkmkend AAAAAAAABG}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Get current wb-amr rate. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrwb_dec_handle} : wb-amr decoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{out\cell }{{\i rate} \cell }{{\f2 int32_t*} : wb-amr rate \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrwb_decode_loop\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_decode_loop}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrwb_decode_loop ({\b amrwb_dec_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAABH}
{\bkmkend AAAAAAAABH}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Read a frame from wb-amr file, decode to pcm and write to pcm file if possible. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrwb_dec_handle} : wb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrwb_decode_open\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_decode_open}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrwb_decode_open (const {\b amrwb_dec_config} *  {\i config}, {\b amrwb_dec_handle} *  {\i handle})}}
\par
{\bkmkstart AAAAAAAABI}
{\bkmkend AAAAAAAABI}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Start wb-amr decoder with configuration. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i config} \cell }{{\f2 const amrwb_dec_config*} : wb-amr decoder configuration \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in,out\cell }{{\i handle} \cell }{{\f2 amrwb_dec_handle*} : wb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrwb_decode_read\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_decode_read}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrwb_decode_read ({\b amrwb_dec_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAABJ}
{\bkmkend AAAAAAAABJ}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Read a frame from wb-amr file into memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrwb_dec_handle} : wb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrwb_decode_set\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_decode_set}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrwb_decode_set ({\b amrwb_dec_handle}  {\i handle}, const uint8_t *  {\i data}, uint32_t  {\i size})}}
\par
{\bkmkstart AAAAAAAABK}
{\bkmkend AAAAAAAABK}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Set a wb-amr frame in memory. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrwb_dec_handle} : wb-amr decoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i data} \cell }{{\f2 const uint8_t*} : wb-amr frame address \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i size} \cell }{{\f2 uint32_t} : wb-amr frame size \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrwb_decode_write\:amrwb_dec_api.h}
{\xe \v amrwb_dec_api.h\:amrwb_decode_write}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrwb_decode_write ({\b amrwb_dec_handle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAABL}
{\bkmkend AAAAAAAABL}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Write a frame from memory to pcm file. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 amrwb_dec_handle} : wb-amr decoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
\par \pard\plain 

\pard\plain \sect\sbkpage
\s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
\pard\plain \s2\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs28\kerning28\cgrid 
pcac/amr/include/amr_vocoder_api.h File Reference\par \pard\plain 
{\tc\tcl2 \v pcac/amr/include/amr_vocoder_api.h}
{\xe \v pcac/amr/include/amr_vocoder_api.h}
{\bkmkstart AAAAAAAABM}
{\bkmkend AAAAAAAABM}
\par
{
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR amr related API describes the process and functions used to play from and encode into amr file on ASR RTOS platform. }}\par
{
\pard\plain \s18\widctlpar\fs22\cgrid {\f2 #include "acm_audio_def.h"}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Classes\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
struct {\b AmrFileConfigInfo}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
struct {\b AmrEncConfigInfo}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Typedefs\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

typedef enum {\b AmrFileStatus} {\b AmrFileStatus}{\bkmkstart AAAAAAAABN}
{\bkmkend AAAAAAAABN}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

typedef enum {\b AmrFileEventType} {\b AmrFileEventType}{\bkmkstart AAAAAAAABO}
{\bkmkend AAAAAAAABO}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

typedef void(* {\b AmrPlaybackEventCallback}) ({\b AmrFileEventType}, int){\bkmkstart AAAAAAAABP}
{\bkmkend AAAAAAAABP}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

typedef struct {\b AmrFileConfigInfo} {\b AmrFileConfigInfo}{\bkmkstart AAAAAAAABQ}
{\bkmkend AAAAAAAABQ}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 

typedef struct {\b AmrEncConfigInfo} {\b AmrEncConfigInfo}{\bkmkstart AAAAAAAABR}
{\bkmkend AAAAAAAABR}
\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
typedef uint32_t {\b AmrPlaybackHandle}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
typedef uint32_t {\b AmrEncodeHandle}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Enumerations\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
enum {\b AmrFileStatus} \{ {\b AMR_FILE_STATUS_ENDED} = 0, 
{\b AMR_FILE_STATUS_STARTED}
 \}\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
enum {\b AmrFileEventType} \{ {\b AMR_FILE_EVENT_STATUS}, 
{\b AMR_FILE_EVENT_RATE}
 \}\par
}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Functions\par
\pard\plain 

{
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrStart} (const char *name)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrStop} (void)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrPlayStart} (const char *file_name, const {\b AmrFileConfigInfo} *config, {\b AmrPlaybackHandle} *handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrPlayStop} ({\b AmrPlaybackHandle} handle, int drain)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrGetPCMOutHandle} ({\b AmrPlaybackHandle} handle, uint32_t *track_handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
void {\b amrPlayInit} (void)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrPlayStopWithName} (const char *file_name, int drain)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrEncStart} (const char *file_name, const {\b AmrEncConfigInfo} *config, {\b AmrEncodeHandle} *handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrEncStop} ({\b AmrEncodeHandle} handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrEncStopWithName} (const char *file_name)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
int {\b amrGetPCMInHandle} ({\b AmrEncodeHandle} handle, uint32_t *record_handle)\par
\pard\plain \s80\fi-360\li360\widctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls1\pnrnot0\pndec }\ls1\adjustright \fs20\cgrid 
void {\b amrEncInit} (void)\par
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Detailed Description\par
\pard\plain 
{
\pard\plain \s17\sa60\sb30\widctlpar\qj \fs22\cgrid {\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
ASR amr related API describes the process and functions used to play from and encode into amr file on ASR RTOS platform. \par
}{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
\par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Typedef Documentation\par
\pard\plain 
{\xe \v AmrEncodeHandle\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:AmrEncodeHandle}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
typedef uint32_t {\b AmrEncodeHandle}}}
\par
{\bkmkstart AAAAAAAABS}
{\bkmkend AAAAAAAABS}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
amr encoder handle, held and used by amr encoder user \par
}}
{\xe \v AmrPlaybackHandle\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:AmrPlaybackHandle}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
typedef uint32_t {\b AmrPlaybackHandle}}}
\par
{\bkmkstart AAAAAAAABT}
{\bkmkend AAAAAAAABT}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
amr playback handle, held and used by amr playback user \par
}}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Enumeration Type Documentation\par
\pard\plain 
{\xe \v AmrFileEventType\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:AmrFileEventType}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
enum {\b AmrFileEventType}}}
\par
{\bkmkstart AAAAAAAABU}
{\bkmkend AAAAAAAABU}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Enumerator:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx2187
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{\xe \v AMR_FILE_EVENT_STATUS\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:AMR_FILE_EVENT_STATUS}
{\qr AMR_FILE_EVENT_STATUS{\bkmkstart AAAAAAAABV}
{\bkmkend AAAAAAAABV}
\cell }{{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
status related event type, refer {\f2 AmrFileStatus}  for event value \par
}\cell }{\row }
{\xe \v AMR_FILE_EVENT_RATE\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:AMR_FILE_EVENT_RATE}
{\qr AMR_FILE_EVENT_RATE{\bkmkstart AAAAAAAABW}
{\bkmkend AAAAAAAABW}
\cell }{{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
amr rate event type, value ranges from 0 to 7 \par
}\cell }{\row }
}
}
{\xe \v AmrFileStatus\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:AmrFileStatus}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
enum {\b AmrFileStatus}}}
\par
{\bkmkstart AAAAAAAABX}
{\bkmkend AAAAAAAABX}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Enumerator:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx2187
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{\xe \v AMR_FILE_STATUS_ENDED\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:AMR_FILE_STATUS_ENDED}
{\qr AMR_FILE_STATUS_ENDED{\bkmkstart AAAAAAAABY}
{\bkmkend AAAAAAAABY}
\cell }{{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
indicate current file has been readed and played out \par
}\cell }{\row }
{\xe \v AMR_FILE_STATUS_STARTED\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:AMR_FILE_STATUS_STARTED}
{\qr AMR_FILE_STATUS_STARTED{\bkmkstart AAAAAAAABZ}
{\bkmkend AAAAAAAABZ}
\cell }{{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
indicate output device is opened \par
}\cell }{\row }
}
}
{\pard\widctlpar\brdrb\brdrs\brdrw5\brsp20 \adjustright \par}
\pard\plain \s3\sb240\sa60\keepn\widctlpar\adjustright \b\f1\cgrid 
Function Documentation\par
\pard\plain 
{\xe \v amrEncInit\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrEncInit}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
void amrEncInit (void )}}
\par
{\bkmkstart AAAAAAAACA}
{\bkmkend AAAAAAAACA}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Init amr encoder resource. \par
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Note:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid for amr encoder user do {\b not}  call this api! \par
}}}
{\xe \v amrEncStart\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrEncStart}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrEncStart (const char *  {\i file_name}, const {\b AmrEncConfigInfo} *  {\i config}, {\b AmrEncodeHandle} *  {\i handle})}}
\par
{\bkmkstart AAAAAAAACB}
{\bkmkend AAAAAAAACB}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Start amr encoder with configuration. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i file_name} \cell }{{\f2 const char*} : file name into which amr is encoded \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i config} \cell }{{\f2 const AmrEncConfigInfo*} : amr encoder configuration \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in,out\cell }{{\i handle} \cell }{{\f2 AmrEncodeHandle*} : amr encoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrEncStop\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrEncStop}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrEncStop ({\b AmrEncodeHandle}  {\i handle})}}
\par
{\bkmkstart AAAAAAAACC}
{\bkmkend AAAAAAAACC}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Close and free memory etc resource of an amr encoder instance specified by handle. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 AmrEncodeHandle} : amr encoder handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrEncStopWithName\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrEncStopWithName}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrEncStopWithName (const char *  {\i file_name})}}
\par
{\bkmkstart AAAAAAAACD}
{\bkmkend AAAAAAAACD}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Close and free memory etc resource of an amr encoder instance specified by name. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i file_name} \cell }{{\f2 const char*} : file name for amr encoder result \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrGetPCMInHandle\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrGetPCMInHandle}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrGetPCMInHandle ({\b AmrEncodeHandle}  {\i handle}, uint32_t *  {\i record_handle})}}
\par
{\bkmkstart AAAAAAAACE}
{\bkmkend AAAAAAAACE}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Get audio record handle, which is used to manipulate pcm stream. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 AmrEncodeHandle} : amr encoder handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i record_handle} \cell }{{\f2 uint32_t*} : audio record handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrGetPCMOutHandle\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrGetPCMOutHandle}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrGetPCMOutHandle ({\b AmrPlaybackHandle}  {\i handle}, uint32_t *  {\i track_handle})}}
\par
{\bkmkstart AAAAAAAACF}
{\bkmkend AAAAAAAACF}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Get audio track handle, which is used to manipulate pcm stream. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 AmrPlaybackHandle} : amr playback handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i track_handle} \cell }{{\f2 uint32_t*} : audio track handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrPlayInit\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrPlayInit}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
void amrPlayInit (void )}}
\par
{\bkmkstart AAAAAAAACG}
{\bkmkend AAAAAAAACG}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Init amr playback resource. \par
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Note:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid for amr playback user do {\b not}  call this api! \par
}}}
{\xe \v amrPlayStart\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrPlayStart}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrPlayStart (const char *  {\i file_name}, const {\b AmrFileConfigInfo} *  {\i config}, {\b AmrPlaybackHandle} *  {\i handle})}}
\par
{\bkmkstart AAAAAAAACH}
{\bkmkend AAAAAAAACH}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Start amr file playback with configuration. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i config} \cell }{{\f2 const AmrFileConfigInfo*} : amr playback configuration \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in,out\cell }{{\i handle} \cell }{{\f2 AmrPlaybackHandle*} : amr playback handle \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrPlayStop\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrPlayStop}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrPlayStop ({\b AmrPlaybackHandle}  {\i handle}, int  {\i drain})}}
\par
{\bkmkstart AAAAAAAACI}
{\bkmkend AAAAAAAACI}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Close and free memory etc resource of an amr playback instance specified by handle. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 AmrPlaybackHandle} : amr playback handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i drain} \cell }{{\f2 bool} : drain or not \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrPlayStopWithName\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrPlayStopWithName}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrPlayStopWithName (const char *  {\i file_name}, int  {\i drain})}}
\par
{\bkmkstart AAAAAAAACJ}
{\bkmkend AAAAAAAACJ}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Close and free memory etc resource of an amr playback instance specified by name. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i handle} \cell }{{\f2 AmrPlaybackHandle} : amr playback handle \cell }
{\row }
\trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i drain} \cell }{{\f2 bool} : drain or not \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrStart\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrStart}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrStart (const char *  {\i name})}}
\par
{\bkmkstart AAAAAAAACK}
{\bkmkend AAAAAAAACK}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Start amr file playback with default option, playback to near side. {\par
{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Parameters:\par}
\pard\plain \s61\li360\widctlpar\ql\adjustright \fs20\cgrid \trowd \trgaph108\trleft426\tblind426\trbrdrt\brdrs\brdrw10\brdrcf15 \trbrdrl\brdrs\brdrw10\brdrcf15 \trbrdrb\brdrs\brdrw10\brdrcf15 \trbrdrr\brdrs\brdrw10\brdrcf15 \trbrdrh\brdrs\brdrw10\brdrcf15 \trbrdrv\brdrs\brdrw10\brdrcf15 
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx1224
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx3061
\clvertalt\clbrdrt\brdrs\brdrw10\brdrcf15 \clbrdrl\brdrs\brdrw10\brdrcf15 \clbrdrb\brdrs\brdrw10\brdrcf15 \clbrdrr \brdrs\brdrw10\brdrcf15 \cltxlrtb \cellx8748
\pard \widctlpar\intbl\adjustright
{in\cell }{{\i name} \cell }{{\f2 const char*} : amr file name \cell }
{\row }
}
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}
{\xe \v amrStop\:amr_vocoder_api.h}
{\xe \v amr_vocoder_api.h\:amrStop}
\pard\plain \s4\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs20\cgrid {
{\b 
int amrStop (void )}}
\par
{\bkmkstart AAAAAAAACL}
{\bkmkend AAAAAAAACL}
{
\pard\plain \s51\li360\sa60\sb30\qj\widctlpar\qj\adjustright \fs20\cgrid 
{\s17\sa60\sb30\widctlpar\qj \fs22\cgrid 
Stop all the amr file playbacks ongoing. \par
{{\s5\sb90\sa30\keepn\widctlpar\adjustright \b\f1\fs20\cgrid 
Returns:\par}\pard\plain \s62\li720\widctlpar\ql\adjustright \fs20\cgrid error code in {\f2 int} , non-zero on failure \par
}}}

\pard\plain \sect\sbkpage
\s1\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs36\kerning36\cgrid 
\s1\sb240\sa60\keepn\widctlpar\adjustright \b\f1\fs36\kerning36\cgrid Index\par 
\pard\plain 
{\tc \v Index}
{\field\fldedit {\*\fldinst INDEX \\c2 \\*MERGEFORMAT}{\fldrslt INDEX}}
}