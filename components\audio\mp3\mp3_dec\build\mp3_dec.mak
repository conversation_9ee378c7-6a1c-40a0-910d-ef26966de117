#------------------------------------------------------------
# (C) Copyright [2006-2008] Marvell International Ltd.
# All Rights Reserved
#------------------------------------------------------------

#--------------------------------------------------------------------------------------------------------------------
# INTEL CONFIDENTIAL
# Copyright 2006 Intel Corporation All Rights Reserved.
# The source code contained or described herein and all documents related to the source code ("Material") are owned
# by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
# its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
# Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
# treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
# transmitted, distributed, or disclosed in any way without Intel's prior express written permission.
#
# No license under any patent, copyright, trade secret or other intellectual property right is granted to or
# conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
# estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
# Intel in writing.
# -------------------------------------------------------------------------------------------------------------------

#=========================================================================
# File Name      : mp3_dec.mak
# Description    : Main make file for the mp3_dec package.
#
# Usage          : make [-s] -f mp3_dec.mak OPT_FILE=<path>/<opt_file>
#
# Notes          : The options file defines macro values defined
#                  by the environment, target, and groups. It
#                  must be included for proper package building.
#
#
#
#=========================================================================

# Package build options
include ${OPT_FILE}

# Package Makefile information
GEN_PACK_MAKEFILE = ${BUILD_ROOT}/env/${HOST}/build/cpppackage.mak

# Define Package ---------------------------------------

PACKAGE_NAME     = mp3_dec
PACKAGE_BASE     = pcac
PACKAGE_PATH     = ${BUILD_ROOT}/${PACKAGE_BASE}/${PACKAGE_NAME}
#INCLUDE_UNIT_TEST = yes

# The relative path locations of source and include file directories.
PACKAGE_SRC_PATH    = ${PACKAGE_PATH}/src          \
                      ${PACKAGE_PATH}/src/api \
		      ${BUILD_ROOT}/quectel/open/common_api/audio

PACKAGE_DFLAGS   = -I${PACKAGE_PATH}/src                \
		      -I${PACKAGE_PATH}/include                 \
		   -I${PACKAGE_PATH}/src/api				\
		   -I${BUILD_ROOT}/quectel/open/common_api/include \
		   -I${BUILD_ROOT}/softutil/littlefs/inc 


# Package source files, paths not required
PACKAGE_SRC_FILES =    \
		   pvmp3_alias_reduction.cpp \
		   pvmp3_crc.cpp \
		   pvmp3_dct_6.cpp \
		   pvmp3_dct_9.cpp \
		   pvmp3_dct_16.cpp \
		   pvmp3_decode_header.cpp \
		   pvmp3_decode_huff_cw.cpp \
		   pvmp3_dequantize_sample.cpp \
		   pvmp3_equalizer.cpp \
		   pvmp3_framedecoder.cpp \
		   pvmp3_get_main_data_size.cpp \
		   pvmp3_get_scale_factors.cpp \
		   pvmp3_get_side_info.cpp \
		   pvmp3_getbits.cpp \
		   pvmp3_huffman_decoding.cpp \
		   pvmp3_huffman_parsing.cpp \
		   pvmp3_imdct_synth.cpp \
		   pvmp3_mdct_6.cpp \
		   pvmp3_mdct_18.cpp \
		   pvmp3_mpeg2_get_scale_data.cpp \
		   pvmp3_mpeg2_get_scale_factors.cpp \
		   pvmp3_mpeg2_stereo_proc.cpp \
		   pvmp3_normalize.cpp \
		   pvmp3_poly_phase_synthesis.cpp \
		   pvmp3_polyphase_filter_window.cpp \
		   pvmp3_reorder.cpp \
		   pvmp3_seek_synch.cpp \
		   pvmp3_stereo_proc.cpp \
		   pvmp3_tables.cpp

#test files
PACKAGE_SRC_FILES +=    \
		   mp3dec_api.cpp \
		   mp3reader.cpp

PACKAGE_CFLAGS  = -Otime --diag_suppress=2523
PACKAGE_ARFLAGS =
PACKAGE_DFLAGS  +=  \
		  -DOSCL_UNUSED_ARG(...)=0


# Include the Standard Package Make File ---------------
include ${GEN_PACK_MAKEFILE}

# Include the Make Dependency File ---------------------
# This must be the last line in the file
