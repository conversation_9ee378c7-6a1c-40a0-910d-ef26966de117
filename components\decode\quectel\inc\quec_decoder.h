
#ifndef _QUEC_DECODER_H_
#define _QUEC_DECODER_H_

#ifdef __cplusplus

/** C++ namespace for library interfaces */
namespace quec_decoder {
    extern "C" {
#endif


/** @name Global library interfaces */
/*@{*/

/** "color" of element: bar or space. */
typedef enum quec_decoder_color_e {
    QUEC_DECODER_SPACE = 0,    /**< light area or space between bars */
    QUEC_DECODER_BAR = 1,      /**< dark area or colored bar segment */
} quec_decoder_color_t;

/** decoded symbol type. */
typedef enum quec_decoder_symbol_type_e {
    QUEC_DECODER_NONE        =      0,  /**< no symbol decoded */
    QUEC_DECODER_PARTIAL     =      1,  /**< intermediate status */
    QUEC_DECODER_EAN8        =      8,  /**< EAN-8 */
    QUEC_DECODER_UPCE        =      9,  /**< UPC-E */
    QUEC_DECODER_ISBN10      =     10,  /**< ISBN-10 (from EAN-13). @since 0.4 */
    QUEC_DECODER_UPCA        =     12,  /**< UPC-A */
    QUEC_DECODER_EAN13       =     13,  /**< EAN-13 */
    QUEC_DECODER_ISBN13      =     14,  /**< ISBN-13 (from EAN-13). @since 0.4 */
    QUEC_DECODER_I25         =     25,  /**< Interleaved 2 of 5. @since 0.4 */
    QUEC_DECODER_CODE39      =     39,  /**< Code 39. @since 0.4 */
    QUEC_DECODER_PDF417      =     57,  /**< PDF417. @since 0.6 */
    QUEC_DECODER_QRCODE      =     64,  /**< QR Code. @since 0.10 */
    QUEC_DECODER_CODE128     =    128,  /**< Code 128 */
    QUEC_DECODER_SYMBOL      = 0x00ff,  /**< mask for base symbol type */
    QUEC_DECODER_ADDON2      = 0x0200,  /**< 2-digit add-on flag */
    QUEC_DECODER_ADDON5      = 0x0500,  /**< 5-digit add-on flag */
    QUEC_DECODER_ADDON       = 0x0700,  /**< add-on flag mask */
} quec_decoder_symbol_type_t;

/** error codes. */
typedef enum quec_decoder_error_e {
    QUEC_DECODER_OK = 0,                /**< no error */
    QUEC_DECODER_ERR_NOMEM,             /**< out of memory */
    QUEC_DECODER_ERR_INTERNAL,          /**< internal library error */
    QUEC_DECODER_ERR_UNSUPPORTED,       /**< unsupported request */
    QUEC_DECODER_ERR_INVALID,           /**< invalid request */
    QUEC_DECODER_ERR_SYSTEM,            /**< system error */
    QUEC_DECODER_ERR_LOCKING,           /**< locking error */
    QUEC_DECODER_ERR_BUSY,              /**< all resources busy */
    QUEC_DECODER_ERR_XDISPLAY,          /**< X11 display error */
    QUEC_DECODER_ERR_XPROTO,            /**< X11 protocol error */
    QUEC_DECODER_ERR_CLOSED,            /**< output window is closed */
    QUEC_DECODER_ERR_WINAPI,            /**< windows system error */
    QUEC_DECODER_ERR_NUM                /**< number of error codes */
} quec_decoder_error_t;

/** decoder configuration options.
 * @since 0.4
 */
typedef enum quec_decoder_config_e {
    QUEC_DECODER_CFG_ENABLE = 0,        /**< enable symbology/feature */
    QUEC_DECODER_CFG_ADD_CHECK,         /**< enable check digit when optional */
    QUEC_DECODER_CFG_EMIT_CHECK,        /**< return check digit when present */
    QUEC_DECODER_CFG_ASCII,             /**< enable full ASCII character set */
    QUEC_DECODER_CFG_NUM,               /**< number of boolean decoder configs */

    QUEC_DECODER_CFG_MIN_LEN = 0x20,    /**< minimum data length for valid decode */
    QUEC_DECODER_CFG_MAX_LEN,           /**< maximum data length for valid decode */

    QUEC_DECODER_CFG_POSITION = 0x80,   /**< enable scanner to collect position data */

    QUEC_DECODER_CFG_X_DENSITY = 0x100, /**< image scanner vertical scan density */
    QUEC_DECODER_CFG_Y_DENSITY,         /**< image scanner horizontal scan density */
} quec_decoder_config_t;

#define BARCODE 0
#define QRCODE 1

/** retrieve runtime library version information.
 * @param major set to the running major version (unless NULL)
 * @param minor set to the running minor version (unless NULL)
 * @returns 0
 */
extern int quec_decoder_version(unsigned *major,
                        unsigned *minor);

/** set global library debug level.
 * @param verbosity desired debug level.  higher values create more spew
 */
extern void quec_decoder_set_verbosity(int verbosity);

/** increase global library debug level.
 * eg, for -vvvv
 */
extern void quec_decoder_increase_verbosity(void);

/** retrieve string name for symbol encoding.
 * @param sym symbol type encoding
 * @returns the static string name for the specified symbol type,
 * or "UNKNOWN" if the encoding is not recognized
 */
extern const char *quec_decoder_get_symbol_name(quec_decoder_symbol_type_t sym);

/** retrieve string name for addon encoding.
 * @param sym symbol type encoding
 * @returns static string name for any addon, or the empty string
 * if no addons were decoded
 */
extern const char *quec_decoder_get_addon_name(quec_decoder_symbol_type_t sym);

/** parse a configuration string of the form "[symbology.]config[=value]".
 * the config must match one of the recognized names.
 * the symbology, if present, must match one of the recognized names.
 * if symbology is unspecified, it will be set to 0.
 * if value is unspecified it will be set to 1.
 * @returns 0 if the config is parsed successfully, 1 otherwise
 * @since 0.4
 */
extern int quec_decoder_parse_config(const char *config_string,
                             quec_decoder_symbol_type_t *symbology,
                             quec_decoder_config_t *config,
                             int *value);

/** @internal type unsafe error API (don't use) */
extern int _quec_decoder_error_spew(const void *object,
                            int verbosity);
extern const char *_quec_decoder_error_string(const void *object,
                                      int verbosity);
extern quec_decoder_error_t _quec_decoder_get_error_code(const void *object);

/*@}*/

struct quec_decoder_symbol_s;
typedef struct quec_decoder_symbol_s quec_decoder_symbol_t;

struct quec_decoder_symbol_set_s;
typedef struct quec_decoder_symbol_set_s quec_decoder_symbol_set_t;


/*------------------------------------------------------------*/
/** @name Symbol interface
 * decoded barcode symbol result object.  stores type, data, and image
 * location of decoded symbol.  all memory is owned by the library
 */
/*@{*/

/** @typedef quec_decoder_symbol_t
 * opaque decoded symbol object.
 */

/** symbol reference count manipulation.
 * increment the reference count when you store a new reference to the
 * symbol.  decrement when the reference is no longer used.  do not
 * refer to the symbol once the count is decremented and the
 * containing image has been recycled or destroyed.
 * @note the containing image holds a reference to the symbol, so you
 * only need to use this if you keep a symbol after the image has been
 * destroyed or reused.
 * @since 0.9
 */
extern void quec_decoder_symbol_ref(const quec_decoder_symbol_t *symbol,
                            int refs);

/** retrieve type of decoded symbol.
 * @returns the symbol type
 */
extern quec_decoder_symbol_type_t quec_decoder_symbol_get_type(const quec_decoder_symbol_t *symbol);

/** retrieve data decoded from symbol.
 * @returns the data string
 */
extern const char *quec_decoder_symbol_get_data(const quec_decoder_symbol_t *symbol);

/** retrieve length of binary data.
 * @returns the length of the decoded data
 */
extern unsigned int quec_decoder_symbol_get_data_length(const quec_decoder_symbol_t *symbol);

/** retrieve a symbol confidence metric.
 * @returns an unscaled, relative quantity: larger values are better
 * than smaller values, where "large" and "small" are application
 * dependent.
 * @note expect the exact definition of this quantity to change as the
 * metric is refined.  currently, only the ordered relationship
 * between two values is defined and will remain stable in the future
 * @since 0.9
 */
extern int quec_decoder_symbol_get_quality(const quec_decoder_symbol_t *symbol);

/** retrieve current cache count.  when the cache is enabled for the
 * image_scanner this provides inter-frame reliability and redundancy
 * information for video streams.
 * @returns < 0 if symbol is still uncertain.
 * @returns 0 if symbol is newly verified.
 * @returns > 0 for duplicate symbols
 */
extern int quec_decoder_symbol_get_count(const quec_decoder_symbol_t *symbol);

/** retrieve the number of points in the location polygon.  the
 * location polygon defines the image area that the symbol was
 * extracted from.
 * @returns the number of points in the location polygon
 * @note this is currently not a polygon, but the scan locations
 * where the symbol was decoded
 */
extern unsigned quec_decoder_symbol_get_loc_size(const quec_decoder_symbol_t *symbol);

/** retrieve location polygon x-coordinates.
 * points are specified by 0-based index.
 * @returns the x-coordinate for a point in the location polygon.
 * @returns -1 if index is out of range
 */
extern int quec_decoder_symbol_get_loc_x(const quec_decoder_symbol_t *symbol,
                                 unsigned index);

/** retrieve location polygon y-coordinates.
 * points are specified by 0-based index.
 * @returns the y-coordinate for a point in the location polygon.
 * @returns -1 if index is out of range
 */
extern int quec_decoder_symbol_get_loc_y(const quec_decoder_symbol_t *symbol,
                                 unsigned index);

/** iterate the set to which this symbol belongs (there can be only one).
 * @returns the next symbol in the set, or
 * @returns NULL when no more results are available
 */
extern const quec_decoder_symbol_t *quec_decoder_symbol_next(const quec_decoder_symbol_t *symbol);

/** retrieve components of a composite result.
 * @returns the symbol set containing the components
 * @returns NULL if the symbol is already a physical symbol
 * @since 0.10
 */
extern const quec_decoder_symbol_set_t*
quec_decoder_symbol_get_components(const quec_decoder_symbol_t *symbol);

/** iterate components of a composite result.
 * @returns the first physical component symbol of a composite result
 * @returns NULL if the symbol is already a physical symbol
 * @since 0.10
 */
extern const quec_decoder_symbol_t*
quec_decoder_symbol_first_component(const quec_decoder_symbol_t *symbol);

/** print XML symbol element representation to user result buffer.
 * @see http://quec_decoder.sourceforge.net/2008/barcode.xsd for the schema.
 * @param symbol is the symbol to print
 * @param buffer is the inout result pointer, it will be reallocated
 * with a larger size if necessary.
 * @param buflen is inout length of the result buffer.
 * @returns the buffer pointer
 * @since 0.6
 */
extern char *quec_decoder_symbol_xml(const quec_decoder_symbol_t *symbol,
                             char **buffer,
                             unsigned *buflen);

/*@}*/

/*------------------------------------------------------------*/
/** @name Symbol Set interface
 * container for decoded result symbols associated with an image
 * or a composite symbol.
 * @since 0.10
 */
/*@{*/

/** @typedef quec_decoder_symbol_set_t
 * opaque symbol iterator object.
 * @since 0.10
 */

/** reference count manipulation.
 * increment the reference count when you store a new reference.
 * decrement when the reference is no longer used.  do not refer to
 * the object any longer once references have been released.
 * @since 0.10
 */
extern void quec_decoder_symbol_set_ref(const quec_decoder_symbol_set_t *symbols,
                                int refs);

/** retrieve set size.
 * @returns the number of symbols in the set.
 * @since 0.10
 */
extern int quec_decoder_symbol_set_get_size(const quec_decoder_symbol_set_t *symbols);

/** set iterator.
 * @returns the first decoded symbol result in a set
 * @returns NULL if the set is empty
 * @since 0.10
 */
extern const quec_decoder_symbol_t*
quec_decoder_symbol_set_first_symbol(const quec_decoder_symbol_set_t *symbols);

/*@}*/

/*------------------------------------------------------------*/
/** @name Image interface
 * stores image data samples along with associated format and size
 * metadata
 */
/*@{*/

struct quec_decoder_image_s;
/** opaque image object. */
typedef struct quec_decoder_image_s quec_decoder_image_t;

/** cleanup handler callback function.
 * called to free sample data when an image is destroyed.
 */
typedef void (quec_decoder_image_cleanup_handler_t)(quec_decoder_image_t *image);

/** data handler callback function.
 * called when decoded symbol results are available for an image
 */
typedef void (quec_decoder_image_data_handler_t)(quec_decoder_image_t *image,
                                         const void *userdata);

/** new image constructor.
 * @returns a new image object with uninitialized data and format.
 * this image should be destroyed (using quec_decoder_image_destroy()) as
 * soon as the application is finished with it
 */
extern quec_decoder_image_t *quec_decoder_image_create(void);

/** image destructor.  all images created by or returned to the
 * application should be destroyed using this function.  when an image
 * is destroyed, the associated data cleanup handler will be invoked
 * if available
 * @note make no assumptions about the image or the data buffer.
 * they may not be destroyed/cleaned immediately if the library
 * is still using them.  if necessary, use the cleanup handler hook
 * to keep track of image data buffers
 */
extern void quec_decoder_image_destroy(quec_decoder_image_t *image);

/** image reference count manipulation.
 * increment the reference count when you store a new reference to the
 * image.  decrement when the reference is no longer used.  do not
 * refer to the image any longer once the count is decremented.
 * quec_decoder_image_ref(image, -1) is the same as quec_decoder_image_destroy(image)
 * @since 0.5
 */
extern void quec_decoder_image_ref(quec_decoder_image_t *image,
                           int refs);

/** image format conversion.  refer to the documentation for supported
 * image formats
 * @returns a @em new image with the sample data from the original image
 * converted to the requested format.  the original image is
 * unaffected.
 * @note the converted image size may be rounded (up) due to format
 * constraints
 */
extern quec_decoder_image_t *quec_decoder_image_convert(const quec_decoder_image_t *image,
                                        unsigned long format);

/** image format conversion with crop/pad.
 * if the requested size is larger than the image, the last row/column
 * are duplicated to cover the difference.  if the requested size is
 * smaller than the image, the extra rows/columns are dropped from the
 * right/bottom.
 * @returns a @em new image with the sample data from the original
 * image converted to the requested format and size.
 * @note the image is @em not scaled
 * @see quec_decoder_image_convert()
 * @since 0.4
 */
extern quec_decoder_image_t *quec_decoder_image_convert_resize(const quec_decoder_image_t *image,
                                               unsigned long format,
                                               unsigned width,
                                               unsigned height);

/** retrieve the image format.
 * @returns the fourcc describing the format of the image sample data
 */
extern unsigned long quec_decoder_image_get_format(const quec_decoder_image_t *image);

/** retrieve a "sequence" (page/frame) number associated with this image.
 * @since 0.6
 */
extern unsigned quec_decoder_image_get_sequence(const quec_decoder_image_t *image);

/** retrieve the width of the image.
 * @returns the width in sample columns
 */
extern unsigned quec_decoder_image_get_width(const quec_decoder_image_t *image);

/** retrieve the height of the image.
 * @returns the height in sample rows
 */
extern unsigned quec_decoder_image_get_height(const quec_decoder_image_t *image);

/** return the image sample data.  the returned data buffer is only
 * valid until quec_decoder_image_destroy() is called
 */
extern const void *quec_decoder_image_get_data(const quec_decoder_image_t *image);

/** return the size of image data.
 * @since 0.6
 */
extern unsigned long quec_decoder_image_get_data_length(const quec_decoder_image_t *img);

/** retrieve the decoded results.
 * @returns the (possibly empty) set of decoded symbols
 * @returns NULL if the image has not been scanned
 * @since 0.10
 */
extern const quec_decoder_symbol_set_t*
quec_decoder_image_get_symbols(const quec_decoder_image_t *image);

/** associate the specified symbol set with the image, replacing any
 * existing results.  use NULL to release the current results from the
 * image.
 * @see quec_decoder_image_scanner_recycle_image()
 * @since 0.10
 */
extern void quec_decoder_image_set_symbols(quec_decoder_image_t *image,
                                   const quec_decoder_symbol_set_t *symbols);

/** image_scanner decode result iterator.
 * @returns the first decoded symbol result for an image
 * or NULL if no results are available
 */
extern const quec_decoder_symbol_t*
quec_decoder_image_first_symbol(const quec_decoder_image_t *image);

/** specify the fourcc image format code for image sample data.
 * refer to the documentation for supported formats.
 * @note this does not convert the data!
 * (see quec_decoder_image_convert() for that)
 */
extern void quec_decoder_image_set_format(quec_decoder_image_t *image,
                                  unsigned long format);

/** associate a "sequence" (page/frame) number with this image.
 * @since 0.6
 */
extern void quec_decoder_image_set_sequence(quec_decoder_image_t *image,
                                    unsigned sequence_num);

/** specify the pixel size of the image.
 * @note this does not affect the data!
 */
extern void quec_decoder_image_set_size(quec_decoder_image_t *image,
                                unsigned width,
                                unsigned height);

/** specify image sample data.  when image data is no longer needed by
 * the library the specific data cleanup handler will be called
 * (unless NULL)
 * @note application image data will not be modified by the library
 */
extern void quec_decoder_image_set_data(quec_decoder_image_t *image,
                                const void *data,
                                unsigned long data_byte_length,
                                quec_decoder_image_cleanup_handler_t *cleanup_hndlr);

/** built-in cleanup handler.
 * passes the image data buffer to free()
 */
extern void quec_decoder_image_free_data(quec_decoder_image_t *image);

/** associate user specified data value with an image.
 * @since 0.5
 */
extern void quec_decoder_image_set_userdata(quec_decoder_image_t *image,
                                    void *userdata);

/** return user specified data value associated with the image.
 * @since 0.5
 */
extern void *quec_decoder_image_get_userdata(const quec_decoder_image_t *image);

/** dump raw image data to a file for debug.
 * the data will be prefixed with a 16 byte header consisting of:
 *   - 4 bytes uint = 0x676d697a ("zimg")
 *   - 4 bytes format fourcc
 *   - 2 bytes width
 *   - 2 bytes height
 *   - 4 bytes size of following image data in bytes
 * this header can be dumped w/eg:
 * @verbatim
       od -Ax -tx1z -N16 -w4 [file]
@endverbatim
 * for some formats the image can be displayed/converted using
 * ImageMagick, eg:
 * @verbatim
       display -size 640x480+16 [-depth ?] [-sampling-factor ?x?] \
           {GRAY,RGB,UYVY,YUV}:[file]
@endverbatim
 *
 * @param image the image object to dump
 * @param filebase base filename, appended with ".XXXX.zimg" where
 * XXXX is the format fourcc
 * @returns 0 on success or a system error code on failure
 */

/*@}*/

/*------------------------------------------------------------*/
/** @name Processor interface
 * @anchor c-processor
 * high-level self-contained image processor.
 * processes video and images for barcodes, optionally displaying
 * images to a library owned output window
 */
/*@{*/

struct quec_decoder_processor_s;
/** opaque standalone processor object. */
typedef struct quec_decoder_processor_s quec_decoder_processor_t;

/** constructor.
 * if threaded is set and threading is available the processor
 * will spawn threads where appropriate to avoid blocking and
 * improve responsiveness
 */
extern quec_decoder_processor_t *quec_decoder_processor_create(int threaded);

/** destructor.  cleans up all resources associated with the processor
 */
extern void quec_decoder_processor_destroy(quec_decoder_processor_t *processor);

/** (re)initialization.
 * opens a video input device and/or prepares to display output
 */
extern int quec_decoder_processor_init(quec_decoder_processor_t *processor,
                               const char *video_device,
                               int enable_display);

/** request a preferred size for the video image from the device.
 * the request may be adjusted or completely ignored by the driver.
 * @note must be called before quec_decoder_processor_init()
 * @since 0.6
 */
extern int quec_decoder_processor_request_size(quec_decoder_processor_t *processor,
                                       unsigned width,
                                       unsigned height);

/** request a preferred video driver interface version for
 * debug/testing.
 * @note must be called before quec_decoder_processor_init()
 * @since 0.6
 */
extern int quec_decoder_processor_request_interface(quec_decoder_processor_t *processor,
                                            int version);

/** request a preferred video I/O mode for debug/testing.  You will
 * get errors if the driver does not support the specified mode.
 * @verbatim
    0 = auto-detect
    1 = force I/O using read()
    2 = force memory mapped I/O using mmap()
    3 = force USERPTR I/O (v4l2 only)
@endverbatim
 * @note must be called before quec_decoder_processor_init()
 * @since 0.7
 */
extern int quec_decoder_processor_request_iomode(quec_decoder_processor_t *video,
                                         int iomode);

/** force specific input and output formats for debug/testing.
 * @note must be called before quec_decoder_processor_init()
 */
extern int quec_decoder_processor_force_format(quec_decoder_processor_t *processor,
                                       unsigned long input_format,
                                       unsigned long output_format);

/** setup result handler callback.
 * the specified function will be called by the processor whenever
 * new results are available from the video stream or a static image.
 * pass a NULL value to disable callbacks.
 * @param processor the object on which to set the handler.
 * @param handler the function to call when new results are available.
 * @param userdata is set as with quec_decoder_processor_set_userdata().
 * @returns the previously registered handler
 */
extern quec_decoder_image_data_handler_t*
quec_decoder_processor_set_data_handler(quec_decoder_processor_t *processor,
                                quec_decoder_image_data_handler_t *handler,
                                const void *userdata);

/** associate user specified data value with the processor.
 * @since 0.6
 */
extern void quec_decoder_processor_set_userdata(quec_decoder_processor_t *processor,
                                        void *userdata);

/** return user specified data value associated with the processor.
 * @since 0.6
 */
extern void *quec_decoder_processor_get_userdata(const quec_decoder_processor_t *processor);

/** set config for indicated symbology (0 for all) to specified value.
 * @returns 0 for success, non-0 for failure (config does not apply to
 * specified symbology, or value out of range)
 * @see quec_decoder_decoder_set_config()
 * @since 0.4
 */
extern int quec_decoder_processor_set_config(quec_decoder_processor_t *processor,
                                     quec_decoder_symbol_type_t symbology,
                                     quec_decoder_config_t config,
                                     int value);

/** parse configuration string using quec_decoder_parse_config()
 * and apply to processor using quec_decoder_processor_set_config().
 * @returns 0 for success, non-0 for failure
 * @see quec_decoder_parse_config()
 * @see quec_decoder_processor_set_config()
 * @since 0.4
 */
static inline int quec_decoder_processor_parse_config (quec_decoder_processor_t *processor,
                                               const char *config_string)
{
    quec_decoder_symbol_type_t sym;
    quec_decoder_config_t cfg;
    int val;
    return(quec_decoder_parse_config(config_string, &sym, &cfg, &val) ||
           quec_decoder_processor_set_config(processor, sym, cfg, val));
}

/** retrieve the current state of the ouput window.
 * @returns 1 if the output window is currently displayed, 0 if not.
 * @returns -1 if an error occurs
 */
extern int quec_decoder_processor_is_visible(quec_decoder_processor_t *processor);

/** show or hide the display window owned by the library.
 * the size will be adjusted to the input size
 */
extern int quec_decoder_processor_set_visible(quec_decoder_processor_t *processor,
                                      int visible);

/** control the processor in free running video mode.
 * only works if video input is initialized. if threading is in use,
 * scanning will occur in the background, otherwise this is only
 * useful wrapping calls to quec_decoder_processor_user_wait(). if the
 * library output window is visible, video display will be enabled.
 */
extern int quec_decoder_processor_set_active(quec_decoder_processor_t *processor,
                                     int active);

/** retrieve decode results for last scanned image/frame.
 * @returns the symbol set result container or NULL if no results are
 * available
 * @note the returned symbol set has its reference count incremented;
 * ensure that the count is decremented after use
 * @since 0.10
 */
extern const quec_decoder_symbol_set_t*
quec_decoder_processor_get_results(const quec_decoder_processor_t *processor);

/** wait for input to the display window from the user
 * (via mouse or keyboard).
 * @returns >0 when input is received, 0 if timeout ms expired
 * with no input or -1 in case of an error
 */
extern int quec_decoder_processor_user_wait(quec_decoder_processor_t *processor,
                                    int timeout);

/** process from the video stream until a result is available,
 * or the timeout (in milliseconds) expires.
 * specify a timeout of -1 to scan indefinitely
 * (quec_decoder_processor_set_active() may still be used to abort the scan
 * from another thread).
 * if the library window is visible, video display will be enabled.
 * @note that multiple results may still be returned (despite the
 * name).
 * @returns >0 if symbols were successfully decoded,
 * 0 if no symbols were found (ie, the timeout expired)
 * or -1 if an error occurs
 */
extern int quec_decoder_process_one(quec_decoder_processor_t *processor,
                            int timeout);

/** process the provided image for barcodes.
 * if the library window is visible, the image will be displayed.
 * @returns >0 if symbols were successfully decoded,
 * 0 if no symbols were found or -1 if an error occurs
 */
extern int quec_decoder_process_image(quec_decoder_processor_t *processor,
                              quec_decoder_image_t *image);

/** display detail for last processor error to stderr.
 * @returns a non-zero value suitable for passing to exit()
 */
static inline int
quec_decoder_processor_error_spew (const quec_decoder_processor_t *processor,
                           int verbosity)
{
    return(_quec_decoder_error_spew(processor, verbosity));
}

/** retrieve the detail string for the last processor error. */
static inline const char*
quec_decoder_processor_error_string (const quec_decoder_processor_t *processor,
                             int verbosity)
{
    return(_quec_decoder_error_string(processor, verbosity));
}

/** retrieve the type code for the last processor error. */
static inline quec_decoder_error_t
quec_decoder_processor_get_error_code (const quec_decoder_processor_t *processor)
{
    return(_quec_decoder_get_error_code(processor));
}

/*@}*/

/*------------------------------------------------------------*/
/** @name Video interface
 * @anchor c-video
 * mid-level video source abstraction.
 * captures images from a video device
 */
/*@{*/

struct quec_decoder_video_s;
/** opaque video object. */
typedef struct quec_decoder_video_s quec_decoder_video_t;

/** constructor. */
extern quec_decoder_video_t *quec_decoder_video_create(void);

/** destructor. */
extern void quec_decoder_video_destroy(quec_decoder_video_t *video);

/** open and probe a video device.
 * the device specified by platform specific unique name
 * (v4l device node path in *nix eg "/dev/video",
 *  DirectShow DevicePath property in windows).
 * @returns 0 if successful or -1 if an error occurs
 */
extern int quec_decoder_video_open(quec_decoder_video_t *video,
                           const char *device);

/** retrieve file descriptor associated with open *nix video device
 * useful for using select()/poll() to tell when new images are
 * available (NB v4l2 only!!).
 * @returns the file descriptor or -1 if the video device is not open
 * or the driver only supports v4l1
 */
extern int quec_decoder_video_get_fd(const quec_decoder_video_t *video);

/** request a preferred size for the video image from the device.
 * the request may be adjusted or completely ignored by the driver.
 * @returns 0 if successful or -1 if the video device is already
 * initialized
 * @since 0.6
 */
extern int quec_decoder_video_request_size(quec_decoder_video_t *video,
                                   unsigned width,
                                   unsigned height);

/** request a preferred driver interface version for debug/testing.
 * @note must be called before quec_decoder_video_open()
 * @since 0.6
 */
extern int quec_decoder_video_request_interface(quec_decoder_video_t *video,
                                        int version);

/** request a preferred I/O mode for debug/testing.  You will get
 * errors if the driver does not support the specified mode.
 * @verbatim
    0 = auto-detect
    1 = force I/O using read()
    2 = force memory mapped I/O using mmap()
    3 = force USERPTR I/O (v4l2 only)
@endverbatim
 * @note must be called before quec_decoder_video_open()
 * @since 0.7
 */
extern int quec_decoder_video_request_iomode(quec_decoder_video_t *video,
                                     int iomode);

/** retrieve current output image width.
 * @returns the width or 0 if the video device is not open
 */
extern int quec_decoder_video_get_width(const quec_decoder_video_t *video);

/** retrieve current output image height.
 * @returns the height or 0 if the video device is not open
 */
extern int quec_decoder_video_get_height(const quec_decoder_video_t *video);

/** initialize video using a specific format for debug.
 * use quec_decoder_negotiate_format() to automatically select and initialize
 * the best available format
 */
extern int quec_decoder_video_init(quec_decoder_video_t *video,
                           unsigned long format);

/** start/stop video capture.
 * all buffered images are retired when capture is disabled.
 * @returns 0 if successful or -1 if an error occurs
 */
extern int quec_decoder_video_enable(quec_decoder_video_t *video,
                             int enable);

/** retrieve next captured image.  blocks until an image is available.
 * @returns NULL if video is not enabled or an error occurs
 */
extern quec_decoder_image_t *quec_decoder_video_next_image(quec_decoder_video_t *video);

/** display detail for last video error to stderr.
 * @returns a non-zero value suitable for passing to exit()
 */
static inline int quec_decoder_video_error_spew (const quec_decoder_video_t *video,
                                         int verbosity)
{
    return(_quec_decoder_error_spew(video, verbosity));
}

/** retrieve the detail string for the last video error. */
static inline const char *quec_decoder_video_error_string (const quec_decoder_video_t *video,
                                                   int verbosity)
{
    return(_quec_decoder_error_string(video, verbosity));
}

/** retrieve the type code for the last video error. */
static inline quec_decoder_error_t
quec_decoder_video_get_error_code (const quec_decoder_video_t *video)
{
    return(_quec_decoder_get_error_code(video));
}

/*@}*/

/*------------------------------------------------------------*/
/** @name Window interface
 * @anchor c-window
 * mid-level output window abstraction.
 * displays images to user-specified platform specific output window
 */
/*@{*/

struct quec_decoder_window_s;
/** opaque window object. */
typedef struct quec_decoder_window_s quec_decoder_window_t;

/** constructor. */
extern quec_decoder_window_t *quec_decoder_window_create(void);

/** destructor. */
extern void quec_decoder_window_destroy(quec_decoder_window_t *window);

/** associate reader with an existing platform window.
 * This can be any "Drawable" for X Windows or a "HWND" for windows.
 * input images will be scaled into the output window.
 * pass NULL to detach from the resource, further input will be
 * ignored
 */
extern int quec_decoder_window_attach(quec_decoder_window_t *window,
                              void *x11_display_w32_hwnd,
                              unsigned long x11_drawable);

/** control content level of the reader overlay.
 * the overlay displays graphical data for informational or debug
 * purposes.  higher values increase the level of annotation (possibly
 * decreasing performance). @verbatim
    0 = disable overlay
    1 = outline decoded symbols (default)
    2 = also track and display input frame rate
@endverbatim
 */
extern void quec_decoder_window_set_overlay(quec_decoder_window_t *window,
                                    int level);

/** retrieve current content level of reader overlay.
 * @see quec_decoder_window_set_overlay()
 * @since 0.10
 */
extern int quec_decoder_window_get_overlay(const quec_decoder_window_t *window);

/** draw a new image into the output window. */
extern int quec_decoder_window_draw(quec_decoder_window_t *window,
                            quec_decoder_image_t *image);

/** redraw the last image (exposure handler). */
extern int quec_decoder_window_redraw(quec_decoder_window_t *window);

/** resize the image window (reconfigure handler).
 * this does @em not update the contents of the window
 * @since 0.3, changed in 0.4 to not redraw window
 */
extern int quec_decoder_window_resize(quec_decoder_window_t *window,
                              unsigned width,
                              unsigned height);

/** display detail for last window error to stderr.
 * @returns a non-zero value suitable for passing to exit()
 */
static inline int quec_decoder_window_error_spew (const quec_decoder_window_t *window,
                                          int verbosity)
{
    return(_quec_decoder_error_spew(window, verbosity));
}

/** retrieve the detail string for the last window error. */
static inline const char*
quec_decoder_window_error_string (const quec_decoder_window_t *window,
                          int verbosity)
{
    return(_quec_decoder_error_string(window, verbosity));
}

/** retrieve the type code for the last window error. */
static inline quec_decoder_error_t
quec_decoder_window_get_error_code (const quec_decoder_window_t *window)
{
    return(_quec_decoder_get_error_code(window));
}


/** select a compatible format between video input and output window.
 * the selection algorithm attempts to use a format shared by
 * video input and window output which is also most useful for
 * barcode scanning.  if a format conversion is necessary, it will
 * heuristically attempt to minimize the cost of the conversion
 */
extern int quec_decoder_negotiate_format(quec_decoder_video_t *video,
                                 quec_decoder_window_t *window);

/*@}*/

/*------------------------------------------------------------*/
/** @name Image Scanner interface
 * @anchor c-imagescanner
 * mid-level image scanner interface.
 * reads barcodes from 2-D images
 */
/*@{*/

struct quec_decoder_image_scanner_s;
/** opaque image scanner object. */
typedef struct quec_decoder_image_scanner_s quec_decoder_image_scanner_t;

/** constructor. */
extern quec_decoder_image_scanner_t *quec_decoder_image_scanner_create(void);

/** destructor. */
extern void quec_decoder_image_scanner_destroy(quec_decoder_image_scanner_t *scanner);

/** setup result handler callback.
 * the specified function will be called by the scanner whenever
 * new results are available from a decoded image.
 * pass a NULL value to disable callbacks.
 * @returns the previously registered handler
 */
extern quec_decoder_image_data_handler_t*
quec_decoder_image_scanner_set_data_handler(quec_decoder_image_scanner_t *scanner,
                                    quec_decoder_image_data_handler_t *handler,
                                    const void *userdata);


/** set config for indicated symbology (0 for all) to specified value.
 * @returns 0 for success, non-0 for failure (config does not apply to
 * specified symbology, or value out of range)
 * @see quec_decoder_decoder_set_config()
 * @since 0.4
 */
extern int quec_decoder_image_scanner_set_config(quec_decoder_image_scanner_t *scanner,
                                         quec_decoder_symbol_type_t symbology,
                                         quec_decoder_config_t config,
                                         int value);

/** parse configuration string using quec_decoder_parse_config()
 * and apply to image scanner using quec_decoder_image_scanner_set_config().
 * @returns 0 for success, non-0 for failure
 * @see quec_decoder_parse_config()
 * @see quec_decoder_image_scanner_set_config()
 * @since 0.4
 */
static inline int
quec_decoder_image_scanner_parse_config (quec_decoder_image_scanner_t *scanner,
                                 const char *config_string)
{
    quec_decoder_symbol_type_t sym;
    quec_decoder_config_t cfg;
    int val;
    return(quec_decoder_parse_config(config_string, &sym, &cfg, &val) ||
           quec_decoder_image_scanner_set_config(scanner, sym, cfg, val));
}

/** enable or disable the inter-image result cache (default disabled).
 * mostly useful for scanning video frames, the cache filters
 * duplicate results from consecutive images, while adding some
 * consistency checking and hysteresis to the results.
 * this interface also clears the cache
 */
extern void quec_decoder_image_scanner_enable_cache(quec_decoder_image_scanner_t *scanner,
                                            int enable);

/** remove any previously decoded results from the image scanner and the
 * specified image.  somewhat more efficient version of
 * quec_decoder_image_set_symbols(image, NULL) which may retain memory for
 * subsequent decodes
 * @since 0.10
 */
extern void quec_decoder_image_scanner_recycle_image(quec_decoder_image_scanner_t *scanner,
                                             quec_decoder_image_t *image);

/** retrieve decode results for last scanned image.
 * @returns the symbol set result container or NULL if no results are
 * available
 * @note the symbol set does not have its reference count adjusted;
 * ensure that the count is incremented if the results may be kept
 * after the next image is scanned
 * @since 0.10
 */
extern const quec_decoder_symbol_set_t*
quec_decoder_image_scanner_get_results(const quec_decoder_image_scanner_t *scanner);

/** scan for symbols in provided image.  The image format must be
 * "Y800" or "GRAY".
 * @returns >0 if symbols were successfully decoded from the image,
 * 0 if no symbols were found or -1 if an error occurs
 * @see quec_decoder_image_convert()
 * @since 0.9 - changed to only accept grayscale images
 */
extern int quec_decoder_scan_image_bar_code(quec_decoder_image_scanner_t *scanner,
                           quec_decoder_image_t *image);
extern int quec_decoder_scan_image_qr_code(quec_decoder_image_scanner_t *scanner,
                           quec_decoder_image_t *image);

/*@}*/

/*------------------------------------------------------------*/
/** @name Decoder interface
 * @anchor c-decoder
 * low-level bar width stream decoder interface.
 * identifies symbols and extracts encoded data
 */
/*@{*/

struct quec_decoder_decoder_s;
/** opaque decoder object. */
typedef struct quec_decoder_decoder_s quec_decoder_decoder_t;

/** decoder data handler callback function.
 * called by decoder when new data has just been decoded
 */
typedef void (quec_decoder_decoder_handler_t)(quec_decoder_decoder_t *decoder);

/** constructor. */
extern quec_decoder_decoder_t *quec_decoder_decoder_create(void);

/** destructor. */
extern void quec_decoder_decoder_destroy(quec_decoder_decoder_t *decoder);

/** set config for indicated symbology (0 for all) to specified value.
 * @returns 0 for success, non-0 for failure (config does not apply to
 * specified symbology, or value out of range)
 * @since 0.4
 */
extern int quec_decoder_decoder_set_config(quec_decoder_decoder_t *decoder,
                                   quec_decoder_symbol_type_t symbology,
                                   quec_decoder_config_t config,
                                   int value);

/** parse configuration string using quec_decoder_parse_config()
 * and apply to decoder using quec_decoder_decoder_set_config().
 * @returns 0 for success, non-0 for failure
 * @see quec_decoder_parse_config()
 * @see quec_decoder_decoder_set_config()
 * @since 0.4
 */
static inline int quec_decoder_decoder_parse_config (quec_decoder_decoder_t *decoder,
                                             const char *config_string)
{
    quec_decoder_symbol_type_t sym;
    quec_decoder_config_t cfg;
    int val;
    return(quec_decoder_parse_config(config_string, &sym, &cfg, &val) ||
           quec_decoder_decoder_set_config(decoder, sym, cfg, val));
}

/** clear all decoder state.
 * any partial symbols are flushed
 */
extern void quec_decoder_decoder_reset(quec_decoder_decoder_t *decoder);

/** mark start of a new scan pass.
 * clears any intra-symbol state and resets color to ::QUEC_DECODER_SPACE.
 * any partially decoded symbol state is retained
 */
extern void quec_decoder_decoder_new_scan(quec_decoder_decoder_t *decoder);

/** process next bar/space width from input stream.
 * the width is in arbitrary relative units.  first value of a scan
 * is ::QUEC_DECODER_SPACE width, alternating from there.
 * @returns appropriate symbol type if width completes
 * decode of a symbol (data is available for retrieval)
 * @returns ::QUEC_DECODER_PARTIAL as a hint if part of a symbol was decoded
 * @returns ::QUEC_DECODER_NONE (0) if no new symbol data is available
 */
extern quec_decoder_symbol_type_t quec_decoder_decode_width(quec_decoder_decoder_t *decoder,
                                            unsigned width,unsigned char flag);

/** retrieve color of @em next element passed to
 * quec_decoder_decode_width(). */
extern quec_decoder_color_t quec_decoder_decoder_get_color(const quec_decoder_decoder_t *decoder);

/** retrieve last decoded data.
 * @returns the data string or NULL if no new data available.
 * the returned data buffer is owned by library, contents are only
 * valid between non-0 return from quec_decoder_decode_width and next library
 * call
 */
extern const char *quec_decoder_decoder_get_data(const quec_decoder_decoder_t *decoder);

/** retrieve length of binary data.
 * @returns the length of the decoded data or 0 if no new data
 * available.
 */
extern unsigned int
quec_decoder_decoder_get_data_length(const quec_decoder_decoder_t *decoder);

/** retrieve last decoded symbol type.
 * @returns the type or ::QUEC_DECODER_NONE if no new data available
 */
extern quec_decoder_symbol_type_t
quec_decoder_decoder_get_type(const quec_decoder_decoder_t *decoder);

/** setup data handler callback.
 * the registered function will be called by the decoder
 * just before quec_decoder_decode_width() returns a non-zero value.
 * pass a NULL value to disable callbacks.
 * @returns the previously registered handler
 */
extern quec_decoder_decoder_handler_t*
quec_decoder_decoder_set_handler(quec_decoder_decoder_t *decoder,
                         quec_decoder_decoder_handler_t *handler);

/** associate user specified data value with the decoder. */
extern void quec_decoder_decoder_set_userdata(quec_decoder_decoder_t *decoder,
                                      void *userdata);

/** return user specified data value associated with the decoder. */
extern void *quec_decoder_decoder_get_userdata(const quec_decoder_decoder_t *decoder);

/*@}*/

/*------------------------------------------------------------*/
/** @name Scanner interface
 * @anchor c-scanner
 * low-level linear intensity sample stream scanner interface.
 * identifies "bar" edges and measures width between them.
 * optionally passes to bar width decoder
 */
/*@{*/

struct quec_decoder_scanner_s;
/** opaque scanner object. */
typedef struct quec_decoder_scanner_s quec_decoder_scanner_t;

/** constructor.
 * if decoder is non-NULL it will be attached to scanner
 * and called automatically at each new edge
 * current color is initialized to ::QUEC_DECODER_SPACE
 * (so an initial BAR->SPACE transition may be discarded)
 */
extern quec_decoder_scanner_t *quec_decoder_scanner_create(quec_decoder_decoder_t *decoder);

/** destructor. */
extern void quec_decoder_scanner_destroy(quec_decoder_scanner_t *scanner);

/** clear all scanner state.
 * also resets an associated decoder
 */
extern quec_decoder_symbol_type_t quec_decoder_scanner_reset(quec_decoder_scanner_t *scanner);

/** mark start of a new scan pass. resets color to ::QUEC_DECODER_SPACE.
 * also updates an associated decoder.
 * @returns any decode results flushed from the pipeline
 * @note when not using callback handlers, the return value should
 * be checked the same as quec_decoder_scan_y()
 * @note call quec_decoder_scanner_flush() at least twice before calling this
 * method to ensure no decode results are lost
 */
extern quec_decoder_symbol_type_t quec_decoder_scanner_new_scan(quec_decoder_scanner_t *scanner, unsigned char flag);
/** flush scanner processing pipeline.
 * forces current scanner position to be a scan boundary.
 * call multiple times (max 3) to completely flush decoder.
 * @returns any decode/scan results flushed from the pipeline
 * @note when not using callback handlers, the return value should
 * be checked the same as quec_decoder_scan_y()
 * @since 0.9
 */
extern quec_decoder_symbol_type_t quec_decoder_scanner_flush(quec_decoder_scanner_t *scanner, unsigned char flag);
/** process next sample intensity value.
 * intensity (y) is in arbitrary relative units.
 * @returns result of quec_decoder_decode_width() if a decoder is attached,
 * otherwise @returns (::QUEC_DECODER_PARTIAL) when new edge is detected
 * or 0 (::QUEC_DECODER_NONE) if no new edge is detected
 */
extern quec_decoder_symbol_type_t quec_decoder_scan_y(quec_decoder_scanner_t *scanner,
                                      int y, unsigned char flag);

/** process next sample from RGB (or BGR) triple. */
static inline quec_decoder_symbol_type_t quec_decoder_scan_rgb24 (quec_decoder_scanner_t *scanner,
                                                    unsigned char *rgb)
{
    return(quec_decoder_scan_y(scanner, rgb[0] + rgb[1] + rgb[2], 0));
}

/** retrieve last scanned width. */
extern unsigned quec_decoder_scanner_get_width(const quec_decoder_scanner_t *scanner);

/** retrieve sample position of last edge.
 * @since 0.10
 */
extern unsigned quec_decoder_scanner_get_edge(const quec_decoder_scanner_t *scn,
                                      unsigned offset,
                                      int prec);

/** retrieve last scanned color. */
extern quec_decoder_color_t quec_decoder_scanner_get_color(const quec_decoder_scanner_t *scanner);

/*@}*/

#ifdef __cplusplus
    }
}

# include "quec_decoder/Exception.h"
# include "quec_decoder/Decoder.h"
# include "quec_decoder/Scanner.h"
# include "quec_decoder/Symbol.h"
# include "quec_decoder/Image.h"
# include "quec_decoder/ImageScanner.h"
# include "quec_decoder/Video.h"
# include "quec_decoder/Window.h"
# include "quec_decoder/Processor.h"
#endif

#endif
