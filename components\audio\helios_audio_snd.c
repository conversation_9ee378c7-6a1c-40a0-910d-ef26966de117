#include <stdio.h>
#include <stdlib.h>
#include <assert.h>


#include "helios_os.h"
#include "helios_audio_py.h"
#include "watermark.h"

#include "helios_include.h"
#include "helios_audio_fs.h"
#include "helios_audio_snd.h"





//////////////////////////////////////
typedef enum{
	SND_PLAY_STOP,
	SND_PLAY_SENDING_MESSAGE,
	SND_PLAY_PLAYING,
	SND_PLAY_STOPPING,
}SND_PLAY_STATUS;

static cb_on_player aud_play_cb = NULL;


static PCM_HANDLE_T snd_pcm_whdl = NULL;


int is_snd_file_playing = SND_PLAY_STOP;
static char snd_file_name[256];
static void* snd_file_stack_ptr = NULL;
static Helios_Sem_t snd_file_play_Sema;
static Helios_OSFlag_t snd_file_pcm_flag_Ref = 0;
static Helios_Thread_t snd_file_task_Ref = 0;
static int snd_file_play_mode = 0;
static int snd_file_pcm_pingpong_index = 0;
static unsigned short snd_file_pcm_pingpong_buffer[48*20*2*2]; // max 48K 20ms stereo * 2
static ring_buffer_handler_t snd_ring_buff = NULL;
static int file_length = 0;

#define SND_FILE_PCM_TASK_CONTINUE   (0x01)
#define SND_FILE_PCM_STOP   (0x02)
#define SND_FILE_PCM_TASK_MASK       (SND_FILE_PCM_STOP | SND_FILE_PCM_TASK_CONTINUE)

enum{
	SND_FILE_MODE,
	SND_STREAM_MODE,
}SND_PLAY_MODE;
static ring_buffer_handler_t snd_file_Ringbuf;


#define HELIOS_PCM_BLOCK_FLAG (0x01)
#define HELIOS_PCM_NONBLOCK_FLAG (0x02)
#define HELIOS_PCM_READ_FLAG (0x04)
#define HELIOS_PCM_WRITE_FLAG (0x08)

#define Helios_PCM_TASK_CONTINUE (0x01)
#define Helios_PCM_STOP (0x02)
#define Helios_PCM_TASK_MASK (Helios_PCM_STOP | Helios_PCM_TASK_CONTINUE)


#define SND_FILE_RINGBUF_SIZE (30*1024)
//////////////////////////////////////


static int Helios_play_stream_start(unsigned int rate, unsigned int ch, unsigned short *pcm_buf, unsigned int frameByteSize)
{
	int ret = 0;
	if (snd_pcm_whdl == NULL)
	{
#ifdef CONFIG_AUDIO_PWM
		snd_pcm_whdl = Helios_Aud_PWM_Open(CONFIG_AUDIO_PWM_PIN, 1, rate, HELIOS_PCM_WRITE_FLAG | HELIOS_PCM_BLOCK_FLAG);
#else
		snd_pcm_whdl = Helios_PCM_Open(ch, rate, HELIOS_PCM_WRITE_FLAG | HELIOS_PCM_BLOCK_FLAG);
#endif
	}

	if (snd_pcm_whdl == NULL)
	{
		return -1;
	}
#ifdef CONFIG_AUDIO_PWM
	ret = Helios_Aud_PWM_Write(snd_pcm_whdl, pcm_buf, frameByteSize);
#else
	ret = Helios_PCM_Write(snd_pcm_whdl, pcm_buf, frameByteSize);
#endif
	if (ret < 0)
	{
		AUDLOGE("kOutputBufferSize[%d] ret[%d]\n", frameByteSize, ret);
		return -1;
	}
	Helios_Flag_Release(snd_file_pcm_flag_Ref, Helios_PCM_TASK_CONTINUE, Helios_FLAG_OR);
	

	return 0;
}



static void snd_file_pcm_stop(void)
{
	if (snd_pcm_whdl)
	{
#ifdef CONFIG_AUDIO_PWM
		Helios_Aud_PWM_Close(snd_pcm_whdl);
#else
		Helios_PCM_Close(snd_pcm_whdl);
#endif
		snd_pcm_whdl = NULL;
	}
}


#ifndef min
#define min(a, b) ((a) < (b) ? (a) : (b))
#endif
static uint32_t get_snd_ringbuf_free_size(void) {
	uint32_t freesize = 0;
	quec_ring_buffer_get_free_size(snd_file_Ringbuf, &freesize);
	return freesize;
}
static uint32_t get_snd_ringbuf_data_size(void) {
	uint32_t dataize = 0;
	if(snd_file_play_mode == SND_FILE_MODE)
		quec_ring_buffer_get_used_size(snd_file_Ringbuf, &dataize);
	else
		quec_ring_buffer_get_used_size(snd_ring_buff, &dataize);
	return dataize;
}


#define FORMAT_PCM 	1

static bool is_support_samplerate(unsigned int rate)
{
	switch(rate) {
	case 8000:
	case 11025:
	case 12000:
	case 16000:
	case 22050:
	case 24000:
	case 32000:
	case 44100:
	case 48000:
		return true;
	}
	return false;
}


uint32_t my_ntohl(uint32_t value) {
    uint32_t result = 0;

    result |= (value & 0xFF000000) >> 24;
    result |= (value & 0x00FF0000) >> 8;
    result |= (value & 0x0000FF00) << 8;
    result |= (value & 0x000000FF) << 24;

    return result;
}



static int check_snd_header(unsigned int temp, HeliosAudFILE *fd, ring_buffer_handler_t *snd_ring_buff, int *sample, int *channel){
	HELIOS_SND_HEADER snd_hdr;
	int ret = 0, i = 0;
	unsigned int chunk_id = 0,chunk_size = 0,chunk_len = 0;
	unsigned int data = 0,data_size = 0;
	if((fd == NULL && temp == 0) || (snd_ring_buff == NULL && temp == 1)){
		AUDLOGE("error\n");
		return -1;
	}
	if(temp == 0){
		ret = Helios_Aud_fseek(fd, 0, SEEK_SET);
		if(ret < 0)
		{
			AUDLOGE("error\n");
			return -1;
		}
		ret = Helios_Aud_fread(&snd_hdr, sizeof(snd_hdr), 1, fd);							//riff
		if(ret < sizeof(snd_hdr))
		{
			Helios_Aud_fseek(fd, 0, SEEK_SET);
			AUDLOGE("error\n");
			return -1;
		}
	}else if(temp == 1){
		quec_ring_buffer_read_ex(snd_ring_buff, &snd_hdr, sizeof(snd_hdr), &ret);
		if(ret < sizeof(snd_hdr)) {
			AUDLOGE("error\n");
			return -1;
		}
			
	}

	

	snd_hdr.magic = my_ntohl(snd_hdr.magic);
    snd_hdr.dataOffset = my_ntohl(snd_hdr.dataOffset);
    snd_hdr.dataSize = my_ntohl(snd_hdr.dataSize);
    snd_hdr.encoding = my_ntohl(snd_hdr.encoding);
    snd_hdr.sampleRate = my_ntohl(snd_hdr.sampleRate);
    snd_hdr.channels = my_ntohl(snd_hdr.channels);

	
	AUDLOGI("snd magic[%x],dataOffset[%d],dataSize[%d],encoding[%d],sampleRate[%d],channels[%d]\n",snd_hdr.magic, 
		snd_hdr.dataOffset, snd_hdr.dataSize, snd_hdr.encoding, snd_hdr.sampleRate, snd_hdr.channels);

	if (snd_hdr.magic != 0x2e736e64) {
        AUDLOGE("Invalid SND file\n");
        return -1;
    }

	if (snd_hdr.encoding != 3) {
        AUDLOGE("Unsupported encoding\n");
        return -1;
    }

	
	if(is_support_samplerate(snd_hdr.sampleRate) == false){
		return -1;
	}

	*sample = snd_hdr.sampleRate;
	*channel = snd_hdr.channels;

	
	Helios_Aud_fseek(fd, snd_hdr.dataOffset+1, SEEK_SET);

	return snd_hdr.dataSize;

}
static int snd_file_play_func() 
{	
    int 					first_time 	= 1;
    unsigned int   			event 		= 0;  
	int 			rc 			= 0;    
	int 					retVal 		= -1;
	
	void*					tempBuf 	= NULL;
	
    int 					channels 	= 0;
    int 					samplerate 	= 0;
	uint32_t 				bytesRead 	= 0;
	bool 					isReadEnd = false;
	int						data = 0;
	HeliosAudFILE 					*fd = NULL;

    // Open the file.
    if(snd_file_play_mode == SND_FILE_MODE){
    	fd = Helios_Aud_fopen(snd_file_name, "r");
		if(fd == NULL) {
			 AUDLOGI("%s:open file fail\r\n", __func__);
			 goto end;
		}
    }
	file_length =  check_snd_header(snd_file_play_mode, fd, snd_ring_buff, &samplerate, &channels);
	if(file_length < 0){
		AUDLOGE("check snd header error! \n");
		goto end;
	}
	if(snd_file_play_mode == SND_FILE_MODE)
		tempBuf = malloc(SND_FILE_RINGBUF_SIZE/2);
	
    while (1) {
		if(snd_file_play_mode == SND_FILE_MODE){
			while (!isReadEnd && get_snd_ringbuf_free_size() >= SND_FILE_RINGBUF_SIZE/2)  {
		        // Read input from the file.
		        uint32_t freesize = 0;
				freesize = get_snd_ringbuf_free_size();
				bytesRead = min(freesize, SND_FILE_RINGBUF_SIZE/2);
		        retVal = Helios_Aud_fread(tempBuf, bytesRead, 1, fd);
				if (retVal <= 0) {
					isReadEnd = true;
					break;
				}
		        //quec_ring_buffer_write(snd_file_Ringbuf, tempBuf, retVal);
		        rc = Helios_play_stream_start(samplerate, channels, tempBuf, retVal);
			}
		}else
			isReadEnd = true;
        
        Helios_Flag_Wait(snd_file_pcm_flag_Ref, SND_FILE_PCM_TASK_MASK, Helios_FLAG_OR_CLEAR, &event, HELIOS_WAIT_FOREVER); 
		if(isReadEnd){
			while((0 != get_snd_ringbuf_data_size()) 
					&& (SND_FILE_PCM_STOP != (SND_FILE_PCM_STOP & event))){
				Helios_Flag_Wait(snd_file_pcm_flag_Ref, SND_FILE_PCM_TASK_MASK, Helios_FLAG_OR_CLEAR, &event, HELIOS_WAIT_FOREVER); 
			}
		}

			
        if(SND_FILE_PCM_STOP == (SND_FILE_PCM_STOP & event) || isReadEnd){
            break;
        }
		
    }
	
end:	
//	if(AUDIOHAL_ERR_NO == rc){
    	snd_file_pcm_stop();
//	}
    // Free allocated memory.
    if(tempBuf)	free(tempBuf);
	if(fd) {
		Helios_Aud_fclose(fd);
	}
	if(snd_file_play_mode == SND_STREAM_MODE){
		quec_ring_buffer_deinit(&snd_ring_buff);
		snd_ring_buff = NULL;
	}else
    	quec_ring_buffer_reset(snd_file_Ringbuf);
	return retVal;
}


static int snd_file_play(void)
{
    int status;
	unsigned char first_entry = 0;
	while(1){

        if(0 != first_entry) {
			is_snd_file_playing = SND_PLAY_STOP;
		}
		else {
			first_entry = !first_entry;
		}
        status = Helios_Semaphore_Acquire(snd_file_play_Sema, HELIOS_WAIT_FOREVER);
//        assert(status != 0); 

		if (aud_play_cb) {
			aud_play_cb(snd_file_name, 0, HELIOS_AUD_PLAYER_START);
		}
		
		if(SND_PLAY_SENDING_MESSAGE == is_snd_file_playing) {
			is_snd_file_playing = SND_PLAY_PLAYING;
			snd_file_play_func();
		}
		is_snd_file_playing = SND_PLAY_STOPPING;
		
		if (aud_play_cb) {
			aud_play_cb(snd_file_name, 0, HELIOS_AUD_PLAYER_FINISHED);
		}	
	}
	return 0;
}


static void snd_file_play_task_init(void)
{
    int status;
    static int inited = 0;	
    size_t snd_stackSize = 1024*10;
    int snd_thread_priority = 90;	
		
    if(!inited){
		quec_ring_buffer_init(&snd_file_Ringbuf, SND_FILE_RINGBUF_SIZE);
	
        snd_file_pcm_flag_Ref = Helios_Flag_Create();
        assert(snd_file_pcm_flag_Ref != 0);
		
        snd_file_stack_ptr = malloc(snd_stackSize);
		assert(snd_file_stack_ptr != NULL);

		
		Helios_ThreadAttr attr = {0};
		attr.name = "Helios_snd_stream_play";
		attr.stack_size = snd_stackSize;
		attr.priority = snd_thread_priority;
		attr.entry = snd_file_play;

		snd_file_task_Ref = Helios_Thread_Create(&attr);
		
        assert(snd_file_task_Ref != 0);       

        inited = 1;
    }
}

typedef unsigned char           BOOL;

static BOOL helios_find_file(char *name)
{
	HeliosAudFILE *fileID = NULL;

	fileID = Helios_Aud_fopen(name, "r");
	if (fileID)
	{
		Helios_Aud_fclose(fileID);

		return TRUE;
	}
	return FALSE;
}


int Helios_snd_file_start(char *file_name, cb_on_player aud_cb)
{
	OSA_STATUS status;
	uint32_t ret = 0;

	if(!file_name){
	    AUDLOGE("%s:file_name NULL\r\n", __func__);
		return -1;
	}

	AUDLOGI("%s:play file_name %s\r\n", __func__, file_name);
	
    if((0 == strlen(file_name))
		|| (strlen(file_name) > 250)
		|| (!helios_find_file(file_name))) {
		AUDLOGE("%s:file_name error!!\r\n", __func__);
        return -1;
    }

	if (aud_cb)
	{
		aud_play_cb = aud_cb;
	}

    if(SND_PLAY_STOP != is_snd_file_playing) {
		AUDLOGE("%s:snd file is in playing\r\n", __func__);
        return -2;
    }
    is_snd_file_playing = SND_PLAY_SENDING_MESSAGE;
    
	snd_file_play_mode = SND_FILE_MODE;
	memset(snd_file_name, 0, sizeof(snd_file_name));
    memcpy(snd_file_name, file_name, strlen(file_name) + 1);
		
    if(snd_file_task_Ref == 0) {
		
		snd_file_play_Sema = Helios_Semaphore_Create(1, 0);
		assert(snd_file_play_Sema != 0);
		
        snd_file_play_task_init();

		
		Helios_Semaphore_Poll(snd_file_play_Sema, &ret);
		if (ret == 0)
		{
			Helios_Semaphore_Release(snd_file_play_Sema);
		}
    } else {
        Helios_Semaphore_Release(snd_file_play_Sema);
    }
    return 0;
}

int Helios_play_snd_stream_start(char *data, unsigned int size)
{
	OSA_STATUS status;

	if(data == NULL){
	    AUDLOGI("%s:data error\r\n", __func__);
		return -1;
	}
	
    if(SND_PLAY_STOP != is_snd_file_playing) {
		AUDLOGI("%s:snd file is in playing\r\n", __func__);
        return -2;
    }
    is_snd_file_playing = SND_PLAY_SENDING_MESSAGE;
	
	snd_file_play_mode = SND_STREAM_MODE;

	quec_ring_buffer_init(&snd_ring_buff, size);
	quec_ring_buffer_write(snd_ring_buff, (uint8 *)data, size);
		
    if(snd_file_task_Ref == 0) {
		snd_file_play_Sema = Helios_Semaphore_Create(1, 0);
		assert(snd_file_play_Sema != 0);
		
        snd_file_play_task_init();
    } else {
        Helios_Semaphore_Release(snd_file_play_Sema);
    }
    return 0;
}

int Helios_snd_file_stop(void)
{  
    AUDLOGI("%s:stop\r\n", __func__);
    int count = 0;
    if(SND_PLAY_STOP == is_snd_file_playing) {
		AUDLOGI("%s:error, snd is not playing\r\n", __func__);
        return -1;
    }

    if(SND_PLAY_SENDING_MESSAGE == is_snd_file_playing) {
		is_snd_file_playing = SND_PLAY_STOPPING;
    }
	
    Helios_Flag_Release(snd_file_pcm_flag_Ref, SND_FILE_PCM_STOP, Helios_FLAG_OR);
	while ((SND_PLAY_STOP != is_snd_file_playing) && count < 100) {
		Helios_msleep(15);
		count++;
	}
    return 0;
}

