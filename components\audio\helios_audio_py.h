#ifndef __HELIOS_PY_AUDIO_H__
#define __HELIOS_PY_AUDIO_H__

typedef int QuecOSStatus;
typedef unsigned char OSA_STATUS;

typedef void *OSATaskRef;
typedef void *OSAFlagRef;
typedef void *PCM_HANDLE_T;
#define kNoErr 0	   //! No error occurred.
#define QUEC_SUCCESS 0 //! No error occurred.

#ifndef size_t
typedef unsigned int size_t;
#endif



/**
 * AUDIO PLAY TYPE
 */
typedef enum
{
	HELIOS_AUDIO_PLAY_TYPE_NONE = 0,
	HELIOS_AUDIO_PLAY_TYPE_LOCAL,
	HELIOS_AUDIO_PLAY_TYPE_VOICE,
	HELIOS_AUDIO_PLAY_TYPE_POC,
	HELIOS_AUDIO_PLAY_TYPE_MAX
} Helios_AudPlayerType;

typedef enum
{
	HELIOS_AUD_PLAYER_ERROR = -1,
	HELIOS_AUD_PLAYER_START = 0, // audio player start play
	HELIOS_AUD_PLAYER_PAUSE,	 // audio player has been paused by call or pause api
	HELIOS_AUD_PLAYER_FINISHED,	 // audio player finish current playing
	HELIOS_AUD_PLAYER_CLOSE,	 // audio player closed
	HELIOS_AUD_PLAYER_RESUME,	 // resume player from pause
	HELIOS_AUD_PLAYER_MOREDATA,
	HELIOS_AUD_PLAYER_NODATA_STRAT,
	HELIOS_AUD_PLAYER_DATA_RESUME,
	HELIOS_AUD_PLAYER_REQURST_CLOSE
} Helios_EnumAudPlayerState;

typedef int (*helios_cb_on_player)(char *p_data, size_t len, Helios_EnumAudPlayerState state);

#define QL_PCM_BLOCK_FLAG (0x01)
#define QL_PCM_NONBLOCK_FLAG (0x02)
#define QL_PCM_READ_FLAG (0x04)
#define QL_PCM_WRITE_FLAG (0x08)

typedef enum
{
	HELIOS_AUDIO_FORMAT_UNKNOWN, ///< placeholder for unknown format
	HELIOS_AUDIO_FORMAT_PCM,	 ///< raw PCM data
	HELIOS_AUDIO_FORMAT_WAVPCM,	 ///< WAV, PCM inside
	HELIOS_AUDIO_FORMAT_MP3,	 ///< MP3
	HELIOS_AUDIO_FORMAT_AMRNB,	 ///< AMR-NB
	HELIOS_AUDIO_FORMAT_AMRWB,	 ///< AMR_WB
	HELIOS_AUDIO_FORMAT_AAC,	 ///< AAC
	HELIOS_AUDIO_FORMAT_M4A,	 ///< M4A
	HELIOS_AUDIO_FORMAT_TS,		 ///< TS
	HELIOS_AUDIO_FORMAT_FLV,
	HELIOS_AUDIO_FORMAT_RTMP,
	HELIOS_AUDIO_FORMAT_MAX
} Helios_AudStreamFormat;

typedef enum
{
	HELIOS_OUTPUT_RECEIVER = 0,	 ///< receiver
	HELIOS_OUTPUT_HEADPHONE = 1, ///< headphone
	HELIOS_OUTPUT_SPEAKER = 2,	 ///< speaker

	HELIOS_OUTPUT_MAX,
} Helios_AudOutputType;


typedef enum{
	QUEC_PCM_FORMAT_S8 = 0,
	QUEC_PCM_FORMAT_S16_LE,
	QUEC_PCM_FORMAT_S24_LE,
	QUEC_PCM_FORMAT_S32_LE,
}QUEC_PCM_FMT_T;

typedef enum{
	AUD_STREAM_FORMAT_MP3 = 0,
	AUD_STREAM_FORMAT_AMR = 1,
	AUD_STREAM_FORMAT_PCM = 2,
	AUD_STREAM_FORMAT_AMRNB,
	AUD_STREAM_FORMAT_AMRWB,
	AUD_STREAM_FORMAT_SND,
	AUD_STREAM_FORMAT_END
}aud_stream_format;


typedef enum
{
	HELIOS_NOT_INITED,
	HELIOS_INITED,
} HELIOS_STREAM_INIT_STATE;

typedef enum
{
	HELIOS_STREAM_SEND_FINISH,
	HELIOS_STREAM_SEND_NOT_FINISH,
} HELIOS_STREAM_FINISH_STATE;

extern HELIOS_STREAM_INIT_STATE helios_stream_init_flag;

/**
 ******************************************************************************
 * @file    ql_audio.h
 * <AUTHOR>
 * @version V1.0.0
 * @date    2019/12/10
 * @brief   This file contains the audio functions's definitions
 ******************************************************************************
 */
typedef void (*cb_on_player)(char *p_data, int len, Helios_EnumAudPlayerState state);
typedef void (*helios_audio_pa_cb_t)(unsigned int event);

typedef void(*helios_cb_pcm_play)(void*, int);


void *Helios_PCM_Open(unsigned int channels, unsigned int samplerate, unsigned int flags);
int Helios_PCM_Close(void *handle);
int Helios_PCM_Write(void *handle, void *data, unsigned int count);
int Helios_PCM_Read(void *handle, void *data, unsigned int count);
int Helios_set_mic_channel(int channel);
int Helios_PCM_buffer_Reset(void* handle);

int Helios_Audio_FilePlayStart_PY(char *file_name, Helios_AudPlayerType type, helios_cb_on_player usr_cb);

int Helios_Audio_MontageFilePlayStart(char *montage_name, char *file_name, Helios_AudPlayerType type, helios_cb_on_player usr_cb, unsigned char device);

int Helios_Audio_StreamPlayStart(Helios_AudStreamFormat format, const void *buf, size_t streamsize,
								 Helios_AudPlayerType type, Helios_AudOutputType outputtype, helios_cb_on_player usr_cb);

 int Helios_Audio_SetPaCallback_py(Helios_AudOutputType type, helios_audio_pa_cb_t cb);

 void *Helios_Aud_PWM_Open(int pwm_n, unsigned int channels, unsigned int samplerate, unsigned int flags);
 int Helios_Aud_PWM_Close(void* handle);
 int Helios_Aud_PWM_Write(void* handle, void* data, unsigned int count);
int Helios_PCM_Play_Cb(void* handle, helios_cb_pcm_play cb);
int Helios_PCM_Close_Force(void* handle);

void Helios_file_jump_frame(Helios_AudStreamFormat format, uint32_t cnt);

#endif
