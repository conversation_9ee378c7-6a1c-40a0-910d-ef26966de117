# 环境：  
硬件：STM32F407  
软件：KEIL5

# 描述：  
UTF-8与GB2312编码互转

# 使用说明：  
## UTF-8转GB2312
```
#include <stdio.h>
#include <string.h>
#include "utf8_gb2312_switch.h"

//"你好123ABC世界！
char utf8Data[256] = {0xE4,0xBD,0xA0,0xE5,0xA5,0xBD,0x31,0x32,0x33,0x41,0x42,0x43,0xE4,0xB8,0x96,0xE7,0x95,0x8C,0xEF,0xBC,0x81}; 
char gb2312Data[256] = {0};

size_t gb2312DataLen = utf8_to_gb2312((uint8_t *)utf8Data, strlen(utf8Data), (uint8_t *)gb2312Data, sizeof(gb2312Data));
for(int i=0;i<gb2312DataLen;i++)
{
  printf("0x%02X ", gb2312Data[i]);
}
printf("\r\n");
```
结果如下：  
```
0xC4 0xE3 0xBA 0xC3 0x31 0x32 0x33 0x41 0x42 0x43 0xCA 0xC0 0xBD 0xE7 0xA3 0xA1
```
##  GB2312转UTF-8  
```
#include <stdio.h>
#include <string.h>
#include "utf8_gb2312_switch.h"

//"你好123ABC世界！"
char gb2312Data[256] = {0xC4,0xE3,0xBA,0xC3,0x31,0x32,0x33,0x41,0x42,0x43,0xCA,0xC0,0xBD,0xE7,0xA3,0xA1};
char utf8Data[256] = {0};

size_t utf8DataLen = gb2312_to_utf8((uint8_t *)gb2312Data, strlen(gb2312Data), (uint8_t *)utf8Data, sizeof(utf8Data));
for(int i=0;i<utf8DataLen;i++)
{
  printf("0x%02X ", utf8Data[i]);
}
printf("\r\n");
```
结果如下：  
```
0xE4 0xBD 0xA0 0xE5 0xA5 0xBD 0x31 0x32 0x33 0x41 0x42 0x43 0xE4 0xB8 0x96 0xE7 0x95 0x8C 0xEF 0xBC 0x81
```