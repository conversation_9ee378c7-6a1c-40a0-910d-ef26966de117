/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2020 ASR Ltd. All Rights Reserved
-------------------------------------------------------------------------------------------------------------------*/

#include <stdlib.h>
#include <string.h>
#include "voAMRWB.h"
#include "cmnMemory.h"
#include "amrwb_enc_api.h"
extern "C" {
#include "audio_file.h"

#define  AMRWB_INPUT_SIZE   640
#define  AMRWB_OUTPUT_SIZE  1024
#define VOAMRWB_RFC3267_HEADER_INFO "#!AMR-WB\n"

    typedef int (VO_API * VOGETAUDIOENCAPI) (VO_AUDIO_CODECAPI * pEncHandle);
    typedef struct amrwb_enc_state {
        AUDIO_FILE_ID fsrc;
        AUDIO_FILE_ID fdst;
        unsigned char InputBuf[AMRWB_INPUT_SIZE];
        unsigned char OutputBuf[AMRWB_OUTPUT_SIZE];
        unsigned char *inBuf;
        unsigned char *outBuf;
        int mode;
        int allow_dtx;
        int frame_type;
        int relens;
        int framenum;
        VO_HANDLE* hCodec;
        VO_AUDIO_CODECAPI AudioAPI;
        VO_CODECBUFFER inData;
        VO_CODECBUFFER outData;
        VO_MEM_OPERATOR moper;
        VO_CODEC_INIT_USERDATA useData;
        VO_AUDIO_OUTPUTINFO outFormat;
    }amrwb_enc_state;

    static int  GetNextBuf(AUDIO_FILE_ID inFile, unsigned char* dst, int size) {
        int size2 = (int)common_fread(dst, sizeof(signed char), size, inFile);
        return size2;
    }

    int amrwb_encode_open(const amrwb_enc_config* config, amrwb_enc_handle* handle) {
        if (config && handle) {
            amrwb_enc_state* st = (amrwb_enc_state*)malloc(sizeof(amrwb_enc_state));
            if (!st)
                return -1;

            memset(st, 0, sizeof(amrwb_enc_state));
            st->inBuf = st->InputBuf;
            st->outBuf = st->OutputBuf;
            st->mode = config->mode;
            st->frame_type = config->frame_type;
            st->allow_dtx = config->allow_dtx;

            if (config->name) {
                if ((st->fsrc = common_fopen(config->name, "rb")) == NULL) {
                    return -2;
                }
            }

            if (config->out_name) {
                if ((st->fdst = common_fopen(config->out_name, "wb")) == NULL) {
                    return -3;
                }
            }

            st->moper.Alloc = cmnMemAlloc;
            st->moper.Copy = cmnMemCopy;
            st->moper.Free = cmnMemFree;
            st->moper.Set = cmnMemSet;
            st->moper.Check = cmnMemCheck;

            st->useData.memflag = VO_IMF_USERMEMOPERATOR;
            st->useData.memData = (VO_PTR)(&st->moper);

            int ret = voGetAMRWBEncAPI(&st->AudioAPI);
            if (ret) {
                return -4;
            }

            //#######################################   Init Encoding Section   #########################################
            ret = st->AudioAPI.Init((VO_HANDLE*)&st->hCodec, VO_AUDIO_CodingAMRWB, &st->useData);
            if (ret) {
                return -5;
            }

            //###################################### set encode Mode ##################################################
            ret = st->AudioAPI.SetParam((VO_HANDLE*)st->hCodec, VO_PID_AMRWB_FRAMETYPE, &st->frame_type);
            ret = st->AudioAPI.SetParam((VO_HANDLE*)st->hCodec, VO_PID_AMRWB_MODE, &st->mode);
            ret = st->AudioAPI.SetParam((VO_HANDLE*)st->hCodec, VO_PID_AMRWB_DTX, &st->allow_dtx);

            if (st->frame_type == VOAMRWB_RFC3267) {
                /* write RFC3267 Header info to indicate single channel AMR file storage format */
                if (st->fdst)
                    common_fwrite(VOAMRWB_RFC3267_HEADER_INFO, 1, strlen(VOAMRWB_RFC3267_HEADER_INFO), st->fdst);
            }

            *handle = (amrwb_enc_handle)st;
            return 0;
        }

        return -1;
    }

    int amrwb_encode_mode(amrwb_enc_handle handle, int32_t mode) {
        amrwb_enc_state* st = (amrwb_enc_state*)handle;
        if (st) {
            if (mode >= VOAMRWB_MD66 && mode < VOAMRWB_N_MODES)
                st->mode = mode;
            else
                st->mode = VOAMRWB_MD66;
            return 0;
        }
        return -1;
    }

    int amrwb_encode_do(amrwb_enc_handle handle) {
        amrwb_enc_state* st = (amrwb_enc_state*)handle;
        if (st) {
            st->inData.Buffer = (unsigned char *)st->inBuf;
            st->inData.Length = st->relens;
            st->outData.Buffer = st->outBuf;
            int returnCode = st->AudioAPI.SetInputData(st->hCodec, &st->inData);
            do {
                returnCode = st->AudioAPI.GetOutputData(st->hCodec, &st->outData, &st->outFormat);
                if (returnCode == 0) {
                    break;
                }
                else if ((unsigned)returnCode == VO_ERR_LICENSE_ERROR) {
                    return -2;
                }
            } while ((unsigned)returnCode != VO_ERR_INPUT_BUFFER_SMALL);
            return 0;
        }

        return -1;
    }

    int amrwb_encode_read(amrwb_enc_handle handle) {
        amrwb_enc_state* st = (amrwb_enc_state*)handle;
        if (st) {
            st->relens = GetNextBuf(st->fsrc, st->InputBuf, AMRWB_INPUT_SIZE);
            st->inBuf = st->InputBuf;
            if (st->relens == AMRWB_INPUT_SIZE)
                return 0;
        }

        return -1;
    }

    int amrwb_encode_write(amrwb_enc_handle handle) {
        amrwb_enc_state* st = (amrwb_enc_state*)handle;
        if (st && st->outData.Length > 0) {
            common_fwrite(st->outData.Buffer, 1, st->outData.Length, st->fdst);
            return 0;
        }
        return -1;
    }

    int amrwb_encode_set(amrwb_enc_handle handle, const uint16_t* input_data, uint32_t size) {
        if (handle && input_data) {
            if (size != AMRWB_INPUT_SIZE)
                return -2;

            amrwb_enc_state* st = (amrwb_enc_state*)handle;
            memcpy(st->InputBuf, input_data, AMRWB_INPUT_SIZE);
            st->inBuf = st->InputBuf;
            st->relens = AMRWB_INPUT_SIZE;
            return 0;
        }

        return -1;
    }

    int amrwb_encode_get(amrwb_enc_handle handle, uint8_t* output_data, uint32_t* size) {
        if (handle && output_data && size) {
            amrwb_enc_state* st = (amrwb_enc_state*)handle;
            *size = st->outData.Length;
            memcpy(output_data, st->outData.Buffer, st->outData.Length);
            return 0;
        }

        return -1;
    }

    int amrwb_encode_loop(amrwb_enc_handle handle) {
        int err_code = amrwb_encode_read(handle);
        if (err_code != 0)
            return -1;

        err_code = amrwb_encode_do(handle);
        if (err_code != 0)
            return -2;

        err_code = amrwb_encode_write(handle);
        if (err_code != 0)
            return -3;

        return 0;
    }

    int amrwb_encode_close(amrwb_enc_handle handle) {
        amrwb_enc_state* st = (amrwb_enc_state*)handle;
        if (st) {
            st->AudioAPI.Uninit(st->hCodec);
            if (st->fsrc)
                common_fclose(st->fsrc);
            if (st->fdst)
                common_fclose(st->fdst);
            free(st);
            return 0;
        }
        return -1;
    }
}

#if ENCODER_TEST_AMR_WB == 1
int main(int argc, char **argv) {
    amrwb_enc_handle handle = 0;
    int err_code = -1;
    if (argc > 2) {
        amrwb_enc_config config = { 0 };
        config.name = argv[1];
        config.out_name = argv[2];
        config.allow_dtx = 0;
        config.frame_type = VOAMRWB_RFC3267;
        config.mode = VOAMRWB_MD2385;
        if (argc > 3)
            config.frame_type = atoi(argv[3]);
        if (argc > 4)
            config.mode = atoi(argv[4]);
        if (argc > 5)
            config.allow_dtx = atoi(argv[5]);
        err_code = amrwb_encode_open(&config, &handle);
    }

    if (err_code != 0) {
        goto safe_exit;
    }

    while (1) {
        err_code = amrwb_encode_loop(handle);
        if (err_code != 0)
            goto safe_exit;
    }

safe_exit:
    amrwb_encode_close(handle);
    return 0;
}
#endif
