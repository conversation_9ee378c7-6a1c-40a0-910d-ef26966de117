/**  @file
  ql_decoder.c

  @brief
  This file is decoder api.

*/

/*================================================================
  Copyright (c) 2020 Quectel Wireless Solution, Co., Ltd.  All Rights Reserved.
  Quectel Wireless Solution Proprietary and Confidential.
=================================================================*/
/*=================================================================

                        EDIT HISTORY FOR MODULE

This section contains comments describing changes made to the module.
Notice that changes are listed in reverse chronological order.

WHEN              WHO         WHAT, WHERE, WHY
------------     -------     -------------------------------------------------------------------------------

=================================================================*/

/*===========================================================================
 * include files
 ===========================================================================*/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

#include "quec_decoder.h"
#include "ql_decoder.h"

#include "ql_zo_malloc.h"   
#include "ql_imgdeal.h"

#include "newcheck.h"

#include "helios_fs.h"

#include "helios_debug.h"
#define DECODE_LOG(msg, ...)      custom_log(decode, msg, ##__VA_ARGS__)

/******************************************************************************
 *   Macro
 ******************************************************************************/
/*===========================================================================
 * Macro Definition
 ===========================================================================*/

#define DECODERING 1
#define NOT_DECODER 0
/*===========================================================================
 * Struct
 ===========================================================================*/

/*===========================================================================
 * Enum
 ===========================================================================*/

/*===========================================================================
 * Variate
 ===========================================================================*/

unsigned char ql_decoder_state = 0;
unsigned char decoder_data[500] = {0};
quec_decoder_symbol_type_t typ = 0;
int output_lenth = 0;
unsigned char decoder_version[20] = "QUEC DECODER 1.0";
unsigned char decoder_flag = NOT_DECODER;
int saveImg_count = 0;
/*===========================================================================
 * Functions
 ===========================================================================*/

void saveImg(unsigned char *raw, int width, int height, unsigned char flag)
{
    int fd = 0;
    char filename[60] = {0};

    sprintf(filename, "SD:decoder/img%d_%d_%d_%d.yuv", saveImg_count, flag, width, height);
    
    fd = Helios_fopen(filename, "wb+");
    Helios_fwrite(raw, (width*height), 1, fd);
    Helios_fclose(fd);
    //DECODE_LOG("Save image_%d_%d_%d_%d success", saveImg_count, flag, width, height);
}

int ql_image_decoder_qr_code(void* raw, int width, int height, unsigned char *data1)
{
    int i;
    quec_decoder_image_scanner_t *scanner = NULL;
    typ = QUEC_DECODER_NONE;
    /* create a reader */
    scanner = quec_decoder_image_scanner_create();
    /* configure the reader */
    quec_decoder_image_scanner_set_config(scanner, 0, QUEC_DECODER_CFG_ENABLE, 1);
    /* wrap image data */
    quec_decoder_image_t *image = quec_decoder_image_create();
    quec_decoder_image_set_format(image, *(int*)"Y800");
    quec_decoder_image_set_size(image, width, height);

    quec_decoder_image_set_data(image, raw, width* height, quec_decoder_image_free_data);

    /* scan the image for barcodes */
    int n = quec_decoder_scan_image_qr_code(scanner, image);
    if(n == 0)
    {
        // saveImg((unsigned char *)raw, width, height, 1);
        // saveImg_count++;
        memset(data1, 0, strlen((const char*)data1));
        quec_decoder_image_destroy(image);
        quec_decoder_image_scanner_destroy(scanner);
		//RTI_LOG("decoded fail");
        return false;
    }
	// saveImg((unsigned char *)raw, width, height, 2);
    //     saveImg_count++;
    /* extract results */
    const quec_decoder_symbol_t *symbol = quec_decoder_image_first_symbol(image);
    for(; symbol; symbol = quec_decoder_symbol_next(symbol)) 
    {
        /* do something useful with results */
        typ = quec_decoder_symbol_get_type(symbol);
        const char *data = quec_decoder_symbol_get_data(symbol);
        memset(data1, 0, strlen((const char*)data1));
        for(i=0; i<strlen(data); i++)
        {
            data1[i] = data[i];
        }
        data1[strlen(data)] = '\0';
        //RTI_LOG("decoded %s, n=%d, symbol \"%s\"", quec_decoder_get_symbol_name(typ), n, data);
        //QL_DECODER_LOG("symbol \"%x\"", data);
    }
    /* clean up */
    //saveImg((unsigned char *)raw, width, height, 2);
    //saveImg_count++;
    quec_decoder_image_destroy(image);
    quec_decoder_image_scanner_destroy(scanner);

    return true;
 }

int ql_image_decoder_bar_code(void* raw, int rawWidth, ImgData imgdata, unsigned char *data1)
{
#define HH 2
#define SET 1
	int i = 0, start_x = 0, start_y = 0, k = 0;
	int j = 0;
	Pt startPt = { 0 };
	int direction = 0;
	int  dx = 0, dy = 0, longSinAx16 = 0, longCosAx16 = 0;
	double  degree = 0;
	int width = 0, height = 0;
	int  pt1 = 0, pt2 = 0;
	quec_decoder_image_scanner_t *scanner = NULL;
	typ = QUEC_DECODER_NONE;
	/* create a reader */
	scanner = quec_decoder_image_scanner_create();
	/* configure the reader */
	quec_decoder_image_scanner_set_config(scanner, QUEC_DECODER_NONE, QUEC_DECODER_CFG_ENABLE, 1);
	/* wrap image data */
	quec_decoder_image_t *image = NULL;

	unsigned char *dst_data = (unsigned char *)zo_mymalloc(imgdata.length * HH);

	/*根据矩形四点查找起始点、判断像素行走向*/
	if (imgdata.pt[0].x == imgdata.pt[1].x || imgdata.pt[0].y == imgdata.pt[1].y) //矩形垂直或水平
	{
		height = abs(imgdata.pt[0].y - imgdata.pt[3].y);
		width = abs(imgdata.pt[0].x - imgdata.pt[3].x);
		if(height > width)  //垂直矩形
		{
			DECODE_LOG("direction = VERTICAL");
			direction = VERTICAL;
			start_x = (imgdata.pt[0].x + imgdata.pt[3].x) / 2;
			if (imgdata.pt[3].y > imgdata.pt[0].y)
			{
				start_y = imgdata.pt[0].y;
			}
			else
			{
				start_y = imgdata.pt[3].y;
			}

			imgdata.length = height;
			imgdata.wide = width;
		}
		else
		{
			DECODE_LOG("direction = HORIZONTAL");
			direction = HORIZONTAL;
			start_y = (imgdata.pt[0].y + imgdata.pt[3].y) / 2;
			if (imgdata.pt[3].x > imgdata.pt[0].x)
			{
				start_x = imgdata.pt[0].x;
			}
			else
			{
				start_x = imgdata.pt[3].x;
			}

			imgdata.length = width;
			imgdata.wide = height;
		}
		
	}
	else //倾斜方向矩形
	{
		DECODE_LOG("direction = NORMAL");
		direction = NORMAL;
		height = imgdata.wide;
		width = imgdata.length;

		if(width > height)
		{
			if (0 == (imgdata.pt[0].x - imgdata.pt[1].x))
			{
				imgdata.degree = PI / 2;
			}
			else
			{
				imgdata.degree = atan((double)(imgdata.pt[0].y - imgdata.pt[1].y) / (imgdata.pt[0].x - imgdata.pt[1].x));
			}
			imgdata.length = width;
			imgdata.wide = height;
			pt1 = imgdata.pt[0].x + imgdata.pt[2].x;
			pt2 = imgdata.pt[1].x + imgdata.pt[3].x;
			if (pt1 > pt2)
			{
				start_x = (imgdata.pt[1].x + imgdata.pt[3].x) / 2 * (1 << 16);
				start_y = (imgdata.pt[1].y + imgdata.pt[3].y) / 2 * (1 << 16);
			}
			else
			{
				start_x = (imgdata.pt[0].x + imgdata.pt[2].x) / 2 * (1 << 16);
				start_y = (imgdata.pt[0].y + imgdata.pt[2].y) / 2 * (1 << 16);
			}
		}
		else
		{
			if (0 == (imgdata.pt[0].x - imgdata.pt[2].x))
			{
				imgdata.degree = PI / 2;
			}
			else
			{
				imgdata.degree = atan((double)(imgdata.pt[0].y - imgdata.pt[2].y) / (imgdata.pt[0].x - imgdata.pt[2].x));
			}
			imgdata.length = height;
			imgdata.wide = width;
			pt1 = imgdata.pt[0].x + imgdata.pt[1].x;
			pt2 = imgdata.pt[2].x + imgdata.pt[3].x;
			if (pt1 > pt2)
			{
				start_x = (imgdata.pt[2].x + imgdata.pt[3].x) / 2 * (1 << 16);
				start_y = (imgdata.pt[2].y + imgdata.pt[3].y) / 2 * (1 << 16);
			}
			else
			{
				start_x = (imgdata.pt[0].x + imgdata.pt[1].x) / 2 * (1 << 16);
				start_y = (imgdata.pt[0].y + imgdata.pt[1].y) / 2 * (1 << 16);
			}
		}

		degree = imgdata.degree;
		longSinAx16 = (int)(sin(degree) * (1 << 16));
		longCosAx16 = (int)(cos(degree) * (1 << 16));	
		dx = (-longSinAx16);
		dy = (longCosAx16);
	}
	
	startPt.x = start_x;
	startPt.y = start_y;
	DECODE_LOG("bar code length = %d, wide = %d ", imgdata.length, imgdata.wide);
	DECODE_LOG("startPt = (%d, %d)", startPt.x, startPt.y);

    image = quec_decoder_image_create();
	quec_decoder_image_set_format(image, *(int*)"Y800");
	quec_decoder_image_set_size(image, imgdata.length, HH);

	while (j < imgdata.wide)
	{
		

		/*单行像素重构条形码*/
		//singlePixBuildBarCode(raw, dst_data, rawWidth, imgdata.length, 2, startPt);
		singlePixBuildBarCode_degree(raw, dst_data, rawWidth, imgdata.length, HH, startPt, longSinAx16, longCosAx16, direction);

		quec_decoder_image_set_data(image, dst_data, imgdata.length * HH, quec_decoder_image_free_data);

		/* scan the image for barcodes */
		int n = quec_decoder_scan_image_bar_code(scanner, image);
		if (n == 0)
		{
			//DEBUG("%d decoded fail \r\n", ii/SET);
			/*切换下一个像素点  间距为5个单位*/
			switch (direction)
			{
			case VERTICAL:
				if (j % 2 == 0)
				{
					k += 1;
					startPt.x = start_x + k * SET;
				}
				else
				{
					startPt.x = start_x - k * SET;
				}
				j += SET;
				break;
			case HORIZONTAL:
				if (j % 2 == 0)
				{
					k += 1;
					startPt.y = start_y + k * SET;
				}
				else
				{
					startPt.y = start_y - k * SET;
				}
				j += SET;
				break;
			case NORMAL:
				if (j % 2 == 0)
				{
					k += 1;
					startPt.x = start_x + k * SET * dx;
					startPt.y = start_y + k * SET * dy;
				}
				else
				{
					startPt.x = start_x - k * SET * dx;
					startPt.y = start_y - k * SET * dy;
				}

				j += SET;
				break;
			default:
				break;
			}
		}
		else
		{
			DECODE_LOG("startPt = (%d, %d), j = %d", startPt.x, startPt.y, j);
			/* extract results */
			const quec_decoder_symbol_t *symbol = quec_decoder_image_first_symbol(image);
			for (; symbol; symbol = quec_decoder_symbol_next(symbol))
			{
				/* do something useful with results */
				typ = quec_decoder_symbol_get_type(symbol);
				const char *data = quec_decoder_symbol_get_data(symbol);
				memset(data1, 0, strlen((const char*)data1));
				for (i = 0; i < strlen(data); i++)
				{
					data1[i] = data[i];
				}
				data1[strlen(data)] = '\0';
				//DECODE_LOG("%d symbol \"%s\" \r\n", ii / SET, data);
				quec_decoder_image_destroy(image);
				quec_decoder_image_scanner_destroy(scanner);

				return true;
			}
		}	
	}
	quec_decoder_image_destroy(image);
	quec_decoder_image_scanner_destroy(scanner);

	return false;
}

int ql_getResultType()
{
	// DECODE_TYPE_E ret = NONECODE;
	// if(typ == QUEC_DECODER_QRCODE)
	// 	ret = QRCODE;
	// else if(typ == QUEC_DECODER_CODE39)
	// 	ret = CODE39;
	// else if(typ == QUEC_DECODER_CODE128)
	// 	ret = CODE128;
	// else
	// 	ret = NONECODE;
		
    return 0;
}

int ql_getCodeLenth()
{
    return (int)output_lenth;
}

unsigned int ql_getResultLength(void)
{
    return strlen((const char*)decoder_data);
}

int ql_getDecoderResult(unsigned char * result)
{
	int i;

    int len = ql_getResultLength();
    if(len != 0)
    {
        for(i=0; i<len; i++)
        {
            result[i] = decoder_data[i];
        }
        result[len] = '\0';
        
        return true;
    }
    
    return false;
}

void ql_getDecoderVersion(unsigned char * version)
{
	int i;

    int len = strlen((const char*)decoder_version);
    for(i=0; i<len; i++)
        {
            version[i] = decoder_version[i];
        }
    version[len] = '\0';
}

unsigned int ql_initial_decoder(void)
{ 
    if(ql_decoder_state == 0)
    {
        zo_mem_init();
        ql_decoder_state = 1;
        return true;
    }
    return false;
}

void ql_destroydecoder(void)
{
    if(ql_decoder_state == 1)
    {
        zo_mem_release();
        ql_decoder_state = 0;
    }
}

//最新版本解码api，添加条形码判断检测
int ql_decoding_image(unsigned char* raw, int width, int height)
{
	if (decoder_flag == NOT_DECODER)
	{
		memset(decoder_data, 0, 500);
		decoder_flag = DECODERING;
		int result = 0;
        int d = 0;
		AreaData retdata = { 0 };
		ImgData outdata = { 0 };
		unsigned char *pYDataConvert = NULL;

		if ((width * height) > QVGA)
		{
			d = 2;
		}
		else
		{
			d = 1;
		}

        if(d == 2)
		{
			pYDataConvert = (unsigned char *)zo_mymalloc((width / d) * (height / d));
        	ql_gety_convert(raw, pYDataConvert, width, height, (width / d), (height / d), 0);
			retdata = ImgCheckCode(pYDataConvert, (width / d), (height / d), BLOCK_SIZE, 20); 
		}
		else
		{
			retdata = ImgCheckCode(raw, (width / d), (height / d), BLOCK_SIZE, 20); 
		}

		if(d == 2)
		{
			zo_myfree(pYDataConvert);
		}
            	
		//DEBUG("retdata.block_count %d \r\n", retdata.block_count);
		if (retdata.block_count == 0)
		{
			decoder_flag = NOT_DECODER;
			//DECODE_LOG("find code fail");
			return false;
		}
        //DECODE_LOG("length %d ", retdata.length);
	    //DECODE_LOG("wide %d ",retdata.wide);
		
		outdata = getDecoderImg(raw, width, retdata);

		if (outdata.code_check == NO_CODE)
		{
			// saveImg((unsigned char *)raw, width, height, 2);
            // saveImg_count++;
			decoder_flag = NOT_DECODER;
			//free(pYDataConvert);
			DECODE_LOG("find code fail");
			return false;
		}
		else if (outdata.code_check == BAR_CODE)
		{
            
			DECODE_LOG("bar code pt[0] = (%d, %d) ",outdata.pt[0].x, outdata.pt[0].y);
			DECODE_LOG("bar code pt[1] = (%d, %d) ",outdata.pt[1].x, outdata.pt[1].y);
			DECODE_LOG("bar code pt[2] = (%d, %d) ",outdata.pt[2].x, outdata.pt[2].y);
			DECODE_LOG("bar code pt[3] = (%d, %d) ",outdata.pt[3].x, outdata.pt[3].y);

			result = ql_image_decoder_bar_code(raw, width, outdata, decoder_data);
            if(result == false)
            {
                //保存640*480图像
                // saveImg((unsigned char *)raw, width, height, 1);
                // saveImg_count++;
            }
                
			decoder_flag = NOT_DECODER;
			return result;
		}
		else if (outdata.code_check == QR_CODE)
		{
			//saveImg(outdata.data, outdata.length, outdata.wide, 1);
            DECODE_LOG("qr code length = %d  wide = %d",outdata.length, outdata.wide);
			result = ql_image_decoder_qr_code(outdata.data, outdata.length, outdata.wide, decoder_data);
            if(result == false)
            {
                //保存640*480图像
                // saveImg((unsigned char *)raw, width, height, 0);
                // saveImg_count++;
            }
			else
			{
				// saveImg((unsigned char *)raw, width, height, 0);
        		// saveImg_count++;
			}
			decoder_flag = NOT_DECODER;
			return result;
		}
		else
		{
			decoder_flag = NOT_DECODER;
			DECODE_LOG("decoder find code error");
			return false;
		}
	}
	else
	{
		DECODE_LOG("decodering");
		return false;
	}
}

#if 1 //文件系统验证解码库解码
void getData(unsigned char *indata, unsigned char *outdata, int width, int height)
{
    int i = 0;
    for(i = 0;i<(width*height);i++)
    {
        outdata[i] = indata[i];
    } 
}

int ql_decoding_image_fs(unsigned char* raw, int width, int height)
{
    if(decoder_flag == NOT_DECODER)
    {
        memset(decoder_data, 0, 500);
        decoder_flag = DECODERING;
        int result = 0;
        unsigned char *get_data = NULL;

        get_data = (unsigned char *)zo_mymalloc((width*height));
        if(get_data == NULL)
        {
            decoder_flag = NOT_DECODER;
            DECODE_LOG("zo malloc fail");
            return false;
        }
        getData(raw, get_data, width, height);
        //ql_gaussian(get_data,get_data,width,height,4,1);
        //ql_sharpen(get_data,get_data,width,height);
        memset(decoder_data,0,500);
        result = ql_image_decoder_qr_code(get_data, width, height, decoder_data);
		if(result == false)
		{
			// saveImg((unsigned char *)raw, width, height, 0);
			// saveImg_count++;
		}
        decoder_flag = NOT_DECODER;
        return result;
    }
    else
    {
        return false;
    }
}
#endif



ql_errcode_decoder_e ql_qrdecode_get_decoder_result(ql_decoder_type_e* type, unsigned char* result, int* lenth)
{
	int iret = DECODER_ERROR;

    iret = ql_getDecoderResult(result);
	if (DECODER_SUCCESS == iret)
	{ 
        result[ql_getResultLength()] = '\0';
        *type = ql_getResultType();
        *lenth = ql_getCodeLenth();
	    return QL_DECODER_SUCCESS;
	}
    
    *type = QL_DECODER_TYPE_NONE;
    DECODE_LOG("Get failed");
	return QL_DECODER_GET_RESULT_ERR;	
}