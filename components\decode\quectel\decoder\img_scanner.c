
#include "quec_decoder_config.h"
//#include <unistd.h>
#ifdef HAVE_INTTYPES_H
# include <inttypes.h>
#endif
#include <stdlib.h>     /* malloc, free */
#include <time.h>       /* clock_gettime */
//#include <sys/time.h>   /* gettimeofday */
#include <string.h>     /* memcmp, memset, memcpy */
#include <assert.h>

#include "quec_decoder.h"
//#include "error.h"
#include "image.h"
#ifdef ENABLE_QRCODE
#include "qrcode.h"
#endif
#include "img_scanner.h"

#include "ql_zo_malloc.h" 
#include "stdint.h"


#if 1
#define ASSERT_POS 
#else
# define ASSERT_POS
#endif

/* FIXME cache setting configurability */

/* number of times the same result must be detected
 * in "nearby" images before being reported
 */
#define CACHE_CONSISTENCY    3 /* images */

/* time interval for which two images are considered "nearby"
 */
#define CACHE_PROXIMITY   1000 /* ms */

/* time that a result must *not* be detected before
 * it will be reported again
 */
#define CACHE_HYSTERESIS  2000 /* ms */

/* time after which cache entries are invalidated
 */
#define CACHE_TIMEOUT     (CACHE_HYSTERESIS * 2) /* ms */

#define NUM_SCN_CFGS (QUEC_DECODER_CFG_Y_DENSITY - QUEC_DECODER_CFG_X_DENSITY + 1)

#define CFG(iscn, cfg) ((iscn)->configs[(cfg) - QUEC_DECODER_CFG_X_DENSITY])
#define TEST_CFG(iscn, cfg) (((iscn)->config >> ((cfg) - QUEC_DECODER_CFG_POSITION)) & 1)

#ifndef NO_STATS
#define STAT(x) iscn->stat_##x++
#else
#define STAT(...)
#define dump_stats(...)
#endif

#define RECYCLE_BUCKETS     5

typedef struct recycle_bucket_s {
    int nsyms;
    quec_decoder_symbol_t *head;
} recycle_bucket_t;

/* image scanner state */
struct quec_decoder_image_scanner_s {
    quec_decoder_scanner_t *scn;        /* associated linear intensity scanner */
    quec_decoder_decoder_t *dcode;      /* associated symbol decoder */
#ifdef ENABLE_QRCODE
    qr_reader *qr;              /* QR Code 2D reader */
#endif

    const void *userdata;       /* application data */
    /* user result callback */
    quec_decoder_image_data_handler_t *handler;

    unsigned long time;         /* scan start time */
    quec_decoder_image_t *img;          /* currently scanning image *root* */
    int dx, dy, du, umin, v;    /* current scan direction */
    quec_decoder_symbol_set_t *syms;    /* previous decode results */
    /* recycled symbols in 4^n size buckets */
    recycle_bucket_t recycle[RECYCLE_BUCKETS];

    int enable_cache;           /* current result cache state */
    quec_decoder_symbol_t *cache;       /* inter-image result cache entries */

    /* configuration settings */
    unsigned config;            /* config flags */
    int configs[NUM_SCN_CFGS];  /* int valued configurations */

#ifndef NO_STATS
    int stat_syms_new;
    int stat_iscn_syms_inuse, stat_iscn_syms_recycle;
    int stat_img_syms_inuse, stat_img_syms_recycle;
    int stat_sym_new;
    int stat_sym_recycle[RECYCLE_BUCKETS];
#endif
};

void _quec_decoder_image_scanner_recycle_syms (quec_decoder_image_scanner_t *iscn,
                                       quec_decoder_symbol_t *sym)
{
    quec_decoder_symbol_t *next = NULL;
    for(; sym; sym = next) {
        next = sym->next;
        if(sym->refcnt && _quec_decoder_refcnt(&sym->refcnt, -1)) {
            /* unlink referenced symbol */
            /* FIXME handle outstanding component refs (currently unsupported)
             */
            sym->next = NULL;
        }
        else {
            /* recycle unreferenced symbol */
            if(!sym->data_alloc) {
                sym->data = NULL;
                sym->datalen = 0;
            }
            if(sym->syms) 
            {
                _quec_decoder_image_scanner_recycle_syms(iscn, sym->syms->head);
                sym->syms->head = NULL;
                _quec_decoder_symbol_set_free(sym->syms);
                sym->syms = NULL;
            }
            int i;
            for(i = 0; i < RECYCLE_BUCKETS; i++)
                if(sym->data_alloc < 1 << (i * 2))
                    break;
            if(i == RECYCLE_BUCKETS) {
                free(sym->data);
                sym->data = NULL;
                sym->data_alloc = 0;
                i = 0;
            }
            recycle_bucket_t *bucket = &iscn->recycle[i];
            /* FIXME cap bucket fill */
            bucket->nsyms++;
            sym->next = bucket->head;
            bucket->head = sym;
        }
    }
}

static inline int recycle_syms (quec_decoder_image_scanner_t *iscn,
                                quec_decoder_symbol_set_t *syms)
{
    if(_quec_decoder_refcnt(&syms->refcnt, -1))
        return(1);

    _quec_decoder_image_scanner_recycle_syms(iscn, syms->head);
    syms->head = syms->tail = NULL;
    syms->nsyms = 0;
    return(0);
}

inline void quec_decoder_image_scanner_recycle_image (quec_decoder_image_scanner_t *iscn,
                                              quec_decoder_image_t *img)
{
    quec_decoder_symbol_set_t *syms = iscn->syms;
    if(syms && syms->refcnt) {
        if(recycle_syms(iscn, syms)) {
            STAT(iscn_syms_inuse);
            iscn->syms = NULL;
        }
        else
            STAT(iscn_syms_recycle);
    }

    syms = img->syms;
    img->syms = NULL;
    if(syms && recycle_syms(iscn, syms)) {
        STAT(img_syms_inuse);
        syms = iscn->syms;
    }
    else if(syms) {
        STAT(img_syms_recycle);

        /* select one set to resurrect, destroy the other */
        if(iscn->syms) {
            _quec_decoder_symbol_set_free(syms);
            syms = iscn->syms;
        }
        else
            iscn->syms = syms;
    }
}

inline quec_decoder_symbol_t*
_quec_decoder_image_scanner_alloc_sym (quec_decoder_image_scanner_t *iscn,
                               quec_decoder_symbol_type_t type,
                               int datalen)
{
    /* recycle old or alloc new symbol */
    int i;
    for(i = 0; i < RECYCLE_BUCKETS - 1; i++)
        if(datalen <= 1 << (i * 2))
            break;

    quec_decoder_symbol_t *sym = NULL;
    for(; i > 0; i--)
        if((sym = iscn->recycle[i].head)) {
            STAT(sym_recycle[i]);
            break;
        }

    if(sym) {
        iscn->recycle[i].head = sym->next;
        sym->next = NULL;
        iscn->recycle[i].nsyms--;
    }
    else {
        sym = calloc(1, sizeof(quec_decoder_symbol_t));
        STAT(sym_new);
    }

    /* init new symbol */
    sym->type = type;
    sym->quality = 1;
    sym->npts = 0;
    sym->cache_count = 0;
    sym->time = iscn->time;

    if(datalen > 0) {
        sym->datalen = datalen - 1;
        if(sym->data_alloc < datalen) {
            if(sym->data)
                free(sym->data);
            sym->data_alloc = datalen;
            sym->data = malloc(datalen);
        }
    }
    else {
        if(sym->data)
            free(sym->data);
        sym->data = NULL;
        sym->datalen = sym->data_alloc = 0;
    }
    return(sym);
}

static inline quec_decoder_symbol_t *cache_lookup (quec_decoder_image_scanner_t *iscn,
                                           quec_decoder_symbol_t *sym)
{
    /* search for matching entry in cache */
    quec_decoder_symbol_t **entry = &iscn->cache;
    while(*entry) {
        if((*entry)->type == sym->type &&
           (*entry)->datalen == sym->datalen &&
           !memcmp((*entry)->data, sym->data, sym->datalen))
            break;
        if((sym->time - (*entry)->time) > CACHE_TIMEOUT) {
            /* recycle stale cache entry */
            quec_decoder_symbol_t *next = (*entry)->next;
            (*entry)->next = NULL;
            _quec_decoder_image_scanner_recycle_syms(iscn, *entry);
            *entry = next;
        }
        else
            entry = &(*entry)->next;
    }
    return(*entry);
}

static inline void cache_sym (quec_decoder_image_scanner_t *iscn,
                              quec_decoder_symbol_t *sym)
{
    if(iscn->enable_cache) {
        quec_decoder_symbol_t *entry = cache_lookup(iscn, sym);
        if(!entry) {
            /* FIXME reuse sym */
            entry = _quec_decoder_image_scanner_alloc_sym(iscn, sym->type,
                                                  sym->datalen + 1);
            memcpy(entry->data, sym->data, sym->datalen);
            entry->time = sym->time - CACHE_HYSTERESIS;
            entry->cache_count = -CACHE_CONSISTENCY;
            /* add to cache */
            entry->next = iscn->cache;
            iscn->cache = entry;
        }

        /* consistency check and hysteresis */
        unsigned int age = sym->time - entry->time;
        entry->time = sym->time;
        int near_thresh = (age < CACHE_PROXIMITY);
        int far_thresh = (age >= CACHE_HYSTERESIS);
        int dup = (entry->cache_count >= 0);
        if((!dup && !near_thresh) || far_thresh)
            entry->cache_count = -CACHE_CONSISTENCY;
        else if(dup || near_thresh)
            entry->cache_count++;

        sym->cache_count = entry->cache_count;
    }
    else
        sym->cache_count = 0;
}

void _quec_decoder_image_scanner_add_sym(quec_decoder_image_scanner_t *iscn,
                                 quec_decoder_symbol_t *sym)
{
    cache_sym(iscn, sym);

    quec_decoder_symbol_set_t *syms = iscn->syms;
    if(sym->cache_count || !syms->tail) {
        sym->next = syms->head;
        syms->head = sym;
    }
    else {
        sym->next = syms->tail->next;
        syms->tail->next = sym;
    }

    if(!sym->cache_count)
        syms->nsyms++;
    else if(!syms->tail)
        syms->tail = sym;

    _quec_decoder_symbol_refcnt(sym, 1);
}

#ifdef ENABLE_QRCODE
extern qr_finder_line *_quec_decoder_decoder_get_qr_finder_line(quec_decoder_decoder_t*);

# define QR_FIXED(v, rnd) ((((v) << 1) + (rnd)) << (QR_FINDER_SUBPREC - 1))
# define PRINT_FIXED(val, prec) \
    ((val) >> (prec)),         \
        (1000 * ((val) & ((1 << (prec)) - 1)) / (1 << (prec)))

static inline void qr_handler (quec_decoder_image_scanner_t *iscn)
{
    qr_finder_line *line = _quec_decoder_decoder_get_qr_finder_line(iscn->dcode);

    unsigned u = quec_decoder_scanner_get_edge(iscn->scn, line->pos[0],
                                       QR_FINDER_SUBPREC);
    line->boffs = u - quec_decoder_scanner_get_edge(iscn->scn, line->boffs,
                                            QR_FINDER_SUBPREC);
    line->len = quec_decoder_scanner_get_edge(iscn->scn, line->len,
                                      QR_FINDER_SUBPREC);
    line->eoffs = quec_decoder_scanner_get_edge(iscn->scn, line->eoffs,
                                        QR_FINDER_SUBPREC) - line->len;
    line->len -= u;

    u = QR_FIXED(iscn->umin, 0) + iscn->du * u;
    if(iscn->du < 0) {
        u -= line->len;
        int tmp = line->boffs;
        line->boffs = line->eoffs;
        line->eoffs = tmp;
    }
    int vert = !iscn->dx;
    line->pos[vert] = u;
    line->pos[!vert] = QR_FIXED(iscn->v, 1);

    _quec_decoder_qr_found_line(iscn->qr, vert, line);
}
#endif

static void symbol_handler (quec_decoder_decoder_t *dcode)
{
    quec_decoder_image_scanner_t *iscn = quec_decoder_decoder_get_userdata(dcode);
    quec_decoder_symbol_type_t type = quec_decoder_decoder_get_type(dcode);
    /* FIXME assert(type == QUEC_DECODER_PARTIAL) */
    /* FIXME debug flag to save/display all PARTIALs */
    if(type <= QUEC_DECODER_PARTIAL)
        return;

#ifdef ENABLE_QRCODE
    if(type == QUEC_DECODER_QRCODE) {
        qr_handler(iscn);
        return;
    }
#else
    
#endif

    const char *data = quec_decoder_decoder_get_data(dcode);
    unsigned datalen = quec_decoder_decoder_get_data_length(dcode);

    int x = 0, y = 0;
    if(TEST_CFG(iscn, QUEC_DECODER_CFG_POSITION)) {
        /* tmp position fixup */
        int w = quec_decoder_scanner_get_width(iscn->scn);
        int u = iscn->umin + iscn->du * quec_decoder_scanner_get_edge(iscn->scn, w, 0);
        if(iscn->dx) {
            x = u;
            y = iscn->v;
        }
        else {
            x = iscn->v;
            y = u;
        }
    }

    /* FIXME need better symbol matching */
    quec_decoder_symbol_t *sym;
    for(sym = iscn->syms->head; sym; sym = sym->next)
        if(sym->type == type &&
           sym->datalen == datalen &&
           !memcmp(sym->data, data, datalen)) {
            sym->quality++;
            if(TEST_CFG(iscn, QUEC_DECODER_CFG_POSITION))
                /* add new point to existing set */
                /* FIXME should be polygon */
                sym_add_point(sym, x, y);
            return;
        }

    sym = _quec_decoder_image_scanner_alloc_sym(iscn, type, datalen + 1);
    /* FIXME grab decoder buffer */
    memcpy(sym->data, data, datalen + 1);

    /* initialize first point */
    if(TEST_CFG(iscn, QUEC_DECODER_CFG_POSITION))
        sym_add_point(sym, x, y);

    _quec_decoder_image_scanner_add_sym(iscn, sym);
}

quec_decoder_image_scanner_t *quec_decoder_image_scanner_create ()
{
    quec_decoder_image_scanner_t *iscn = calloc(1, sizeof(quec_decoder_image_scanner_t));
	memset(iscn,0,sizeof(quec_decoder_image_scanner_t));
    //quec_decoder_image_scanner_t *iscn = zo_mymalloc(sizeof(quec_decoder_image_scanner_t));  //mymalloc_test
    if(!iscn)
        return(NULL);
    iscn->dcode = quec_decoder_decoder_create();
    iscn->scn = quec_decoder_scanner_create(iscn->dcode);
    if(!iscn->dcode || !iscn->scn) {
        quec_decoder_image_scanner_destroy(iscn);
        return(NULL);
    }
    quec_decoder_decoder_set_userdata(iscn->dcode, iscn);
    quec_decoder_decoder_set_handler(iscn->dcode, symbol_handler);

#ifdef ENABLE_QRCODE
    iscn->qr = _quec_decoder_qr_create();
#endif

    /* apply default configuration */
    CFG(iscn, QUEC_DECODER_CFG_X_DENSITY) = 1;
    CFG(iscn, QUEC_DECODER_CFG_Y_DENSITY) = 1;
    quec_decoder_image_scanner_set_config(iscn, 0, QUEC_DECODER_CFG_POSITION, 1);
    return(iscn);
}

#ifndef NO_STATS
static inline void dump_stats (const quec_decoder_image_scanner_t *iscn)
{

}
#endif

void quec_decoder_image_scanner_destroy (quec_decoder_image_scanner_t *iscn)
{
    dump_stats(iscn);
    if(iscn->syms) {
        if(iscn->syms->refcnt)
            quec_decoder_symbol_set_ref(iscn->syms, -1);
        else
            _quec_decoder_symbol_set_free(iscn->syms);
        iscn->syms = NULL;
    }
    if(iscn->scn)
        quec_decoder_scanner_destroy(iscn->scn);
    iscn->scn = NULL;
    if(iscn->dcode)
        quec_decoder_decoder_destroy(iscn->dcode);
    iscn->dcode = NULL;
    int i;
    for(i = 0; i < RECYCLE_BUCKETS; i++) {
        quec_decoder_symbol_t *sym, *next;
        for(sym = iscn->recycle[i].head; sym; sym = next) {
            next = sym->next;
            _quec_decoder_symbol_free(sym);
        }
    }
#ifdef ENABLE_QRCODE
    if(iscn->qr) {
        _quec_decoder_qr_destroy(iscn->qr);
        iscn->qr = NULL;
    }
#endif

    free(iscn);
    //zo_myfree(iscn);//mymalloc_test

}

quec_decoder_image_data_handler_t*
quec_decoder_image_scanner_set_data_handler (quec_decoder_image_scanner_t *iscn,
                                     quec_decoder_image_data_handler_t *handler,
                                     const void *userdata)
{
    quec_decoder_image_data_handler_t *result = iscn->handler;
    iscn->handler = handler;
    iscn->userdata = userdata;
    return(result);
}

int quec_decoder_image_scanner_set_config (quec_decoder_image_scanner_t *iscn,
                                   quec_decoder_symbol_type_t sym,
                                   quec_decoder_config_t cfg,
                                   int val)
{
    if(cfg < QUEC_DECODER_CFG_POSITION)
        return(quec_decoder_decoder_set_config(iscn->dcode, sym, cfg, val));

    if(sym > QUEC_DECODER_PARTIAL)
        return(1);

    if(cfg >= QUEC_DECODER_CFG_X_DENSITY && cfg <= QUEC_DECODER_CFG_Y_DENSITY) {

        CFG(iscn, cfg) = val;
        return(0);
    }

    if(cfg > QUEC_DECODER_CFG_POSITION)
        return(1);
    cfg -= QUEC_DECODER_CFG_POSITION;

    if(!val)
        iscn->config &= ~(1 << cfg);
    else if(val == 1)
        iscn->config |= (1 << cfg);
    else
        return(1);

    return(0);
}

void quec_decoder_image_scanner_enable_cache (quec_decoder_image_scanner_t *iscn,
                                      int enable)
{
    if(iscn->cache) {
        /* recycle all cached syms */
        _quec_decoder_image_scanner_recycle_syms(iscn, iscn->cache);
        iscn->cache = NULL;
    }
    iscn->enable_cache = (enable) ? 1 : 0;
}

const quec_decoder_symbol_set_t *
quec_decoder_image_scanner_get_results (const quec_decoder_image_scanner_t *iscn)
{
    return(iscn->syms);
}

static inline void quiet_border(quec_decoder_image_scanner_t *iscn, unsigned char flag)
{
    /* flush scanner pipeline */
    quec_decoder_scanner_t *scn = iscn->scn;
    quec_decoder_scanner_flush(scn, flag);
    quec_decoder_scanner_flush(scn, flag);
    quec_decoder_scanner_new_scan(scn, flag);
}

#define movedelta(dx, dy) do {                  \
        x += (dx);                              \
        y += (dy);                              \
        p += (dx) + ((intptr_t)(dy) * w);       \
    } while(0);

int quec_decoder_scan_image_bar_code (quec_decoder_image_scanner_t *iscn,
                     quec_decoder_image_t *img)
{
    iscn->time = 0;

    /* get grayscale image, convert if necessary */
    if(img->format != fourcc('Y','8','0','0') &&
       img->format != fourcc('G','R','E','Y'))
        return(-1);
    iscn->img = img;

    /* recycle previous scanner and image results */
    quec_decoder_image_scanner_recycle_image(iscn, img);
    quec_decoder_symbol_set_t *syms = iscn->syms;
    if(!syms) {
        syms = iscn->syms = _quec_decoder_symbol_set_create();
        STAT(syms_new);
        quec_decoder_symbol_set_ref(syms, 1);
    }
    else
        quec_decoder_symbol_set_ref(syms, 2);
    img->syms = syms;

    unsigned w = img->width;
    unsigned h = img->height;
    const unsigned char *data = img->data;

    quec_decoder_scanner_t *scn = iscn->scn;

    int density = CFG(iscn, QUEC_DECODER_CFG_Y_DENSITY);
    if(density > 0) {
        const unsigned char *p = data;
        int x = 0, y = 0;
        iscn->dy = 0;

        int border = (((h - 1) % density) + 1) / 2;
        if(border > h / 2)
            border = h / 2;
        movedelta(0, border);
        iscn->v = y;

        quec_decoder_scanner_new_scan(scn, BARCODE);

        while(y < h) {
            iscn->dx = iscn->du = 1;
            iscn->umin = 0;
            while(x < w) {
                unsigned char d = *p;
                movedelta(1, 0);
                quec_decoder_scan_y(scn, d, BARCODE);
            }
            ASSERT_POS;
            quiet_border(iscn, BARCODE);

            movedelta(-1, density);
            iscn->v = y;
            if(y >= h)
                break;

            iscn->dx = iscn->du = -1;
            iscn->umin = w;
            while(x >= 0) {
                unsigned char d = *p;
                movedelta(-1, 0);
                quec_decoder_scan_y(scn, d, BARCODE);
            }
            ASSERT_POS;
            quiet_border(iscn, BARCODE);

            movedelta(1, density);
            iscn->v = y;
        }
    }
    iscn->dx = 0;

    iscn->img = NULL;

    /* FIXME tmp hack to filter bad EAN results */
    if(syms->nsyms && !iscn->enable_cache &&
       (density == 1 || CFG(iscn, QUEC_DECODER_CFG_Y_DENSITY) == 1)) {
        quec_decoder_symbol_t **symp = &syms->head, *sym;
        while((sym = *symp)) {
            if(sym->type < QUEC_DECODER_I25 && sym->type > QUEC_DECODER_PARTIAL &&
               sym->quality < 3) {
                /* recycle */
                *symp = sym->next;
                syms->nsyms--;
                sym->next = NULL;
                _quec_decoder_image_scanner_recycle_syms(iscn, sym);
            }
            else
                symp = &sym->next;
        }
    }

    if(syms->nsyms && iscn->handler)
        iscn->handler(img, iscn->userdata);
    return(syms->nsyms);
}

int quec_decoder_scan_image_qr_code (quec_decoder_image_scanner_t *iscn,
                     quec_decoder_image_t *img)
{
    iscn->time = 0;

    #ifdef ENABLE_QRCODE
    _quec_decoder_qr_reset(iscn->qr);
    #endif

    /* get grayscale image, convert if necessary */
    if(img->format != fourcc('Y','8','0','0') &&
       img->format != fourcc('G','R','E','Y'))
        return(-1);
    iscn->img = img;

    /* recycle previous scanner and image results */
    quec_decoder_image_scanner_recycle_image(iscn, img);
    quec_decoder_symbol_set_t *syms = iscn->syms;
    if(!syms) {
        syms = iscn->syms = _quec_decoder_symbol_set_create();
        STAT(syms_new);
        quec_decoder_symbol_set_ref(syms, 1);
    }
    else
        quec_decoder_symbol_set_ref(syms, 2);
    img->syms = syms;

    unsigned w = img->width;
    unsigned h = img->height;
    const unsigned char *data = img->data;

    quec_decoder_scanner_t *scn = iscn->scn;

    int density = CFG(iscn, QUEC_DECODER_CFG_Y_DENSITY);
    if(density > 0) {
        const unsigned char *p = data;
        int x = 0, y = 0;
        iscn->dy = 0;

        int border = (((h - 1) % density) + 1) / 2;
        if(border > h / 2)
            border = h / 2;
        movedelta(0, border);
        iscn->v = y;

        quec_decoder_scanner_new_scan(scn, QRCODE);
        while(y < h) {
            iscn->dx = iscn->du = 1;
            iscn->umin = 0;
            while(x < w) {
                unsigned char d = *p;
                movedelta(1, 0);
                quec_decoder_scan_y(scn, d, QRCODE);
            }
            ASSERT_POS;
            quiet_border(iscn, QRCODE);

            movedelta(-1, density);
            iscn->v = y;
            if(y >= h)
                break;

            iscn->dx = iscn->du = -1;
            iscn->umin = w;
            while(x >= 0) {
                unsigned char d = *p;
                movedelta(-1, 0);
                quec_decoder_scan_y(scn, d, QRCODE);
            }
            ASSERT_POS;
            quiet_border(iscn, QRCODE);

            movedelta(1, density);
            iscn->v = y;
        }
    }
    iscn->dx = 0;

    density = CFG(iscn, QUEC_DECODER_CFG_X_DENSITY);
    if(density > 0) {
        const unsigned char *p = data;
        int x = 0, y = 0;

        int border = (((w - 1) % density) + 1) / 2;
        if(border > w / 2)
            border = w / 2;
        movedelta(border, 0);
        iscn->v = x;

        while(x < w) {
            iscn->dy = iscn->du = 1;
            iscn->umin = 0;
            while(y < h) {
                unsigned char d = *p;
                movedelta(0, 1);
                quec_decoder_scan_y(scn, d, QRCODE);
            }
            ASSERT_POS;
            quiet_border(iscn, QRCODE);

            movedelta(density, -1);
            iscn->v = x;
            if(x >= w)
                break;

            iscn->dy = iscn->du = -1;
            iscn->umin = h;
            while(y >= 0) {
                unsigned char d = *p;
                movedelta(0, -1);
                quec_decoder_scan_y(scn, d, QRCODE);
            }
            ASSERT_POS;
            quiet_border(iscn, QRCODE);

            movedelta(density, 1);
            iscn->v = x;
        }
    }
    iscn->dy = 0;
    iscn->img = NULL;

    #ifdef ENABLE_QRCODE
    _quec_decoder_qr_decode(iscn->qr, iscn, img);
    #endif

    /* FIXME tmp hack to filter bad EAN results */
    if(syms->nsyms && !iscn->enable_cache &&
       (density == 1 || CFG(iscn, QUEC_DECODER_CFG_Y_DENSITY) == 1)) {
        quec_decoder_symbol_t **symp = &syms->head, *sym;
        while((sym = *symp)) {
            if(sym->type < QUEC_DECODER_I25 && sym->type > QUEC_DECODER_PARTIAL &&
               sym->quality < 3) {
                /* recycle */
                *symp = sym->next;
                syms->nsyms--;
                sym->next = NULL;
                _quec_decoder_image_scanner_recycle_syms(iscn, sym);
            }
            else
                symp = &sym->next;
        }
    }

    if(syms->nsyms && iscn->handler)
        iscn->handler(img, iscn->userdata);
    return(syms->nsyms);
}

