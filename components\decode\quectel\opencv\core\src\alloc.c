#include "stdio.h"
#include "stdlib.h"
#include <string.h>
#include "ql_zo_malloc.h"

//unsigned char membase[MEM_MAX_SIZE];			
//unsigned short memmapbase[MEM_ALLOC_TABLE_SIZE];			

unsigned char *membase = NULL;			
unsigned short *memmapbase = NULL;

const unsigned int memtblsize=MEM_ALLOC_TABLE_SIZE;		
const unsigned int memblksize=MEM_BLOCK_SIZE;			
const unsigned int memsize=MEM_MAX_SIZE;					

struct _m_mallco_dev mallco_dev=
{
	zo_mem_init,			
	mem_perused,		
	NULL,			
	NULL,			
	0,  				
};


void mymemcpy(void *des,void *src,unsigned int n)  
{  
    unsigned char *xdes=des;
	unsigned char *xsrc=src; 
    while(n--)*xdes++=*xsrc++;  
}  

void mymemset(void *s,unsigned char c,unsigned int count)  
{  
    unsigned char *xs = s;  
    while(count--)*xs++=c;  
}	   
 
void zo_mem_init(void)  
{  
    membase = (unsigned char *)calloc(MEM_MAX_SIZE, sizeof(unsigned char));
    memmapbase = (unsigned short *)calloc(MEM_ALLOC_TABLE_SIZE, sizeof(unsigned short));

    mallco_dev.membase = membase;
    mallco_dev.memmap = memmapbase;
    
    mymemset(mallco_dev.memmap, 0,memtblsize*2); 
	mymemset(mallco_dev.membase, 0,memsize);	  
	mallco_dev.memrdy=1;						
}  

void zo_mem_release(void)
{
    if(mallco_dev.memrdy == 1)
    {
    	if(membase)
    	{
        	free(membase);
    	}
		if(memmapbase)
		{
        	free(memmapbase);
		}
        mallco_dev.membase = NULL;
        mallco_dev.memmap = NULL;
        mallco_dev.memrdy=0;
    }
}

unsigned char mem_perused(void)  
{  
    unsigned int used=0;  
    unsigned int i;  
    for(i=0;i<memtblsize;i++)  
    {  
        if(mallco_dev.memmap[i])used++; 
    } 
    return (used*100)/(memtblsize);  
}  

unsigned int zo_mem_malloc(unsigned int size)  
{  
    signed long offset=0;  
    unsigned short nmemb;	  
	unsigned short cmemb=0;
    unsigned int i;  
    if(!mallco_dev.memrdy)mallco_dev.init();	
    if(size==0)return 0XFFFFFFFF;				
    nmemb=size/memblksize;  					
    if(size%memblksize)nmemb++;  
    for(offset=memtblsize-1;offset>=0;offset--)	 
    {     
		if(!mallco_dev.memmap[offset])cmemb++;	
		else cmemb=0;							
		if(cmemb==nmemb)						
		{
            for(i=0;i<nmemb;i++)  				
            {  
                mallco_dev.memmap[offset+i]=nmemb;  
            }  
            return (offset*memblksize);			  
		}
    }  
    return 0XFFFFFFFF;
}  
 
unsigned char zo_mem_free(unsigned int offset)  
{  
    int i;  
    if(!mallco_dev.memrdy)
	{
		mallco_dev.init();    
        return 1;
    }  
    if(offset<memsize)
    {  
        int index=offset/memblksize;		 
        int nmemb=mallco_dev.memmap[index];	
        for(i=0;i<nmemb;i++)  				
        {  
            mallco_dev.memmap[index+i]=0;  
        }
        return 0;  
    }else return 2;
}  

void zo_myfree(void *ptr)  
{  
	unsigned int offset;  
    if(ptr==NULL)return;  
 	offset=(unsigned int)ptr-(unsigned int)mallco_dev.membase;  
    zo_mem_free(offset);	  
}  

void *zo_mymalloc(unsigned int size)  
{  
    unsigned int offset;  									      
	offset=zo_mem_malloc(size);  	   				   
    if(offset==0XFFFFFFFF)
    {
        return NULL;  

    }
    else return (void*)((unsigned int)mallco_dev.membase+offset);  
}  

void *zo_myrealloc(void *ptr,unsigned int size)  
{  
    unsigned int offset;  
    offset=zo_mem_malloc(size);  
    if(offset==0XFFFFFFFF)return NULL;     
    else  
    {  									   
	    mymemcpy((void*)((unsigned int)mallco_dev.membase+offset),ptr,size);	
        zo_myfree(ptr);  											  	
        return (void*)((unsigned int)mallco_dev.membase+offset);  			
    }  
}

#if 0
unsigned char** alignPtr(unsigned char** ptr, int n)
{
	return (unsigned char**)(((size_t)ptr + n - 1) & -n);
}


void* fastMalloc(size_t size)
{
	unsigned char* udata = (unsigned char*)malloc(size + sizeof(void*) + CV_MALLOC_ALIGN);
	if (!udata)
		return NULL;
	unsigned char *adata = alignPtr((unsigned char**)udata + 1, CV_MALLOC_ALIGN);
	int *bdata = adata - 4;
	*bdata = udata;
	//printf("adata = %x  bdata = %x  *bdata = %x\r\n", adata, bdata, *bdata);
	return adata;
}

void fastFree(void* ptr)
{
	if (ptr)
	{
		/*uchar **p = (uchar *)ptr;
		int pp = (*p - 4);
		int *ppp = (int *)pp;
		int *pppp = (int *)(*ppp);*/

		/*uchar **p = (uchar *)ptr;
		int *pp = (int *)(*p - 4);
		printf("ptr = %x  (uchar *)ptr =%x  p = %x  *p = %x  *pp = %x\r\n", ptr, (uchar *)ptr, p, *p, *pp);
		free(*pp);*/

		int *p = (*((int *)ptr) - 4);
		//printf("(*((int *)ptr)-4) = %x  p = %x  *p = %x\r\n", (*((int *)ptr)-4), p, *p);

		free(*p);
	}
}

void* cvAlloc(size_t size)
{
	return fastMalloc(size);
}

void cvFree_(void* ptr)
{
	fastFree(ptr);
}
#endif
