/**  @file
  ql_decoder.h

  @brief
  This file is decoder api.

*/

/*================================================================
  Copyright (c) 2020 Quectel Wireless Solution, Co., Ltd.  All Rights Reserved.
  Quectel Wireless Solution Proprietary and Confidential.
=================================================================*/
/*=================================================================

                        EDIT HISTORY FOR MODULE

This section contains comments describing changes made to the module.
Notice that changes are listed in reverse chronological order.

WHEN              WHO         WHAT, WHERE, WHY
------------     -------     -------------------------------------------------------------------------------

=================================================================*/

#ifndef _QL_DECODER_H
#define _QL_DECODER_H

#ifdef __cplusplus
extern "C" {
#endif

/*===========================================================================
 * include files
 ===========================================================================*/


/*===========================================================================
 * Macro Definition
 ===========================================================================*/

/*===========================================================================
 * Struct
 ===========================================================================*/



/*===========================================================================
 * Enum
 ===========================================================================*/
    
/*===========================================================================
 * Variate
 ===========================================================================*/
 
/*===========================================================================
 * Functions
 ===========================================================================*/

 #define DECODER_INITIAL_FAILED  0
#define DECODER_INITIAL  		1
    
#define DECODER_ERROR			0
#define DECODER_SUCCESS			1
#define DECODER_ERROR_NONE		1


typedef enum
{
    QL_DECODER_SUCCESS,

    QL_DECODER_INIT_ERR,
    QL_DECODER_ERR,
    QL_DECODER_GET_RESULT_ERR,
    QL_DECODER_GET_RESULT_LENGTH_ERR,
    QL_DECODER_DESTROY_ERR,
}ql_errcode_decoder_e;



typedef enum
{
    QL_DECODER_TYPE_CODE39 = 0,
    QL_DECODER_TYPE_CODE128,
    QL_DECODER_TYPE_QR_CODE,

    QL_DECODER_TYPE_NONE = 0xff,
}ql_decoder_type_e;

int ql_decoding_image(unsigned char* raw, int width, int height);
unsigned int ql_initial_decoder(void);
void ql_destroydecoder(void);
unsigned int ql_getResultLength(void);
int ql_getDecoderResult(unsigned char * result);
int ql_getResultType(void);
void ql_getDecoderVersion(unsigned char * version);
int ql_getCodeLenth(void);
int ql_decoding_image_fs(unsigned char* raw, int width, int height);
int ql_decoding_image_new(unsigned char* raw, int width, int height);

ql_errcode_decoder_e ql_qrdecode_get_decoder_result(ql_decoder_type_e* type, unsigned char* result, int* lenth);
#ifdef __cplusplus
    } /*"C" */
#endif
    
#endif /* _QL_DECODER_H */
