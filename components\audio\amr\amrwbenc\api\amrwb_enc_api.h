#pragma once
#ifdef __cplusplus
extern "C" {
#endif
#include <stdint.h>

    typedef uint32_t amrwb_enc_handle;
    typedef struct amrwb_enc_config {
        const char* name;
        const char* out_name;
        int32_t mode;
        int32_t frame_type;
        int32_t allow_dtx;
    }amrwb_enc_config;

    int amrwb_encode_open(const amrwb_enc_config* config, amrwb_enc_handle* handle);
    int amrwb_encode_mode(amrwb_enc_handle handle, int32_t mode);
    int amrwb_encode_do(amrwb_enc_handle handle);
    int amrwb_encode_read(amrwb_enc_handle handle);
    int amrwb_encode_write(amrwb_enc_handle handle);
    int amrwb_encode_set(amrwb_enc_handle handle, const uint16_t* input_data, uint32_t size);
    int amrwb_encode_get(amrwb_enc_handle handle, uint8_t* output_data, uint32_t* size);
    int amrwb_encode_loop(amrwb_enc_handle handle);
    int amrwb_encode_close(amrwb_enc_handle handle);

#ifdef __cplusplus
}
#endif