

#include "helios_audio.h"
#include "helios_include.h"



ql_audio_errcode_e ql_aud_play_stream_start_py(AudStreamFormat_e format, const void *buf, unsigned size, AudPlayType_e type, cb_on_player play_cb)
{
	int err = QL_AUDIO_SUCCESS, cnt=0;
	audio_general_mg_t *mg = &audio_mg;

/*	check player condition  */
	err = (buf == NULL || type <= 0 || type > 3 || size <= 0 || format <= 0 || format >= QL_AUDIO_FORMAT_MAX);
	audio_no_err(err, return QL_AUDIO_INVALID_PARAM, "invalid param");

	err = ((IsAudioInCallMode()&&type != QL_AUDIO_PLAY_TYPE_VOICE && !mg->ringtone.is_ringtone_start) || (!IsAudioInCallMode()&&type == QL_AUDIO_PLAY_TYPE_VOICE));
	audio_no_err(err, return QL_AUDIO_OPER_NOT_SUPPORTED, "operation is not support");
	/*TS transport*/
	glo_format = format;
	if(format == QL_AUDIO_FORMAT_TS){
		if(ts_task_init == 0){
			ts_finished = 0;
			if(ql_ts_decoder_task == NULL){
				if(decoder_fifo_ts == NULL)
					decoder_fifo_ts = osiPipeCreate(10*1024);
				cnt = osiPipeWriteAll(decoder_fifo_ts, buf, size, QL_WAIT_FOREVER);
				start_NeaacDecoder();
				err = ql_rtos_task_create(&ql_ts_decoder_task, 1024*100, 60, "ts_task", ts_decode_task_thread, NULL, 1);
				if(err)				//create task for decoder
				{
					QL_AUDIO_HD_LOG("TS:Decoder task create failed err = %d",err);
					return -1;
				}
				QL_AUDIO_HD_LOG("TS:New Task create success");
			}
			else{
				start_NeaacDecoder();
				osiPipeReset(decoder_fifo_ts);
				cnt = osiPipeWriteAll(decoder_fifo_ts, buf, size, QL_WAIT_FOREVER);
			}
			ts_task_init = 1;
			QL_AUDIO_HD_LOG("TS:First buff inited");
			goto normal_exit;
		}
		else{
			cnt = osiPipeWriteAll(decoder_fifo_ts, buf, size, QL_WAIT_FOREVER);		//write first buffer to pipe
			ql_rtos_task_sleep_ms(10);
			QL_AUDIO_HD_LOG("TS:After write pipe, cnt = %d needed size:%d",cnt, size);		//finished
			goto normal_exit;
		}
	}


	/*AAC M4A transport*/
	if(format == QL_AUDIO_FORMAT_AAC || format == QL_AUDIO_FORMAT_M4A){
		//store buffer head into ram find the head of one frame
		if(m4a_task_init == 0){		//init decoder  init every time while new stream play
									//deinit while play finished

			if(format == QL_AUDIO_FORMAT_AAC){
				return -1;
				if(decoder_fifo_m4a == NULL)
					decoder_fifo_m4a = osiPipeCreate(10*1024);
				cnt = osiPipeWriteAll(decoder_fifo_m4a, buf, size, QL_WAIT_FOREVER);			//AAC��������Ҫ����FAAD_MIN_STREAMSIZE*MAX_CHANNELS����??
				QL_AUDIO_HD_LOG("AAC/M4A:After write pipe, cnt = %d needed size??%d",cnt, FAAD_MIN_STREAMSIZE*MAX_CHANNELS);
				err = ql_rtos_task_create(&ql_m4a_decoder_task, 1024*100, 60, 
				"aac_task", aac_decode_task_thread, NULL, 1);
				if(err)				//create task for decoder
				{
					QL_AUDIO_HD_LOG("AAC:Decoder task create failed err = %d",err);
					return -1;
				}
				QL_AUDIO_HD_LOG("AAC:New Task create success");
				glo_format = format;
				aac_task_init = 1;
			}
			else{	//M4A
				QL_AUDIO_HD_LOG("M4A:Creat Pipe now");
				if(decoder_fifo_m4a == NULL)
					decoder_fifo_m4a = osiPipeCreate(10*1024);
				osiPipeReset(decoder_fifo_m4a);
				cnt = osiPipeWriteAll(decoder_fifo_m4a, buf, size, QL_WAIT_FOREVER);			//AAC��������Ҫ����FAAD_MIN_STREAMSIZE*MAX_CHANNELS����??
				QL_AUDIO_HD_LOG("AAC/M4A:After write pipe, cnt = %d needed size??%d",cnt, 10*1024);
				start_NeaacDecoder();
				while (m4a_task_init != 0)
				{
					QL_AUDIO_HD_LOG("M4A:Wating for task reset");
					ql_rtos_task_sleep_ms(100);
				}

				QL_AUDIO_HD_LOG("M4A:New Task create now");
				err = ql_rtos_task_create(&ql_m4a_decoder_task, 1024*128, 25, 
				"m4a_task", m4a_decode_task_thread, NULL, 1);
				if(err)				//create task for decoder
				{
					QL_AUDIO_HD_LOG("M4A:Decoder task create failed err = %d",err);
					return -1;
				}
				QL_AUDIO_HD_LOG("M4A:New Task create success");
				

				glo_format = format;
				m4a_task_init = 1;
				player_flag = 1;
			}
			
			goto normal_exit;
		}
		else{
			cnt = osiPipeWriteAll(decoder_fifo_m4a, buf, size, QL_WAIT_FOREVER);		//write first buffer to pipe
			QL_AUDIO_HD_LOG("AAC:After write pipe, cnt = %d needed size??%d",cnt, FAAD_MIN_STREAMSIZE*MAX_CHANNELS);
			glo_format = format;
			goto normal_exit;
		}
	}

	if(format == QL_AUDIO_FORMAT_FLV || format == QL_AUDIO_FORMAT_RTMP){
		//1 ����flvͷ��  2 ��tag�л�ȡ��?? �Ƿ�������??? 3 ����Ƶ֡
		if(flv_task_init == 0){	
			if(decoder_fifo_flv == NULL)
				decoder_fifo_flv = osiPipeCreate(FLV_DECODER_PIPE_SIZE);
			osiPipeReset(decoder_fifo_flv);
			cnt = osiPipeWriteAll(decoder_fifo_flv, buf, size, QL_WAIT_FOREVER);
			QL_AUDIO_HD_LOG("FLV:After write pipe, cnt = %d needed size:%d",cnt, 10*1024);
			start_NeaacDecoder();
			if(ql_flv_decoder_task == NULL){
				err = ql_rtos_task_create(&ql_flv_decoder_task, 1024*128, 25, 
				"flv_task", flv_decode_task_thread, NULL, 1);
				if(err)				//create task for decoder
				{
					QL_AUDIO_HD_LOG("FLV:Decoder task create failed err = %d",err);
					return -1;
				}
				QL_AUDIO_HD_LOG("FLV:New Task create success");
			}
			glo_format = format;
			flv_task_init = 1;
			player_flag = 1;
			flv_finished = 0;
			goto normal_exit;
		}
		else{
			cnt = osiPipeWriteAll(decoder_fifo_flv, buf, size, QL_WAIT_FOREVER);		//write first buffer to pipe
			QL_AUDIO_HD_LOG("FLV:After write pipe, cnt = %d needed size:%d",cnt, FAAD_MIN_STREAMSIZE*MAX_CHANNELS);
			glo_format = format;
			goto normal_exit;
		}

	}
	stop_NeaacDecoder();
	
	AudPlayContext_t *player = audio_mg.gAudioPlayCtx;
	if(player->player == NULL)
	{
		ql_player_lock();
		samplerate = 0;
		player->type = (AudPlayType_e)type;		
		player->player = auPlayerCreate();
		audio_no_err((player->player == NULL), goto exit, "player created failed");
		player->pipe = osiPipeCreate(1024*16);
		player->direct = PLAY_STREAM;
		player->aud_cb = play_cb;
		audio_no_err(!player->pipe, goto exit, "pipe created failed");
		glo_format = format;
		player->is_waiting = 1;

		if(format == QL_AUDIO_FORMAT_AMRNB || format == QL_AUDIO_FORMAT_AMRWB) {
			char *head_buf = (char*)buf;
			if(head_buf[0] != 0x23 || head_buf[1] != 0x21 || head_buf[2] != 0x41 || head_buf[3] != 0x4D || head_buf[4] != 0x52 || head_buf[5] != 0x0A) {
				char data_head[6] = {0x23, 0x21, 0x41, 0x4D, 0x52, 0x0A};
				cnt = osiPipeWriteAll(player->pipe, data_head, 6, QL_WAIT_FOREVER);
			}
		}
		cnt = osiPipeWriteAll(player->pipe, buf, size, QL_WAIT_FOREVER);
		if(cnt != size)
		{
			player->is_waiting = 0;
			goto exit;
		}
		player->is_waiting = 0;

		if(player->aud_cb != NULL)
		{
			player->aud_cb(NULL, 0, HELIOS_AUD_PLAYER_START);
		}

		set_play_event_callback(player, AudLocalOnEventCB, AudCallOnEventCB, AudPlayEventCb);
	
		if(format == QL_AUDIO_FORMAT_MP3 && mp3_info_flag == 0)
		{	
			if(mp3_head_buff) {
				free(mp3_head_buff);
				mp3_head_buff = NULL;
			}
			mp3_head_buff = malloc(size);
			mp3_head_len = size;
			memcpy(mp3_head_buff,buf,size);
		}


		if(format == QL_AUDIO_FORMAT_PCM){
			player->pcm.samplerate = 44100;
			player->pcm.channels = 2;
			player->pcm.opt_type = QL_PCM_BLOCK_FLAG|QL_PCM_WRITE_FLAG;
			player->format = QL_AUDIO_FORMAT_PCM;
			player->pcm.initFinish = 1;
			auFrame_t frame = {.sample_format = AUSAMPLE_FORMAT_S16, .sample_rate = player->pcm.samplerate, .channel_count = player->pcm.channels};
			auDecoderParamSet_t params[2] = {{AU_DEC_PARAM_FORMAT, &frame}, {0}};
			err = auPlayerStartPipeV2(player->player, type, format, params, player->pipe);
		}
		else
		{
			err = auPlayerStartPipeV2(player->player, type, format, NULL, player->pipe);
		}
		
		audio_no_err(!err, goto exit, "play failed");
		
		ql_player_unlock();
	}
	else if(player->direct != PLAY_STREAM)
	{
		return QL_AUDIO_DEVICE_BUSY;
	}
	else
	{	
		player->is_waiting = 1;
		while(pause_flag == 1)
		{
			ql_rtos_task_sleep_ms(100);
		}

		if(format == QL_AUDIO_FORMAT_MP3 && mp3_info_flag == 0)
		{	
			void * temp_buf_mp3 = NULL;
			if (mp3_head_len < 10240)
			{
				QL_AUDIO_HD_LOG("MP3:Mp3 head Length is :%d ,need more buff",mp3_head_len);
				temp_buf_mp3 = malloc(mp3_head_len);
				memcpy(temp_buf_mp3,mp3_head_buff,mp3_head_len);
				free(mp3_head_buff);
				mp3_head_buff = NULL;
				mp3_head_buff = malloc(mp3_head_len + size);
				memcpy(mp3_head_buff,temp_buf_mp3,mp3_head_len);
				free(temp_buf_mp3);
				temp_buf_mp3 = NULL;
				memcpy(mp3_head_buff + mp3_head_len,buf,size);
				mp3_head_len += size;
			}
			else
			{
				//�ڴ˽���buffer
				QL_AUDIO_HD_LOG("MP3:Buff enoughed size is :%d",mp3_head_len);
				resyncAtBuffer(mp3_head_buff,mp3_head_len,0,&mp3_frame_pos,&mp3_head);
				mp3_info_flag = 1;
			}

		}

		cnt = osiPipeWriteAll(player->pipe, buf, size, QL_WAIT_FOREVER);
		if(cnt != size)
		{
			player->is_waiting = 0;
			goto exit;
		}
		player->is_waiting = 0;
	}

normal_exit:
	return QL_AUDIO_SUCCESS;


exit:
	glo_format = QL_AUDIO_FORMAT_UNKNOWN;
	player_deinit();
	if(mp3_head_buff) {
		free(mp3_head_buff);
		mp3_head_buff = NULL;
	}
	ql_player_unlock();
	return QL_AUDIO_UNKNOWN_ERROR;
}

