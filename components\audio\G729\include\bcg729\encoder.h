/*
 * Copyright (c) 2011-2019 Belledonne Communications SARL.
 *
 * This file is part of bcg729.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
#ifndef ENCODER_H
#define ENCODER_H
#include <stdint.h>
typedef struct bcg729EncoderChannelContextStruct_struct bcg729EncoderChannelContextStruct;

#ifdef _WIN32
	#ifdef BCG729_STATIC
		#define BCG729_VISIBILITY
	#else
		#ifdef BCG729_EXPORTS
			#define BCG729_VISIBILITY __declspec(dllexport)
		#else
			#define BCG729_VISIBILITY __declspec(dllimport)
		#endif
	#endif
#else
	#define BCG729_VISIBILITY __attribute__ ((visibility ("default")))
#endif

/*****************************************************************************/
/* initBcg729EncoderChannel : create context structure and initialise it     */
/*    parameters:                                                            */
/*      -(i) enanbleVAD : flag set to 1: VAD/DTX is enabled                  */
/*    return value :                                                         */
/*      - the encoder channel context data                                   */
/*                                                                           */
/*****************************************************************************/
BCG729_VISIBILITY bcg729EncoderChannelContextStruct *initBcg729EncoderChannel(uint8_t enableVAD);

/*****************************************************************************/
/* closeBcg729EncoderChannel : free memory of context structure              */
/*    parameters:                                                            */
/*      -(i) encoderChannelContext : the channel context data                */
/*                                                                           */
/*****************************************************************************/
BCG729_VISIBILITY void closeBcg729EncoderChannel(bcg729EncoderChannelContextStruct *encoderChannelContext);

/*****************************************************************************/
/* bcg729Encoder :                                                           */
/*    parameters:                                                            */
/*      -(i) encoderChannelContext : context for this encoder channel        */
/*      -(i) inputFrame : 80 samples (16 bits PCM)                           */
/*      -(o) bitStream : The 15 parameters for a frame on 80 bits            */
/*           on 80 bits (5 16bits words) for voice frame, 4 on 2 byte for    */
/*           noise frame, 0 for untransmitted frames                         */
/*      -(o) bitStreamLength : actual length of output, may be 0, 2 or 10    */
/*           if VAD/DTX is enabled                                           */
/*                                                                           */
/*****************************************************************************/
BCG729_VISIBILITY void bcg729Encoder(bcg729EncoderChannelContextStruct *encoderChannelContext, const int16_t inputFrame[], uint8_t bitStream[], uint8_t *bitStreamLength);

/*****************************************************************************/
/* bcg729GetRFC3389Payload : return the comfort noise payload according to   */
/*                     RFC3389 for the last CN frame generated by encoder    */
/*                                                                           */
/*    parameters:                                                            */
/*      -(i) encoderChannelContext : retrieve the last CN frame encoded      */
/*                    using this context                                     */
/*      -(o) payload : 11 parameters following RFC3389 with filter order 10  */
/*                                                                           */
/*****************************************************************************/
BCG729_VISIBILITY void bcg729GetRFC3389Payload(bcg729EncoderChannelContextStruct *encoderChannelContext, uint8_t payload[]);
#endif /* ifndef ENCODER_H */
