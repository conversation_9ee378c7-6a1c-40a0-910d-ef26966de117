﻿/**  
  @file
  ql_api_qr_code.h

  @brief
  This file provides the definitions for qr code API functions.

*/
/*=================================================================

						EDIT HISTORY FOR MODULE

This section contains comments describing changes made to the module.
Notice that changes are listed in reverse chronological order.

WHEN			  WHO		  WHAT, WHERE, WHY
------------	 -------	 -------------------------------------------------------------------------------

=================================================================*/


#ifndef QL_API_DECODER_H
#define QL_API_DECODER_H

#ifdef __cplusplus
extern "C" {
#endif

/*===========================================================================
 * Macro Definition
 ===========================================================================*/
#define QL_DECODER_ERRCODE_BASE (QL_COMPONENT_BSP_DECODER<<16)
    
#define DECODER_INITIAL_FAILED  0
#define DECODER_INITIAL  		1
    
#define DECODER_ERROR			0
#define DECODER_SUCCESS			1
#define DECODER_ERROR_NONE		1

/*===========================================================================
 * Enum
===========================================================================*/
typedef enum
{
    QL_DECODER_SUCCESS ,

    QL_DECODER_INIT_ERR,
    QL_DECODER_ERR,
    QL_DECODER_GET_RESULT_ERR,
    QL_DECODER_GET_RESULT_LENGTH_ERR,
    QL_DECODER_DESTROY_ERR,
}ql_errcode_decoder_e;

typedef enum
{
    QL_DECODER_TYPE_CODE39 = 0,
    QL_DECODER_TYPE_CODE128,
    QL_DECODER_TYPE_QR_CODE,

    QL_DECODER_TYPE_NONE = 0xff,
}ql_decoder_type_e;

/*===========================================================================
 * Variate
 ===========================================================================*/

/*===========================================================================
 * Functions
 ===========================================================================*/

/*****************************************************************
* Function: ql_qr_decoder_init
*
* Description: Initialize the decoder
* 
* Parameters:
*
* Return:
* 	ql_errcode_decoder_e 
*
*****************************************************************/
ql_errcode_decoder_e ql_qr_decoder_init (void);

/*****************************************************************
* Function: ql_qr_get_decoder_result
*
* Description: get the decode result
* 
* Parameters:
*	*type 		[out]	the type of the code
*   *result     [out]   the result of the code
* Return:
* 	ql_errcode_decoder_e 
*
*****************************************************************/
ql_errcode_decoder_e ql_qr_get_decoder_result(ql_decoder_type_e* type, unsigned char* result, int* lenth);

/*****************************************************************
* Function: ql_qr_image_decoder
*
* Description: decode the image
* 
* Parameters:
*   *img_buffer     [in]    the image need to be decoded
*   whidth          [in]    the width of the image
*   height          [in]    the height of the image
* Return:
* 	ql_errcode_decoder_e 
*
*****************************************************************/
ql_errcode_decoder_e ql_qr_image_decoder (uint16_t *img_buffer, uint32_t width, uint32_t height);

/*****************************************************************
* Function: ql_destroy_decoder
*
* Description: destroy the deocder
* 
* Parameters:
*
* Return:
* 	ql_errcode_decoder_e 
*
*****************************************************************/
ql_errcode_decoder_e ql_destroy_decoder(void);

/*****************************************************************
* Function: ql_get_decoder_version
*
* Description: get version of the deocder
* 
* Parameters:
*   *version        [out]   the data of the version
*
* Return:
* 	ql_errcode_decoder_e 
*
*****************************************************************/
ql_errcode_decoder_e ql_get_decoder_version(unsigned char* version);



ql_errcode_decoder_e ql_qr_decoder_init_qing(void);
ql_errcode_decoder_e ql_qr_image_decoder_qing(uint16_t *img_buffer, uint32_t width, uint32_t height);
ql_errcode_decoder_e ql_qr_get_decoder_result_qing(ql_decoder_type_e* type, unsigned char* result);
ql_errcode_decoder_e ql_destroy_decoder_qing(void);
ql_errcode_decoder_e ql_get_decoder_version_qing(unsigned char* version);
ql_errcode_decoder_e ql_qr_image_decoder_fs (uint8_t *img_buffer, uint32_t width, uint32_t height);
ql_errcode_decoder_e ql_qr_image_decoder_fs_qing(uint8_t *img_buffer, uint32_t width, uint32_t height);

int ql_decoding_image(unsigned char* raw, int width, int height);
#ifdef __cplusplus
    } /*"C" */
#endif

#endif /* QL_API_DECODER_H*/

