/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2020 ASR Ltd. All Rights Reserved
-------------------------------------------------------------------------------------------------------------------*/

extern "C" {
#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

//#include "diags.h"
//#include "gbl_types.h"
//#include "acm_comm.h"
#include "amr_vocoder_api.h"
#include "amr_vocoder_config.h"
#include "amrnb_enc_api.h"
#include "amrwb_enc_api.h"
#include "amrnb_dec_api.h"
#include "amrwb_dec_api.h"
//#include "acm_audio_track.h"
//#include "acm_audio_record.h"
//#include "acm_audio_effect.h"
//#include "audio_def.h"
//#include "acm_audio_def.h"
//#include "msl_sal_stbc_type.h"
//#include "msl_mem.h"
//#include "audio_file.h"
//#include "quec_cust_feature.h"

#ifndef AMR_DEC_USER_MAXIMUM
#if FEATURE_AUDIO_TRACK == 1
#define AMR_DEC_USER_MAXIMUM        (3)
#else
#define AMR_DEC_USER_MAXIMUM        (1)
#endif
#endif

#ifndef AMR_ENC_USER_MAXIMUM
#define AMR_ENC_USER_MAXIMUM        (1)
#endif

#ifndef AMR_FILE_NAME_MAXIMUM
#define AMR_FILE_NAME_MAXIMUM       (100)
#endif

#ifndef AMR_DEC_STREAM_SIZE
#define AMR_DEC_STREAM_SIZE         (2 * 1024)
#endif

#define NB_PCM_FRAME_SIZE           (320)
#define WB_PCM_FRAME_SIZE           (640)

#define AMR_FRAME_SIZE_MAXIMUM      (61)
#define AMR_FILE_HEADER_SIZE_NB     (6)
#define AMR_FILE_HEADER_SIZE_WB     (9)

#define AMR_DEC_CONTINUE    (0x01)
#define AMR_DEC_STOP        (0x02)
#define AMR_DEC_START       (0x04)
#define AMR_ENC_CONTINUE    (0x100)
#define AMR_ENC_STOP        (0x200)
#define AMR_ENC_START       (0x400)
#define AMR_VOCODER_TASK_MASK   (AMR_DEC_START | AMR_DEC_STOP | AMR_DEC_CONTINUE | AMR_ENC_START | AMR_ENC_STOP | AMR_ENC_CONTINUE)

#define AMR_VOCODER_REVISION        "2.0.7"

    typedef struct AmrStreamDataInfo {
        /** amr playback handle held by user, used to send following amr data to corresponding mixer instance*/
        AmrPlaybackHandle handle;
        /** user space amr data ptr*/
        uint8_t* buf;
        /** user space amr data size*/
        uint32_t size;
    }AmrStreamDataInfo;

    typedef struct AMR_DEC_USER_INFO {
        union {
            amrnb_dec_handle handle_nb;
            amrwb_dec_handle handle_wb;
            void* handle;
        };
        acm_audio_track_handle handle_pcm, handle_closed;
        AmrPlaybackEventCallback event_cb;
        int close_flag;
        int option;
        int amr_mode;
        int amr_rate;
        int stream_mode;
#if AMR_DEC_STREAM_SUPPORT == 1
        int file_header_parsed;
        uint32_t frame_offset;
        AmrStreamDataInfo* stream;
        uint8_t current_frame[AMR_FRAME_SIZE_MAXIMUM];
        OSAMsgQRef stream_data;
#endif
        char file_name[AMR_FILE_NAME_MAXIMUM];
        short* pcm_buf;
        union {
            AmrPlaybackHandle unique_id;
            char* magic;
        };
    }AMR_DEC_USER_INFO;

    typedef struct AMR_ENC_USER_INFO {
        union {
            amrnb_enc_handle handle;
            amrwb_enc_handle handle_wb;
        };
        acm_audio_record_handle handle_pcm;
        AmrEncConfigInfo config;
        int close_flag;
        char file_name[AMR_FILE_NAME_MAXIMUM];
        union {
            AmrEncodeHandle unique_id;
            char* magic;
        };
    }AMR_ENC_USER_INFO;

#if FEATURE_AUDIO_AMR_DEC == 1  
    static void* amrdec_stack_ptr = 0;
    static OSATaskRef amrdec_task_ref = 0;
    static OSAFlagRef amrdec_flag_ref = 0;
    static OSASemaRef amrdec_sema_ref = 0;
    static AMR_DEC_USER_INFO amr_dec_info[AMR_DEC_USER_MAXIMUM] = { 0 };
#if FEATURE_AUDIO_TRACK == 1
    static void amr_mixer_event_callback(acm_audio_track_handle handle, acm_audio_track_event_t event) {
        if (handle > 0) {
            int i = 0;
            if (event == AUDIO_TRACK_EVENT_FASTUP) {
                for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
                    if (handle == amr_dec_info[i].handle_pcm) {
                        OSAFlagSet(amrdec_flag_ref, AMR_DEC_CONTINUE, OSA_FLAG_OR);
                        break;
                    }
                }
            }
            else if (event == AUDIO_TRACK_EVENT_CLOSED) {
                for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
                    if (handle == amr_dec_info[i].handle_closed) {
                        amr_dec_info[i].close_flag = 1;
                        OSAFlagSet(amrdec_flag_ref, AMR_DEC_STOP, OSA_FLAG_OR);
                        break;
                    }
                }
            }
        }
    }
#else    
    static void dlstream(DOWNLINKSTREAM_REQUEST* request) {
        if (amr_dec_info[0].amr_mode == 1 || amr_dec_info[0].amr_mode == 2) {
            request->handle_num = 1;
            request->streamInd[0].data = (UINT16*)amr_dec_info[0].pcm_buf;
            request->streamInd[0].dataSize = (amr_dec_info[0].amr_mode == 1 ? NB_PCM_FRAME_SIZE : WB_PCM_FRAME_SIZE);
        }
        OSAFlagSet(amrdec_flag_ref, AMR_DEC_CONTINUE, OSA_FLAG_OR);
    }
#endif

    static void amr_dec_start(void) {
        int i = 0;
        for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
#if FEATURE_AUDIO_TRACK == 0
            AUDIO_PLAY_OPTION play_option;
            play_option.option = amr_dec_info[i].option;
#endif
            if (amr_dec_info[i].handle == 0 && (amr_dec_info[i].stream_mode || strlen(amr_dec_info[i].file_name) > 0)) {
#if FEATURE_AUDIO_NBAMR_DEC == 1 
                amrnb_dec_config config = { 0 };
                config.name = amr_dec_info[i].file_name;
                if (amrnb_decode_open(&config, &amr_dec_info[i].handle_nb) == 0) {
#if FEATURE_AUDIO_TRACK == 1
                    if (amr_dec_info[i].handle_pcm == 0) {
                        acm_audio_track_config_t config = { 0 };
                        config.channels = 1;
                        config.rate = 8000;
                        config.option = amr_dec_info[i].option;
                        config.event_cb = amr_mixer_event_callback;
                        if (acm_audio_track_open(&config, &amr_dec_info[i].handle_pcm) == 0) {
                            amr_dec_info[i].amr_mode = 1;
                            amr_dec_info[i].amr_rate = -1;
                            if (amr_dec_info[i].pcm_buf == 0)
                                amr_dec_info[i].pcm_buf = (short*)malloc(NB_PCM_FRAME_SIZE);
                            if (amr_dec_info[i].event_cb)
                                amr_dec_info[i].event_cb(AMR_PLAYBACK_EVENT_STATUS, AMR_PLAYBACK_STATUS_STARTED);
                        }
                        else {
                            amrnb_decode_close(amr_dec_info[i].handle_nb);
                        }
                    }
#else
                    amr_dec_info[i].amr_mode = 1;
                    amr_dec_info[i].amr_rate = -1;
                    amr_dec_info[i].pcm_buf = (short*)malloc(NB_PCM_FRAME_SIZE);
                    ATCPCMPlayCtrl(0x3,
                        (play_option.dest_end == 2 ? ACM_BOTH_ENDS : (play_option.dest_end == 1 ? ACM_FAR_END : ACM_NEAR_END)),
                        (play_option.active_point ? ACM_NEAR_VOCODER : ACM_NEAR_CODEC),
                        (play_option.override ? ACM_NOT_COMB_WITH_CALL : ACM_COMB_WITH_CALL),
                        (UINT32)dlstream,
                        0);
#endif
                }
#endif

#if FEATURE_AUDIO_WBAMR_DEC == 1 
                amrwb_dec_config config_wb = { 0 };
                config_wb.name = amr_dec_info[i].file_name;
                if (!amr_dec_info[i].handle && amrwb_decode_open(&config_wb, &amr_dec_info[i].handle_wb) == 0) {
#if FEATURE_AUDIO_TRACK == 1
                    if (amr_dec_info[i].handle_pcm == 0) {
                        acm_audio_track_config_t config = { 0 };
                        config.channels = 1;
                        config.rate = 16000;
                        config.option = amr_dec_info[i].option;
                        config.event_cb = amr_mixer_event_callback;
                        if (acm_audio_track_open(&config, &amr_dec_info[i].handle_pcm) == 0) {
                            amr_dec_info[i].amr_mode = 2;
                            amr_dec_info[i].amr_rate = -1;
                            if (amr_dec_info[i].pcm_buf == 0)
                                amr_dec_info[i].pcm_buf = (short*)malloc(WB_PCM_FRAME_SIZE);
                            if (amr_dec_info[i].event_cb)
                                amr_dec_info[i].event_cb(AMR_PLAYBACK_EVENT_STATUS, AMR_FILE_STATUS_STARTED);
                        }
                        else {
                            amrwb_decode_close(amr_dec_info[i].handle_wb);
                            amr_dec_info[i].amr_mode = 0;
                        }
                    }
#else
                    amr_dec_info[i].amr_mode = 2;
                    amr_dec_info[i].amr_rate = -1;
                    amr_dec_info[i].pcm_buf = (short*)malloc(WB_PCM_FRAME_SIZE);
                    ATCPCMPlayCtrl(0x7,
                        (play_option.dest_end == 2 ? ACM_BOTH_ENDS : (play_option.dest_end == 1 ? ACM_FAR_END : ACM_NEAR_END)),
                        (play_option.active_point ? ACM_NEAR_VOCODER : ACM_NEAR_CODEC),
                        (play_option.override ? ACM_NOT_COMB_WITH_CALL : ACM_COMB_WITH_CALL),
                        (UINT32)dlstream,
                        0);
#endif
                }
#endif
            }

            if (amr_dec_info[i].amr_mode == 0) {
                memset(&amr_dec_info[i], 0, sizeof(AMR_DEC_USER_INFO));
            }
        }
    }

    static int amr_dec_continue(void) {
        int i = 0, ret_code = 0;
        for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
            int error_code = 0;
            uint32_t sz = 0;
#if FEATURE_AUDIO_NBAMR_DEC == 1
            if (amr_dec_info[i].amr_mode == 1) {
                if (!amr_dec_info[i].stream_mode) {
                    error_code = amrnb_decode_read(amr_dec_info[i].handle_nb);
                    if (error_code == 0) {
                        error_code = amrnb_decode_do(amr_dec_info[i].handle_nb);
                        if (error_code == 0) {
                            sz = NB_PCM_FRAME_SIZE;
                            error_code = amrnb_decode_get(amr_dec_info[i].handle_nb, (int16_t*)amr_dec_info[i].pcm_buf, &sz);
                            if (error_code == 0) {
                                DIAG_FILTER(AUDIO, AMR_DEC, amr_dec_continue, DIAG_INFORMATION)
                                diagStructPrintf("dec_amrnb_dump", amr_dec_info[i].pcm_buf, sz);
                            }
                        }
                    }
                }
#if AMR_DEC_STREAM_SUPPORT == 1
                else {
                    int frame_decoded = 0;
                    while (!frame_decoded) {
                        AmrStreamDataInfo* stream = amr_dec_info[i].stream;
                        if (!stream) {
                            MslMessage mslStbcRecvMsg = { 0 };
                            uint32_t count;
                            OSA_STATUS osaStatus;
                            OSAMsgQPoll(amr_dec_info[i].stream_data, &count);
                            if (count == 0) {
                                if (amr_dec_info[i].event_cb)
                                    amr_dec_info[i].event_cb(AMR_PLAYBACK_EVENT_STATUS, AMR_STREAM_STATUS_FAST_UP);
                                if (amr_dec_info[i].close_flag == 2) {
                                    amr_dec_info[i].close_flag = 1;
                                    OSAFlagSet(amrdec_flag_ref, AMR_DEC_STOP, OSA_FLAG_OR);
                                }
                                break;
                            }
                            osaStatus = OSAMsgQRecv(amr_dec_info[i].stream_data, (UINT8*)&mslStbcRecvMsg, sizeof(MslMessage), OSA_NO_SUSPEND);
                            if (osaStatus == OS_SUCCESS) {
                                stream = (AmrStreamDataInfo*)mslStbcRecvMsg.pparm;
                            }
                        }

                        if (stream && stream->buf) {
                            uint32_t block_size = stream->size;
                            while (block_size > 0 && !frame_decoded) {
                                uint32_t copied_size = AMR_FRAME_SIZE_MAXIMUM - amr_dec_info[i].frame_offset;
                                if (copied_size > block_size)
                                    copied_size = block_size;
                                memcpy(&amr_dec_info->current_frame[amr_dec_info[i].frame_offset], &stream->buf[stream->size - block_size], copied_size);
                                amr_dec_info[i].frame_offset += copied_size;
                                block_size -= copied_size;

                                if (!amr_dec_info[i].file_header_parsed) {
                                    if (amr_dec_info[i].frame_offset >= AMR_FILE_HEADER_SIZE_NB) {
                                        if (memcmp(amr_dec_info[i].current_frame, "#!AMR\n", AMR_FILE_HEADER_SIZE_NB) == 0) {
                                            memmove(&amr_dec_info[i].current_frame[0], &amr_dec_info[i].current_frame[AMR_FILE_HEADER_SIZE_NB], amr_dec_info[i].frame_offset - AMR_FILE_HEADER_SIZE_NB);
                                            amr_dec_info[i].frame_offset -= AMR_FILE_HEADER_SIZE_NB;
                                        }
                                        amr_dec_info[i].file_header_parsed = 1;
                                    }
                                }
                                else {
                                    while (amr_dec_info[i].frame_offset > 0) {
                                        uint32_t frame_size = 0;
                                        int error_code = amrnb_decode_set(amr_dec_info[i].handle_nb, amr_dec_info[i].current_frame, amr_dec_info[i].frame_offset, &frame_size);
                                        if (error_code == 0) {
                                            memmove(&amr_dec_info[i].current_frame[0], &amr_dec_info[i].current_frame[frame_size], amr_dec_info[i].frame_offset - frame_size);
                                            amr_dec_info[i].frame_offset -= frame_size;
                                            error_code = amrnb_decode_do(amr_dec_info[i].handle_nb);
                                            if (error_code == 0) {
                                                sz = NB_PCM_FRAME_SIZE;
                                                error_code = amrnb_decode_get(amr_dec_info[i].handle_nb, (int16_t*)amr_dec_info[i].pcm_buf, &sz);
                                                if (error_code == 0) {
                                                    DIAG_FILTER(AUDIO, AMR_DEC, amr_dec_continue, DIAG_INFORMATION)
                                                    diagStructPrintf("dec_amrnb_dump", amr_dec_info[i].pcm_buf, sz);
#if FEATURE_AUDIO_TRACK == 1
                                                    error_code = acm_audio_track_write(amr_dec_info[i].handle_pcm, (const uint8_t*)amr_dec_info[i].pcm_buf, sz);
                                                    if (error_code != 0) {
                                                        DIAG_FILTER(AUDIO, AMR_DEC, amr_dec_continue_write_fail, DIAG_INFORMATION)
                                                        diagPrintf("error_code:%d", error_code);
                                                    }
#endif
                                                    frame_decoded = 1;
                                                    break;
                                                }
                                            }
                                        }
                                        else {
                                            break;
                                        }
                                    }
                                }
                            }
                            if (block_size == 0) {
                                free(stream->buf);
                                free(stream);
                                amr_dec_info[i].stream = 0;
                            }
                            else {
                                ASSERT(stream->size > block_size);
                                memmove(&stream->buf[0], &stream->buf[stream->size - block_size], block_size);
                                stream->size = block_size;
                                amr_dec_info[i].stream = stream;
                            }
                        }
                    }
                }
#endif
            }
#endif

#if FEATURE_AUDIO_WBAMR_DEC == 1
            if (amr_dec_info[i].amr_mode == 2) {
                if (!amr_dec_info[i].stream_mode)
                    error_code = amrwb_decode_read(amr_dec_info[i].handle_wb);
                if (error_code == 0) {
                    error_code = amrwb_decode_do(amr_dec_info[i].handle_wb);
                    if (error_code == 0) {
                        sz = WB_PCM_FRAME_SIZE;
                        error_code = amrwb_decode_get(amr_dec_info[i].handle_wb, (int16_t*)amr_dec_info[i].pcm_buf, &sz);
                        if (error_code == 0) {
                            DIAG_FILTER(AUDIO, AMR_DEC, amr_dec_continue, DIAG_INFORMATION)
                            diagStructPrintf("dec_amrwb_dump", amr_dec_info[i].pcm_buf, sz);
                        }
                    }
                }
            }
#endif
            if (strlen(amr_dec_info[i].file_name) > 0) {
                if (error_code != 0) {
                    amr_dec_info[i].close_flag = 1;
                    ret_code = -1;
                }
                else {
#if FEATURE_AUDIO_TRACK == 1
                    acm_audio_track_write(amr_dec_info[i].handle_pcm, (const uint8_t*)amr_dec_info[i].pcm_buf, sz);
#endif
                }
            }
        }

        return ret_code;
    }

#if AMR_DEC_STREAM_SUPPORT == 1
    static void cleanupMsgQ(OSAMsgQRef* pref) {
        OSAMsgQRef ref = 0;
        if (pref)
            ref = *pref;
        DIAG_FILTER(AUDIO, AMR_DEC, cleanupMsgQ, DIAG_INFORMATION)
        diagPrintf("msgQ ref:0x%lx", ref);
        if (ref) {
            while (1) {
                MslMessage mslStbcRecvMsg = { 0 };
                uint32_t count;
                OSA_STATUS osaStatus;
                OSAMsgQPoll(ref, &count);
                if (count == 0)
                    break;

                osaStatus = OSAMsgQRecv(ref, (UINT8*)&mslStbcRecvMsg, sizeof(MslMessage), OSA_NO_SUSPEND);
                if (osaStatus == OS_SUCCESS) {
                    AmrStreamDataInfo* stream = (AmrStreamDataInfo*)mslStbcRecvMsg.pparm;
                    if (stream) {
                        if (stream->buf)
                            free(stream->buf);
                        free(stream);
                    }
                }
            }

            OSAMsgQDelete(ref);
            *pref = 0;
        }
    }
#endif

    static void amr_dec_stop(void) {
        int i = 0;
        for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
            if (amr_dec_info[i].close_flag == 1) {
#if FEATURE_AUDIO_NBAMR_DEC == 1
                if (amr_dec_info[i].amr_mode == 1) {
                    if (amr_dec_info[i].handle_nb) {
                        amrnb_decode_close(amr_dec_info[i].handle_nb);
                        amr_dec_info[i].handle_nb = 0;
                    }
                }
#endif

#if FEATURE_AUDIO_WBAMR_DEC == 1
                if (amr_dec_info[i].amr_mode == 2) {
                    if (amr_dec_info[i].handle_wb) {
                        amrwb_decode_close(amr_dec_info[i].handle_wb);
                        amr_dec_info[i].handle_wb = 0;
                    }
                }
#endif
                if (amr_dec_info[i].option & AUDIO_PLAY_MASK_CYCLIC) {
                    amr_dec_info[i].close_flag = 0;
                    if (amr_dec_info[i].event_cb)
                        amr_dec_info[i].event_cb(AMR_PLAYBACK_EVENT_FILENAME, (int)amr_dec_info[i].file_name);
                    OSAFlagSet(amrdec_flag_ref, AMR_DEC_START, OSA_FLAG_OR);
                }
                else {
                    AmrPlaybackEventCallback cb = amr_dec_info[i].event_cb;
                    acm_audio_track_handle track_handle = amr_dec_info[i].handle_pcm;
#if FEATURE_AUDIO_TRACK == 1
                    if (amr_dec_info[i].handle_pcm) {
                        bool drain = (amr_dec_info[i].option & AUDIO_PLAY_MASK_DRAIN) ? true : false;
                        acm_audio_track_close(amr_dec_info[i].handle_pcm, drain);
                    }
#else
                    ATCPCMPlayCtrl(0, 0, 0, 0, (UINT32)dlstream, 0);
#endif
                    if (amr_dec_info[i].pcm_buf) {
                        free(amr_dec_info[i].pcm_buf);
                    }
                    if (amr_dec_info[i].magic) {
                        free(amr_dec_info[i].magic);
                    }
#if AMR_DEC_STREAM_SUPPORT == 1
                    if (amr_dec_info[i].stream_data) {
                        cleanupMsgQ(&amr_dec_info[i].stream_data);
                    }
                    if (amr_dec_info[i].stream) {
                        if (amr_dec_info[i].stream->buf)
                            free(amr_dec_info[i].stream->buf);
                        free(amr_dec_info[i].stream);
                    }
#endif
                    memset(&amr_dec_info[i], 0, sizeof(AMR_DEC_USER_INFO));
                    amr_dec_info[i].handle_closed = track_handle;
                    amr_dec_info[i].event_cb = cb;
                    if (track_handle == 0) {
                        if (amr_dec_info[i].event_cb)
                            amr_dec_info[i].event_cb(AMR_PLAYBACK_EVENT_STATUS, AMR_FILE_STATUS_ENDED);
                    }
                }
            }
        }
    }

    static void amrPlay(void*) {
        // Decode loop
        while (1) {
            UINT32 event = 0;
            OSAFlagWait(amrdec_flag_ref, AMR_VOCODER_TASK_MASK, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND);
            DIAG_FILTER(AUDIO, AMR_DEC, amr_dec_event, DIAG_INFORMATION)
            diagPrintf("amr_dec_event:%x", event);

            if (AMR_DEC_CONTINUE & event) {
                int loop = AMR_DEC_FRAMES_PER_REQUEST;
                while (loop--) {
                    if (amr_dec_continue() != 0) {
                        amr_dec_stop();
                        break;
                    }
                }
            }

            if (AMR_DEC_START & event) {
                amr_dec_start();
            }

            if (AMR_DEC_STOP & event) {
                amr_dec_stop();
            }
        }
    }

    void amrPlayInit(void) {
        if (!amrdec_sema_ref) {
            OS_STATUS status = OSASemaphoreCreate(&amrdec_sema_ref, 1, OSA_FIFO);
            ASSERT(status == OS_SUCCESS);
        }
    }

    static void amrdec_task_init(void) {
        static int inited = 0;
        OS_STATUS status;
#if FEATURE_AUDIO_WBAMR_DEC == 1
        size_t amr_stack_size = 1024 * 18;
#else
        size_t amr_stack_size = 1024 * 8;
#endif
        int amr_thread_priority = 75;

        if (!inited) {
            status = OSAFlagCreate(&amrdec_flag_ref);
            ASSERT(status == OS_SUCCESS);

            amrdec_stack_ptr = malloc(amr_stack_size);
            ASSERT(amrdec_stack_ptr);

            status = OSATaskCreate(&amrdec_task_ref, amrdec_stack_ptr, amr_stack_size, amr_thread_priority, "amrPlay", amrPlay, NULL);
            ASSERT(status == OS_SUCCESS);

            amrPlayInit();
            inited = 1;
        }
    }

    static int openHandle(const char* name, const AmrFileConfigInfo* config, AmrPlaybackHandle* handle) {
        int i = 0, error_code = -1;
        OSA_STATUS osa_status;
        osa_status = OSASemaphoreAcquire(amrdec_sema_ref, OSA_SUSPEND);
        ASSERT(osa_status == OS_SUCCESS);
        for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
            if (name && strcmp(amr_dec_info[i].file_name, name) == 0) {
                break;
            }
            if (amr_dec_info[i].handle == 0 && amr_dec_info[i].handle_closed == 0) {
                if (name) {
                    strcpy(amr_dec_info[i].file_name, name);
                }
#if AMR_DEC_STREAM_SUPPORT == 1
                else {
                    OSA_STATUS osaStatus = OSAMsgQCreate(&amr_dec_info[0].stream_data,
#ifdef OSA_QUEUE_NAMES
                        "amrDataQ",
#endif
                        sizeof(MslMessage),
                        300,
                        OSA_PRIORITY);
                    ASSERT(osaStatus == OS_SUCCESS);
                    amr_dec_info[i].stream_mode = 1;
                }
#endif
                amr_dec_info[i].option = config->option | AUDIO_PLAY_MASK_DRAIN;
                amr_dec_info[i].event_cb = config->listener;
                amr_dec_info[i].magic = (char*)malloc(sizeof(char));
                if (handle) {
                    *handle = (AmrPlaybackHandle)amr_dec_info[i].magic;
                }
                error_code = 0;
                break;
            }
        }
        osa_status = OSASemaphoreRelease(amrdec_sema_ref);
        ASSERT(osa_status == OS_SUCCESS);
        return error_code;
    }

    static int closeHandle(uint32_t handle, int drain) {
        int i = 0, error_code = -1;
        OSA_STATUS osa_status;
        osa_status = OSASemaphoreAcquire(amrdec_sema_ref, OSA_SUSPEND);
        ASSERT(osa_status == OS_SUCCESS);
        for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
            if ((handle & amr_dec_info[i].unique_id) == amr_dec_info[i].unique_id && amr_dec_info[i].unique_id > 0) {
                if (amr_dec_info[i].stream_mode && drain)
                    amr_dec_info[i].close_flag = 2;
                else
                    amr_dec_info[i].close_flag = 1;
                if (drain)
                    amr_dec_info[i].option |= AUDIO_PLAY_MASK_DRAIN;
                else
                    amr_dec_info[i].option &= ~AUDIO_PLAY_MASK_DRAIN;
                amr_dec_info[i].option &= ~AUDIO_PLAY_MASK_CYCLIC;
                error_code = 0;
                break;
            }
        }
        osa_status = OSASemaphoreRelease(amrdec_sema_ref);
        ASSERT(osa_status == OS_SUCCESS);
        return error_code;
    }

    int amrPlayStart(const char* file_name, const AmrPlaybackConfigInfo* config, AmrPlaybackHandle* handle) {
        DIAG_FILTER(AUDIO, AMR_DEC, amrPlayStart, DIAG_INFORMATION)
        diagPrintf("file_name:%s, rev:%s", file_name, AMR_VOCODER_REVISION);

        if (file_name && ((0 == strlen(file_name))
            || (strlen(file_name) >= AMR_FILE_NAME_MAXIMUM)
            || (!common_access(file_name)))) {
            DIAG_FILTER(AUDIO, AMR_DEC, wrong_file_name, DIAG_INFORMATION)
            diagPrintf("file_name error!!");
            return -1;
        }

        if (amrdec_task_ref == NULL) {
            amrdec_task_init();
        }

        if (openHandle(file_name, config, handle) != 0)
            return -2;

        OSAFlagSet(amrdec_flag_ref, AMR_DEC_START, OSA_FLAG_OR);
        return 0;
    }

    int amrPlayStop(AmrPlaybackHandle handle, int drain) {
        DIAG_FILTER(AUDIO, AMR_DEC, amrPlayStop, DIAG_INFORMATION)
        diagPrintf("amrPlayStop, handle:0x%lx, drain:%d", handle, drain);
        if (closeHandle(handle, drain) != 0)
            return -1;

        OSAFlagSet(amrdec_flag_ref, AMR_DEC_STOP, OSA_FLAG_OR);
        return 0;
    }

    int amrGetPCMOutHandle(AmrPlaybackHandle handle, uint32_t* track_handle) {
        int i = 0;
        DIAG_FILTER(AUDIO, AMR_DEC, amrGetPCMOutHandle, DIAG_INFORMATION)
        diagPrintf("handle:0x%lx, track_handle:0x%lx", handle, track_handle);
        for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
            if ((handle & amr_dec_info[i].unique_id) == amr_dec_info[i].unique_id) {
                if (track_handle) {
                    *track_handle = amr_dec_info[i].handle_pcm;
                    return 0;
                }
            }
        }

        return -1;
    }

    int amrPlayStopWithName(const char* file_name, int drain) {
        int i = 0;
        DIAG_FILTER(AUDIO, AMR_DEC, amrPlayStopWithName, DIAG_INFORMATION)
        diagPrintf("amrPlayStopWithName:%s,drain:%d", file_name, drain);

        for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
            if (file_name && (strcmp(file_name, amr_dec_info[i].file_name) == 0)) {
                return amrPlayStop(amr_dec_info[i].unique_id, drain);
            }
        }
        return -1;
    }

#ifndef  QUECTEL_PROJECT_CUST
    int amrStart(const char* name) {
        AmrFileConfigInfo config = { 0 };
        config.option = AUDIO_PLAY_MASK_DRAIN;
        return amrPlayStart(name, &config, 0);
    }

    int amrStop(void) {
        int i = 0, err_code = 0;
        for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
            err_code = amrPlayStopWithName(amr_dec_info[i].file_name, 1);
        }
        return err_code;
    }
#endif

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_dec_test1
    int amr_dec_test1(void) {
        int rc = 0;
        AmrFileConfigInfo config = { 0 };
        rc = amrPlayStart("test.amr", &config, 0);
        if (rc) {
            DIAG_FILTER(AUDIO, AMR_DEC, amr_dec_test_fail, DIAG_INFORMATION)
            diagPrintf("rc:%d", rc);
        }
        else {
            DIAG_FILTER(AUDIO, AMR_DEC, amr_dec_test_success, DIAG_INFORMATION)
            diagPrintf("rc:%d", rc);
        }
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_dec_test2
    int amr_dec_test2(int* p) {
        AmrFileConfigInfo config = { 0 };
        if (p)
            config.option = *p;
        amrPlayStart("test.amr", &config, 0);
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_dec_test_farend
    int amr_dec_test_farend(void) {
        int rc = 0;
        AmrFileConfigInfo config = { 0 };
        AUDIO_PLAY_OPTION play_option = { 0 };
        play_option.dest_end = 0x1;
        config.option = play_option.option;
        rc = amrPlayStart("test.amr", &config, 0);
        if (rc) {
            DIAG_FILTER(AUDIO, AMR_DEC, amr_dec_test_farend_fail, DIAG_INFORMATION)
            diagPrintf("rc:%d", rc);
        }
        else {
            DIAG_FILTER(AUDIO, AMR_DEC, amr_dec_test_farend_success, DIAG_INFORMATION)
            diagPrintf("rc:%d", rc);
        }
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_dec_test_stop
    int amr_dec_test_stop(void) {
        amrPlayStopWithName("test.amr", 1);
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_dec_test_stop_all
    int amr_dec_test_stop_all(void) {
        amrStop();
        return 0;
    }

#if AMR_DEC_STREAM_SUPPORT == 1
    int amrPlayBuffer(AmrPlaybackHandle handle, const uint8_t* data, uint32_t size) {
        int i = 0;
        if (!handle || !data || !size)
            return -1;

        DIAG_FILTER(AUDIO, AMR_DEC, amrPlayBuffer_dump, DIAG_INFORMATION)
        diagStructPrintf("amr_stream_buffer", (void*)data, size);

        for (i = 0; i < AMR_DEC_USER_MAXIMUM; i++) {
            if ((handle & amr_dec_info[i].unique_id) == amr_dec_info[i].unique_id) {
                MslMessage mslMessage = { 0 };
                AmrStreamDataInfo* stream = (AmrStreamDataInfo*)malloc(sizeof(AmrStreamDataInfo));
                if (stream) {
                    stream->handle = handle;
                    stream->size = size;
                    stream->buf = (uint8_t*)malloc(size);
                    if (stream->buf) {
                        OSA_STATUS osaStatus;
                        memcpy(stream->buf, data, size);
                        mslMessage.pparm = stream;
                        osaStatus = OSAMsgQSend(amr_dec_info[i].stream_data, sizeof(MslMessage), (UINT8*)&mslMessage, OSA_NO_SUSPEND);
                        ASSERT(osaStatus == OS_SUCCESS);
                        return 0;
                    }
                    else {
                        free(stream);
                    }
                }
                return -2;
            }
        }

        return -3;
    }

    static AUDIO_FILE_ID fd = 0;
    static AmrPlaybackHandle handle = 0;
    static void amr_stream_callback(AmrPlaybackEventType event, int value) {
        if (event == AMR_PLAYBACK_EVENT_STATUS && value == AMR_STREAM_STATUS_FAST_UP && fd) {
            uint8_t* buffer = (uint8_t*)malloc(AMR_DEC_STREAM_SIZE);
            if (buffer) {
                size_t readed = common_fread(buffer, 1, AMR_DEC_STREAM_SIZE, fd);
                if (readed > 0) {
                    int error_code = amrPlayBuffer(handle, buffer, readed);
                    if (error_code != 0) {
                        DIAG_FILTER(AUDIO, AMR_DEC, amrstream_play_fail, DIAG_INFORMATION)
                        diagPrintf("error_code:%d", error_code);
                    }
                }
                else {
                    common_fclose(fd);
                    fd = 0;
                }
                free(buffer);
            }
        }
    }

    int amrstream_filemode_start(const char* name, int option) {
        AmrPlaybackConfigInfo configInfo = { 0 };
        DIAG_FILTER(AUDIO, AMR_DEC, amrstream_filemode_start, DIAG_INFORMATION)
        diagPrintf("name:%s,option:0x%lx,fd:0x%lx", name, option, fd);
        fd = common_fopen(name, "rb");
        if (fd) {
            configInfo.listener = amr_stream_callback;
            configInfo.option = option;
            return amrPlayStart(0, &configInfo, &handle);
        }

        return -1;
    }

    int amrstream_filemode_stop(int drain) {
        DIAG_FILTER(AUDIO, AMR_DEC, amrstream_filemode_stop, DIAG_INFORMATION)
        diagPrintf("drain:%d", drain);
        if (fd) {
            common_fclose(fd);
            fd = 0;
        }
        return amrPlayStop(handle, drain);
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_dec_stream_test
    int amr_dec_stream_test(void) {
        return amrstream_filemode_start("test.amr", 0);
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_dec_stream_test_stop
    int amr_dec_stream_test_stop(void) {
        return amrstream_filemode_stop(1);
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_dec_stream_test_stop_force
    int amr_dec_stream_test_stop_force(void) {
        return amrstream_filemode_stop(0);
    }
#else
    int amrstream_filemode_start(const char* name, int option) {
        return -1;
    }

    int amrstream_filemode_stop(int drain) {
        return -1;
    }
#endif

#endif

#if FEATURE_AUDIO_AMR_ENC == 1 && FEATURE_AUDIO_RECORD == 1
    static void* amrenc_stack_ptr = 0;
    static OSATaskRef amrenc_task_ref = 0;
    static OSAFlagRef amrenc_flag_ref = 0;
    static OSASemaRef amrenc_sema_ref = 0;
    static AMR_ENC_USER_INFO amr_enc_info[AMR_ENC_USER_MAXIMUM] = { 0 };

    static void amr_record_event_callback(acm_audio_record_handle handle, acm_audio_record_event_t event) {
        if (handle > 0 && event == AUDIO_RECORD_EVENT_TICK) {
            int i = 0;
            for (i = 0; i < AMR_ENC_USER_MAXIMUM; i++) {
                if (handle == amr_enc_info[i].handle_pcm) {
                    OSAFlagSet(amrenc_flag_ref, AMR_ENC_CONTINUE, OSA_FLAG_OR);
                    break;
                }
            }
        }
    }

    static void amr_enc_start(void) {
        int i = 0;
        for (i = 0; i < AMR_ENC_USER_MAXIMUM; i++) {
            if (amr_enc_info[i].handle == 0) {
                if (!amr_enc_info[i].config.wb_mode) {
#if FEATURE_AUDIO_NBAMR_ENC == 1
                    amrnb_enc_config config = { 0 };
                    config.name = 0;
                    if (strlen(amr_enc_info[i].file_name) > 0)
                        config.out_name = amr_enc_info[i].file_name;
                    config.dtx_mode = amr_enc_info[i].config.dtx;
                    config.mode = amr_enc_info[i].config.rate;
                    config.output_format = 0;//AMR_TX_WMF
                    if (amrnb_encode_open(&config, &amr_enc_info[i].handle) == 0) {
                        acm_audio_record_config_t record_config;
                        memset(&record_config, 0, sizeof(record_config));
                        record_config.mode = (acm_audio_record_mode_t)amr_enc_info[i].config.mode;
                        record_config.rate = 8000;//fixed for nb-amr
                        record_config.event_cb = amr_record_event_callback;
                        record_config.gain_value = (int32_t)(AUDIO_GAIN_QFORMAT * amr_enc_info[i].config.gain);
                        if (acm_audio_record_open(&record_config, &amr_enc_info[i].handle_pcm) != 0) {
                            amrnb_encode_close(amr_enc_info[i].handle);
                            memset(&amr_enc_info[i], 0, sizeof(AMR_ENC_USER_INFO));
                        }
                    }
#endif
                }
                else {
#if FEATURE_AUDIO_WBAMR_ENC == 1
                    amrwb_enc_config config = { 0 };
                    config.name = 0;
                    if (strlen(amr_enc_info[i].file_name) > 0)
                        config.out_name = amr_enc_info[i].file_name;
                    config.mode = amr_enc_info[i].config.rate;
                    config.frame_type = 2;//VOAMRWB_RFC3267
                    if (amrwb_encode_open(&config, &amr_enc_info[i].handle_wb) == 0) {
                        acm_audio_record_config_t record_config;
                        memset(&record_config, 0, sizeof(record_config));
                        record_config.mode = (acm_audio_record_mode_t)amr_enc_info[i].config.mode;
                        record_config.rate = 16000;//fixed for wb-amr
                        record_config.event_cb = amr_record_event_callback;
                        record_config.gain_value = (int32_t)(AUDIO_GAIN_QFORMAT * amr_enc_info[i].config.gain);
                        if (acm_audio_record_open(&record_config, &amr_enc_info[i].handle_pcm) != 0) {
                            amrwb_encode_close(amr_enc_info[i].handle);
                            memset(&amr_enc_info[i], 0, sizeof(AMR_ENC_USER_INFO));
                        }
                    }
#endif
                }
            }
        }
    }

    static int amr_enc_continue(void) {
        int i = 0;
        for (i = 0; i < AMR_ENC_USER_MAXIMUM; i++) {
            while (1) {
#if FEATURE_AUDIO_WBAMR_ENC == 1
                int16_t pcm_data[320] = { 0 };
#else
                int16_t pcm_data[160] = { 0 };
#endif
                uint32_t pcm_size = sizeof(pcm_data);
                int error_code = acm_audio_record_read(amr_enc_info[i].handle_pcm, pcm_data, &pcm_size, 0);
                if (amr_enc_info[i].close_flag == 0 && error_code == 0) {
                    DIAG_FILTER(AUDIO, AMR_ENC, amrenc_source, DIAG_INFORMATION)
                    diagStructPrintf("amrenc_source_dump", pcm_data, pcm_size);
                    if (!amr_enc_info[i].config.wb_mode) {
#if FEATURE_AUDIO_NBAMR_ENC == 1
                        amrnb_encode_set(amr_enc_info[i].handle, (const int16_t*)pcm_data, pcm_size);
                        amrnb_encode_do(amr_enc_info[i].handle);
                        amrnb_encode_write(amr_enc_info[i].handle);
                        if (amr_enc_info[i].config.callback) {
                            uint8_t current_frame[AMR_FRAME_SIZE_MAXIMUM] = { 0 };
                            uint32_t sz = sizeof(current_frame);
                            error_code = amrnb_encode_get(amr_enc_info[i].handle, &current_frame[0], &sz);
                            if (error_code == 0) {
                                amr_enc_info[i].config.callback(&current_frame[0], sz);
                            }
                        }
#endif
                    }
                    else {
#if FEATURE_AUDIO_WBAMR_ENC == 1
                        amrwb_encode_set(amr_enc_info[i].handle_wb, (const uint16_t*)pcm_data, pcm_size);
                        amrwb_encode_do(amr_enc_info[i].handle_wb);
                        amrwb_encode_write(amr_enc_info[i].handle_wb);
                        if (amr_enc_info[i].config.callback) {
                            uint8_t current_frame[AMR_FRAME_SIZE_MAXIMUM] = { 0 };
                            uint32_t sz = sizeof(current_frame);
                            error_code = amrwb_encode_get(amr_enc_info[i].handle, &current_frame[0], &sz);
                            if (error_code == 0) {
                                amr_enc_info[i].config.callback(&current_frame[0], sz);
                            }
                        }
#endif
                    }
                }
                else {
                    break;
                }
            }
        }
        return 0;
    }

    static void amr_enc_stop(void) {
        int i = 0;
        for (i = 0; i < AMR_ENC_USER_MAXIMUM; i++) {
            if (amr_enc_info[i].close_flag) {
                if (amr_enc_info[i].handle) {
                    if (!amr_enc_info[i].config.wb_mode) {
#if FEATURE_AUDIO_NBAMR_ENC == 1
                        amrnb_encode_close(amr_enc_info[i].handle);
#endif
                    }
                    else {
#if FEATURE_AUDIO_WBAMR_ENC == 1
                        amrwb_encode_close(amr_enc_info[i].handle_wb);
#endif  
                    }
                }
                if (amr_enc_info[i].handle_pcm) {
                    acm_audio_record_close(amr_enc_info[i].handle_pcm);
                }
                if (amr_enc_info[i].magic) {
                    free(amr_enc_info[i].magic);
                }
                memset(&amr_enc_info[i], 0, sizeof(AMR_ENC_USER_INFO));
            }
        }
    }

    static void amrEnc(void*) {
        // Encode loop
        while (1) {
            UINT32 event = 0;
            OSAFlagWait(amrenc_flag_ref, AMR_VOCODER_TASK_MASK, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND);
            DIAG_FILTER(AUDIO, AMR_ENC, amr_enc_event, DIAG_INFORMATION)
            diagPrintf("amr_enc_event:%x", event);

            if (AMR_ENC_CONTINUE & event) {
                amr_enc_continue();
            }

            if (AMR_ENC_START & event) {
                amr_enc_start();
            }

            if (AMR_ENC_STOP & event) {
                amr_enc_stop();
            }
        }
    }

    void amrEncInit(void) {
        if (!amrenc_sema_ref) {
            OS_STATUS status = OSASemaphoreCreate(&amrenc_sema_ref, 1, OSA_FIFO);
            ASSERT(status == OS_SUCCESS);
        }
    }

    static void amrenc_task_init(void) {
        static int inited = 0;
        OS_STATUS status;
#if FEATURE_AUDIO_WBAMR_ENC == 1
        size_t amr_stack_size = 1024 * 100;
#else
        size_t amr_stack_size = 1024 * 45;
#endif
        int amr_thread_priority = 75;

        if (!inited) {
            status = OSAFlagCreate(&amrenc_flag_ref);
            ASSERT(status == OS_SUCCESS);

            amrenc_stack_ptr = malloc(amr_stack_size);
            ASSERT(amrenc_stack_ptr);

            status = OSATaskCreate(&amrenc_task_ref, amrenc_stack_ptr, amr_stack_size, amr_thread_priority, "amrEnc", amrEnc, NULL);
            ASSERT(status == OS_SUCCESS);

            amrEncInit();
            inited = 1;
        }
    }

    static int openEncHandle(const char* file_name, const AmrEncConfigInfo* config, AmrEncodeHandle* handle) {
        int i = 0, error_code = -1;
        OSA_STATUS osa_status;
        osa_status = OSASemaphoreAcquire(amrenc_sema_ref, OSA_SUSPEND);
        ASSERT(osa_status == OS_SUCCESS);
        for (i = 0; i < AMR_ENC_USER_MAXIMUM; i++) {
            if (file_name && strlen(file_name) > 0 && strcmp(amr_enc_info[i].file_name, file_name) == 0) {
                error_code = -2;
                break;
            }
            if (!config->callback && (!file_name)) {
                error_code = -3;
                break;
            }
            if (amr_enc_info[i].handle == 0) {
                if (file_name)
                    strcpy(amr_enc_info[i].file_name, file_name);
                memcpy(&amr_enc_info[i].config, config, sizeof(AmrEncConfigInfo));
                amr_enc_info[i].magic = (char*)malloc(sizeof(char));
                if (handle) {
                    *handle = (AmrEncodeHandle)amr_enc_info[i].magic;
                }
                error_code = 0;
                break;
            }
        }
        osa_status = OSASemaphoreRelease(amrenc_sema_ref);
        ASSERT(osa_status == OS_SUCCESS);

        DIAG_FILTER(AUDIO, AMR_ENC, openEncHandle, DIAG_INFORMATION)
        diagPrintf("error_code:%d", error_code);
        return error_code;
    }

    static int closeEncHandle(AmrEncodeHandle handle) {
        int i = 0, error_code = -1;
        OSA_STATUS osa_status;
        osa_status = OSASemaphoreAcquire(amrenc_sema_ref, OSA_SUSPEND);
        ASSERT(osa_status == OS_SUCCESS);
        for (i = 0; i < AMR_ENC_USER_MAXIMUM; i++) {
            if ((handle & amr_enc_info[i].unique_id) == amr_enc_info[i].unique_id) {
                amr_enc_info[i].close_flag = 1;
                error_code = 0;
                break;
            }
        }
        osa_status = OSASemaphoreRelease(amrenc_sema_ref);
        ASSERT(osa_status == OS_SUCCESS);
        return error_code;
    }

    int amrEncStart(const char* file_name, const AmrEncConfigInfo* config, AmrEncodeHandle* handle) {
        DIAG_FILTER(AUDIO, AMR_ENC, amrEncStart, DIAG_INFORMATION)
        diagPrintf("file_name:%s, rev:%s", file_name, AMR_VOCODER_REVISION);

        if (amrenc_task_ref == NULL) {
            amrenc_task_init();
        }

        if (openEncHandle(file_name, config, handle) != 0)
            return -1;

        OSAFlagSet(amrenc_flag_ref, AMR_ENC_START, OSA_FLAG_OR);
        return 0;
    }

    int amrEncStopWithName(const char* file_name) {
        int i = 0;
        DIAG_FILTER(AUDIO, AMR_ENC, amrEncStopWithName, DIAG_INFORMATION)
        diagPrintf("amrEncStopWithName:%s", file_name);

        for (i = 0; i < AMR_ENC_USER_MAXIMUM; i++) {
            if (file_name && strlen(file_name) > 0 && strcmp(file_name, amr_enc_info[i].file_name) == 0)
                return amrEncStop(amr_enc_info[i].unique_id);
        }

        return -1;
    }

    int amrGetPCMInHandle(AmrEncodeHandle handle, uint32_t* record_handle) {
        int i = 0;
        DIAG_FILTER(AUDIO, AMR_ENC, amrGetPCMInHandle, DIAG_INFORMATION)
        diagPrintf("handle:0x%lx, record_handle:0x%lx", handle, record_handle);
        for (i = 0; i < AMR_ENC_USER_MAXIMUM; i++) {
            if ((handle & amr_enc_info[i].unique_id) == amr_enc_info[i].unique_id) {
                if (record_handle) {
                    *record_handle = amr_enc_info[i].handle_pcm;
                    return 0;
                }
            }
        }

        return -1;
    }

    int amrEncStop(AmrEncodeHandle handle) {
        DIAG_FILTER(AUDIO, AMR_ENC, amrEncStop, DIAG_INFORMATION)
        diagPrintf("handle:0x%x", handle);
        if (closeEncHandle(handle) != 0)
            return -1;

        OSAFlagSet(amrenc_flag_ref, AMR_ENC_STOP, OSA_FLAG_OR);
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_enc_test_start
    int amr_enc_test_start(void) {
        int rc = 0;
        AmrEncConfigInfo config = { 0 };
        config.mode = AUDIO_RECORD_MODE_TX; // record tx pcm to amr

        rc = amrEncStart("test_enc.amr", &config, 0);
        if (rc) {
            DIAG_FILTER(AUDIO, AMR_ENC, amr_enc_test_start_fail, DIAG_INFORMATION)
            diagPrintf("rc:%d", rc);
        }
        else {
            DIAG_FILTER(AUDIO, AMR_ENC, amr_enc_test_start_success, DIAG_INFORMATION)
            diagPrintf("rc:%d", rc);
        }
        return 0;
    }

    static void on_amr_frame_encoded(const uint8_t* buf, uint32_t size) {
        DIAG_FILTER(AUDIO, AMR_ENC, on_amr_frame_encoded, DIAG_INFORMATION)
        diagStructPrintf("frame_dump", (void*)buf, size);
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_enc_test_stop
    int amr_enc_test_stop(void) {
        amrEncStopWithName("test_enc.amr");
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_enc_test2_start
    int amr_enc_test2_start(void) {
        AmrEncConfigInfo config = { 0 };
        config.rate = 5; // MR795
        config.mode = AUDIO_RECORD_MODE_TX; // record tx pcm to amr
        config.callback = on_amr_frame_encoded;
        amrEncStart(0, &config, 0);
        return 0;
    }

    //ICAT EXPORTED FUNCTION - Audio,AMR,amr_enc_test2_stop
    int amr_enc_test2_stop(void) {
        amrEncStop(0xffffffff);
        return 0;
    }
#endif
}
