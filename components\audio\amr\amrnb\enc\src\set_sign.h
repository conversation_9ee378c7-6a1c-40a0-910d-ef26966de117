/* ------------------------------------------------------------------
 * Copyright (C) 1998-2009 PacketVideo
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied.
 * See the License for the specific language governing permissions
 * and limitations under the License.
 * -------------------------------------------------------------------
 */
/****************************************************************************************
Portions of this file are derived from the following 3GPP standard:

    3GPP TS 26.073
    ANSI-C code for the Adaptive Multi-Rate (AMR) speech codec
    Available from http://www.3gpp.org

(C) 2004, 3GPP Organizational Partners (ARIB, ATIS, CCSA, ETSI, TTA, TTC)
Permission to distribute, modify and use this file under the standard license
terms listed above has been obtained from the copyright holder.
****************************************************************************************/
/*
------------------------------------------------------------------------------



 Filename: /audio/gsm_amr/c/include/set_sign.h

     Date: 08/11/2000

------------------------------------------------------------------------------
 REVISION HISTORY


 Description: Changed function prototype for set_sign12k2(); pointer to
                overflow flag is passed in as a parameter.

 Description: Moved _cplusplus #ifdef after Include section.

 Description:


------------------------------------------------------------------------------
 INCLUDE DESCRIPTION

 This file contains all the constant definitions and prototype definitions
 needed by the set_sign() and set_sign12k2() function.

------------------------------------------------------------------------------
*/

#ifndef SET_SIGN_H
#define SET_SIGN_H "@(#)$Id $"

/*----------------------------------------------------------------------------
; INCLUDES
----------------------------------------------------------------------------*/
#include    "basicop_malloc.h"

/*--------------------------------------------------------------------------*/
#ifdef __cplusplus
extern "C"
{
#endif

    /*----------------------------------------------------------------------------
    ; MACROS
    ; Define module specific macros here
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; DEFINES
    ; Include all pre-processor statements here.
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; EXTERNAL VARIABLES REFERENCES
    ; Declare variables used in this module but defined elsewhere
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; SIMPLE TYPEDEF'S
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; ENUMERATED TYPEDEF'S
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; STRUCTURES TYPEDEF'S
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; GLOBAL FUNCTION DEFINITIONS
    ; Function Prototype declaration
    ----------------------------------------------------------------------------*/
    void set_sign(Word16 dn[],   /* i/o : correlation between target and h[]    */
    Word16 sign[], /* o   : sign of dn[]                          */
    Word16 dn2[],  /* o   : maximum of correlation in each track. */
    Word16 n       /* i   : # of maximum correlations in dn2[]    */
                 );

    void set_sign12k2(
        Word16 dn[],        /* i/o : correlation between target and h[]         */
        Word16 cn[],        /* i   : residual after long term prediction        */
        Word16 sign[],      /* o   : sign of d[n]                               */
        Word16 pos_max[],   /* o   : position of maximum correlation            */
        Word16 nb_track,    /* i   : number of tracks tracks                    */
        Word16 ipos[],      /* o   : starting position for each pulse           */
        Word16 step,        /* i   : the step size in the tracks                */
        Flag   *pOverflow   /* i/o : overflow flag                              */
    );
    /*----------------------------------------------------------------------------
    ; END
    ----------------------------------------------------------------------------*/
#ifdef __cplusplus
}
#endif

#endif /* _SET_SIGN_H_ */
