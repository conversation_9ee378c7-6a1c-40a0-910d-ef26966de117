
#ifndef _IMAGE_H_
#define _IMAGE_H_

#include "quec_decoder_config.h"
#ifdef HAVE_INTTYPES_H
# include <inttypes.h>
#endif
#include <stdlib.h>
//#include <assert.h>

#include <quec_decoder.h>
//#include "error.h"
#include "symbol.h"
#include "refcnt.h"

/* adapted from v4l2 spec */
#define fourcc(a, b, c, d)                      \
    ((unsigned int)(a) | ((unsigned int)(b) << 8) |     \
     ((unsigned int)(c) << 16) | ((unsigned int)(d) << 24))

/* unpack size/location of component */
#define RGB_SIZE(c)   ((c) >> 5)
#define RGB_OFFSET(c) ((c) & 0x1f)

/* coarse image format categorization.
 * to limit conversion variations
 */
typedef enum quec_decoder_format_group_e {
    QUEC_DECODER_FMT_GRAY,
    QUEC_DECODER_FMT_YUV_PLANAR,
    QUEC_DECODER_FMT_YUV_PACKED,
    QUEC_DECODER_FMT_RGB_PACKED,
    QUEC_DECODER_FMT_YUV_NV,
    QUEC_DECODER_FMT_JPEG,

    /* enum size */
    QUEC_DECODER_FMT_NUM
} quec_decoder_format_group_t;


struct quec_decoder_image_s {
    unsigned int format;            /* fourcc image format code */
    unsigned width, height;     /* image size */
    const void *data;           /* image sample data */
    unsigned long datalen;      /* allocated/mapped size of data */
    void *userdata;             /* user specified data associated w/image */

    /* cleanup handler */
    quec_decoder_image_cleanup_handler_t *cleanup;
    refcnt_t refcnt;            /* reference count */
    quec_decoder_video_t *src;          /* originator */
    int srcidx;                 /* index used by originator */
    quec_decoder_image_t *next;         /* internal image lists */

    unsigned seq;               /* page/frame sequence number */
    quec_decoder_symbol_set_t *syms;    /* decoded result set */
};

/* description of an image format */
typedef struct quec_decoder_format_def_s {
    unsigned int format;                    /* fourcc */
    quec_decoder_format_group_t group;          /* coarse categorization */
    union {
        unsigned char gen[4];                 /* raw bytes */
        struct {
            unsigned char bpp;                /* bits per pixel */
            unsigned char red, green, blue;   /* size/location a la RGB_BITS() */
        } rgb;
        struct {
            unsigned char xsub2, ysub2;       /* chroma subsampling in each axis */
            unsigned char packorder;          /* channel ordering flags
                                         *   bit0: 0=UV, 1=VU
                                         *   bit1: 0=Y/chroma, 1=chroma/Y
                                         */
        } yuv;
        unsigned int cmp;                   /* quick compare equivalent formats */
    } p;
} quec_decoder_format_def_t;


extern int _quec_decoder_best_format(unsigned int, unsigned int*, const unsigned int*);
extern const quec_decoder_format_def_t *_quec_decoder_format_lookup(unsigned int);
extern void _quec_decoder_image_free(quec_decoder_image_t*);


static inline void _quec_decoder_image_refcnt (quec_decoder_image_t *img,
                                       int delta)
{
    if(!_quec_decoder_refcnt(&img->refcnt, delta) && delta <= 0) {
        if(img->cleanup)
            img->cleanup(img);
        if(!img->src)
            _quec_decoder_image_free(img);
    }
}

static inline void _quec_decoder_image_swap_symbols (quec_decoder_image_t *a,
                                             quec_decoder_image_t *b)
{
    quec_decoder_symbol_set_t *tmp = a->syms;
    a->syms = b->syms;
    b->syms = tmp;
}

#endif
