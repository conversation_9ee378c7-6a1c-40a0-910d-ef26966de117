/*
 * Copyright (C) 2014 The Android Open Source Project
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *  * Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *  * Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

 /*--------------------------------------------------------------------------------------------------------------------
 (C) Copyright 2020 ASR Ltd. All Rights Reserved
 -------------------------------------------------------------------------------------------------------------------*/

#include <stdlib.h>
#include <stdio.h>
#include <stdint.h>
#include <string.h>

#include "gsmamr_dec.h"
#include "amrnb_dec_api.h"
#include "audio_file.h"
extern "C" {
#include "utils.h"

    // Constants for AMR-NB
    enum {
        kInputBufferSize = 64,
        kSamplesPerFrame = 160,
        kBitsPerSample = 16,
        kOutputBufferSize = kSamplesPerFrame * kBitsPerSample / 8,
        kSampleRate = 8000,
        kChannels = 1,
        kFileHeaderSize = 6
    };
    static const uint32_t kFrameSizes[] = { 12, 13, 15, 17, 19, 20, 26, 31, 7 };

    typedef struct amrnb_dec_state {
        AUDIO_FILE_ID fpInput;
        AUDIO_FILE_ID fpOutput;
        void* amrHandle;
        void *inputBuf;
        void *outputBuf;
        uint32_t amrFrameSize;
        uint32_t pcmFrameSize;
        Frame_Type_3GPP frameType;
    }amrnb_dec_state;

    int amrnb_decode_open(const amrnb_dec_config* config, amrnb_dec_handle* handle) {
        if (config && handle) {
            int bytesRead, err;
            amrnb_dec_state* st = (amrnb_dec_state*)malloc(sizeof(amrnb_dec_state));
            if (!st)
                return -1;

            memset(st, 0, sizeof(amrnb_dec_state));
            // Open the input file
            if (config->name && strlen(config->name) > 0) {
                st->fpInput = common_fopen(config->name, "rb");
                if (!st->fpInput)
                    goto amrnb_decode_open_end;
            }

            // Open the output file
            if (config->out_name) {
                st->fpOutput = common_fopen(config->out_name, "wb");
                if (!st->fpOutput)
                    goto amrnb_decode_open_end;
            }

            // Validate the input AMR file
            if (st->fpInput) {
                char header[kFileHeaderSize];
                bytesRead = common_fread(header, 1, kFileHeaderSize, st->fpInput);
                if (bytesRead != kFileHeaderSize || memcmp(header, "#!AMR\n", kFileHeaderSize)) {
                    goto amrnb_decode_open_end;
                }
            }

            // Create AMR-NB decoder instance
            err = GSMInitDecode(&st->amrHandle, (Word8*)"AMRNBDecoder");
            if (err != 0) {
                goto amrnb_decode_open_end;
            }

            //Allocate input buffer
            st->inputBuf = malloc(kInputBufferSize);
            ASSERT(st->inputBuf != NULL);

            //Allocate output buffer
            st->outputBuf = malloc(kOutputBufferSize);
            ASSERT(st->outputBuf != NULL);

            st->pcmFrameSize = kOutputBufferSize;
            *handle = (amrnb_dec_handle)st;
            return 0;

        amrnb_decode_open_end:
            if (st) {
                if (st->fpInput)
                    common_fclose(st->fpInput);
                if (st->fpOutput)
                    common_fclose(st->fpOutput);
                free(st);
                return -1;
            }
        }

        return -1;
    }

    int amrnb_decode_write(amrnb_dec_handle handle) {
        amrnb_dec_state* st = (amrnb_dec_state*)handle;
        if (st && st->fpOutput) {
            common_fwrite(st->outputBuf, kOutputBufferSize, 1, st->fpOutput);
            return 0;
        }

        return -1;
    }

    int amrnb_decode_read(amrnb_dec_handle handle) {
        amrnb_dec_state* st = (amrnb_dec_state*)handle;
        if (st) {
            // Decode loop
            uint32_t bytesRead = 0;

            // Read mode
            uint8_t mode;
            bytesRead = common_fread(&mode, 1, 1, st->fpInput);
            if (bytesRead != 1) {
                return -2;
            }

            // Find frame type
            st->frameType = (Frame_Type_3GPP)((mode >> 3) & 0x0f);
            if (/*st->frameType >= AMR_475 && */st->frameType <= AMR_SID) {
                // Find frame size
                st->amrFrameSize = kFrameSizes[st->frameType];
                bytesRead = common_fread(st->inputBuf, 1, st->amrFrameSize, st->fpInput);
                if (bytesRead != st->amrFrameSize) {
                    return -4;
                }
            }
            else {
                st->amrFrameSize = 0;
            }
            return 0;
        }

        return -1;
    }

    int amrnb_decode_do(amrnb_dec_handle handle) {
        amrnb_dec_state* st = (amrnb_dec_state*)handle;
        if (st) {
            //Decode frame
            int32_t decodeStatus;
            decodeStatus = AMRDecode(st->amrHandle, st->frameType, (uint8_t*)st->inputBuf, (int16_t*)st->outputBuf, MIME_IETF);
            if (decodeStatus == -1) {
                return -2;
            }
            return 0;
        }

        return -1;
    }

    int amrnb_decode_loop(amrnb_dec_handle handle) {
        amrnb_dec_state* st = (amrnb_dec_state*)handle;
        if (st) {
            if (amrnb_decode_read(handle) != 0)
                return -2;

            if (amrnb_decode_do(handle) != 0)
                return -3;

            if (amrnb_decode_write(handle) != 0)
                return -4;

            return 0;
        }

        return -1;
    }

    int amrnb_decode_get_rate(amrnb_dec_handle handle, int32_t* rate) {
        amrnb_dec_state* st = (amrnb_dec_state*)handle;
        if (st && rate) {
            *rate = st->frameType;
        }

        return -1;
    }

    int amrnb_decode_get_amr(amrnb_dec_handle handle, uint8_t* output_data, uint32_t* size) {
        amrnb_dec_state* st = (amrnb_dec_state*)handle;
        if (st && output_data) {
            if (size && *size >= st->amrFrameSize) {
                *size = st->amrFrameSize;
                if (st->amrFrameSize > 0)
                    memcpy(output_data, st->inputBuf, st->amrFrameSize);
                return 0;
            }
        }

        return -1;
    }

    int amrnb_decode_set(amrnb_dec_handle handle, const uint8_t* data, uint32_t size, uint32_t* used_size) {
        amrnb_dec_state* st = (amrnb_dec_state*)handle;
        if (st && data) {
            if (size > 0) {
                Frame_Type_3GPP frame_type = (Frame_Type_3GPP)((data[0] >> 3) & 0x0f);
                uint32_t frame_size = 1;
                if (/*frame_type >= AMR_475 && */frame_type <= AMR_122) {
                    frame_size = kFrameSizes[frame_type] + 1;
                }

                if (size >= frame_size) {
                    if (frame_size > 1)
                        memcpy(st->inputBuf, &data[1], frame_size - 1);
                    st->frameType = frame_type;
                    st->amrFrameSize = frame_size - 1;
                    if (used_size)
                        *used_size = frame_size;
                    return 0;
                }
            }
        }

        return -1;
    }

    int amrnb_decode_get(amrnb_dec_handle handle, int16_t* output_data, uint32_t* size) {
        amrnb_dec_state* st = (amrnb_dec_state*)handle;
        if (st && output_data) {
            if (size && *size >= kOutputBufferSize) {
                *size = kOutputBufferSize;
                memcpy(output_data, st->outputBuf, kOutputBufferSize);
                return 0;
            }
        }

        return -1;
    }

    int amrnb_decode_close(amrnb_dec_handle handle) {
        amrnb_dec_state* st = (amrnb_dec_state*)handle;
        if (st) {
            // Close input and output file
            if (st->fpInput)
                common_fclose(st->fpInput);
            if (st->fpOutput)
                common_fclose(st->fpOutput);

            //Free allocated memory
            if (st->inputBuf)
                free(st->inputBuf);
            if (st->outputBuf)
                free(st->outputBuf);

            // Close decoder instance
            if (st->amrHandle)
                GSMDecodeFrameExit(&st->amrHandle);

            free(st);
            return 0;
        }

        return -1;
    }
}

#if DECODER_TEST_AMR_NB == 1
int main(int argc, char *argv[]) {
    if (argc < 2)
        return -1;

    amrnb_dec_config config = { 0 };
    amrnb_dec_handle handle = 0;
    if (argc > 1)
        config.name = argv[1];
    if (argc > 2)
        config.out_name = argv[2];

    if (amrnb_decode_open(&config, &handle) != 0)
        return -1;

    while (1) {
        if (amrnb_decode_loop(handle) != 0)
            break;
    }

    amrnb_decode_close(handle);
    return 0;
}
#endif
