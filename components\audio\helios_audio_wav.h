
#ifndef __HELIOS_AUDIO_WAV_H__
#define __HELIOS_AUDIO_WAV_H__

typedef enum
{
    AUDIOHAL_ERR_NO = 0,  //No error
    AUDIOHAL_ERR_RESOURCE_RESET,
    AUDIOHAL_ERR_RESOURCE_BUSY,
    AUDIOHAL_ERR_RESOURCE_TIMEOUT,
    AUDIOHAL_ERR_RESOURCE_NOT_ENABLED,
    AUDIOHAL_ERR_BAD_PARAMETER,
    
    AUDIOHAL_ERR_UART_RX_OVERFLOW,
    AUDIOHAL_ERR_UART_TX_OVERFLOW,
    AUDIOHAL_ERR_UART_PARITY,
    AUDIOHAL_ERR_UART_FRAMING,
    AUDIOHAL_ERR_UART_BREAK_INT,

    AUDIOHAL_ERR_TIM_RTC_NOT_VALID,
    AUDIOHAL_ERR_TIM_RTC_ALARM_NOT_ENABLED,
    AUDIOHAL_ERR_TIM_RTC_ALARM_NOT_DISABLED,

    AUDIOHAL_ERR_COMMUNICATION_FAILED,
    
    /* Must be at the end */
    AUDIOHAL_ERR_QTY,


	AUDIOHAL_ERR_ENUM_32_BIT		    	= 0x7FFFFFFF //32bit enum compiling enforcement
} AUDIOHAL_ERR_T;

typedef struct {
    char ChunkID[4];
    unsigned int ChunkSize; 
    char Format[4];
    char SubChunk1ID[4]; 
    unsigned int SubChunk1Size; 
    unsigned short int AudioFormat; 
    unsigned short int NumChannels; 
    unsigned short int SampleRate; 
    unsigned int ByteRate; 
    unsigned short int BlockAlign; 
    unsigned short int BitsPerSample;
    char SubChunk2ID[4];
    unsigned int SubChunk2Size; 
}HELIOS_WAV_TAG;

int Helios_wav_file_start(char *file_name, cb_on_player aud_cb);
int Helios_wav_file_stop(void);



#endif
