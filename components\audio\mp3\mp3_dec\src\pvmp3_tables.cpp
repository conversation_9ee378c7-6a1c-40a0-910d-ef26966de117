/* ------------------------------------------------------------------
 * Copyright (C) 1998-2009 PacketVideo
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied.
 * See the License for the specific language governing permissions
 * and limitations under the License.
 * -------------------------------------------------------------------
 */
/*
------------------------------------------------------------------------------

   PacketVideo Corp.
   MP3 Decoder Library

   Filename: pvmp3_tables.cpp

   Date: 09/21/2007

------------------------------------------------------------------------------
 REVISION HISTORY


 Description:


------------------------------------------------------------------------------


------------------------------------------------------------------------------
 REFERENCES

 [1] ISO MPEG Audio Subgroup Software Simulation Group (1996)
     ISO 13818-3 MPEG-2 Audio Decoder - Lower Sampling Frequency Extension

------------------------------------------------------------------------------
*/


/*----------------------------------------------------------------------------
; INCLUDES
----------------------------------------------------------------------------*/

#include "pvmp3_tables.h"

/*----------------------------------------------------------------------------
; MACROS
; Define module specific macros here
----------------------------------------------------------------------------*/


/*----------------------------------------------------------------------------
; DEFINES
; Include all pre-processor statements here. Include conditional
; compile variables also.
----------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------
; LOCAL FUNCTION DEFINITIONS
; Function Prototype declaration
----------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------
; LOCAL STORE/BUFFER/POINTER DEFINITIONS
; Variable declaration - defined here and used outside this module
----------------------------------------------------------------------------*/

const int32  mp3_s_freq[4][4] =
{
    {44100, 48000, 32000, 0},
    {22050, 24000, 16000, 0},
    {11025, 12000,  8000, 0}
}; // MPEG-2.5


/*
 *  144000./s_freq
 */
const int32 inv_sfreq[4] =
{
    Qfmt_28(3.26530612244898),
    Qfmt_28(3.0),
    Qfmt_28(4.5),
    0
};


/* 1: MPEG-1, 0: MPEG-2 LSF, 1995-07-11 shn */


const int16  mp3_bitrate[3][15] =
{
    {0, 32, 40, 48, 56, 64, 80, 96, 112, 128, 160, 192, 224, 256, 320},
    {0, 8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160},
    {0, 8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160}
};


const mp3_scaleFactorBandIndex mp3_sfBandIndex[9] =
{

    /* MPEG 1 */

    {{0, 4, 8, 12, 16, 20, 24, 30, 36, 44, 52, 62, 74, 90, 110, 134, 162, 196, 238, 288, 342, 418, 576},
    {0, 4, 8, 12, 16, 22, 30, 40, 52, 66, 84, 106, 136, 192}},
    {{0, 4, 8, 12, 16, 20, 24, 30, 36, 42, 50, 60, 72, 88, 106, 128, 156, 190, 230, 276, 330, 384, 576},
    {0, 4, 8, 12, 16, 22, 28, 38, 50, 64, 80, 100, 126, 192}},
    {{0, 4, 8, 12, 16, 20, 24, 30, 36, 44, 54, 66, 82, 102, 126, 156, 194, 240, 296, 364, 448, 550, 576},
        {0, 4, 8, 12, 16, 22, 30, 42, 58, 78, 104, 138, 180, 192}},

    /* MPEG 2 - LSF */

    {{0, 6, 12, 18, 24, 30, 36, 44, 54, 66, 80, 96, 116, 140, 168, 200, 238, 284, 336, 396, 464, 522, 576},
    {0, 4, 8, 12, 18, 24, 32, 42, 56, 74, 100, 132, 174, 192}},
    {{0, 6, 12, 18, 24, 30, 36, 44, 54, 66, 80, 96, 114, 136, 162, 194, 232, 278, 332, 394, 464, 540, 576},
    {0, 4, 8, 12, 18, 26, 36, 48, 62, 80, 104, 136, 180, 192}},
    {{0, 6, 12, 18, 24, 30, 36, 44, 54, 66, 80, 96, 116, 140, 168, 200, 238, 284, 336, 396, 464, 522, 576},
        {0, 4, 8, 12, 18, 26, 36, 48, 62, 80, 104, 134, 174, 192}},

    /* MPEG 2.5  extension */

    {{0, 6, 12, 18, 24, 30, 36, 44, 54, 66, 80, 96, 116, 140, 168, 200, 238, 284, 336, 396, 464, 522, 576},
    {0, 4, 8, 12, 18, 26, 36, 48, 62, 80, 104, 134, 174, 192}},
    {{0, 6, 12, 18, 24, 30, 36, 44, 54, 66, 80, 96, 116, 140, 168, 200, 238, 284, 336, 396, 464, 522, 576},
    {0, 4, 8, 12, 18, 26, 36, 48, 62, 80, 104, 134, 174, 192}},
    {{0, 12, 24, 36, 48, 60, 72, 88, 108, 132, 160, 192, 232, 280, 336, 400, 476, 566, 568, 570, 572, 574, 576},
        {0, 8, 16, 24, 36, 52, 72, 96, 124, 160, 162, 164, 166, 192}}

};

#define INV_Q31( x)   (int32)(0x7FFFFFFF/(float)(x) - 1.0f)

const int32 mp3_shortwindBandWidths[9][13] =
{
    { INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(6), INV_Q31(8), INV_Q31(10),
        INV_Q31(12), INV_Q31(14), INV_Q31(18), INV_Q31(22), INV_Q31(30), INV_Q31(56)},
    { INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(6), INV_Q31(6), INV_Q31(10),
      INV_Q31(12), INV_Q31(14), INV_Q31(16), INV_Q31(20), INV_Q31(26), INV_Q31(66)},
    { INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(6), INV_Q31(8), INV_Q31(12),
      INV_Q31(16), INV_Q31(20), INV_Q31(26), INV_Q31(34), INV_Q31(42), INV_Q31(12)},
    { INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(6), INV_Q31(6), INV_Q31(8), INV_Q31(10),
      INV_Q31(14), INV_Q31(18), INV_Q31(26), INV_Q31(32), INV_Q31(42), INV_Q31(18)},
    { INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(6), INV_Q31(8), INV_Q31(10), INV_Q31(12),
      INV_Q31(14), INV_Q31(18), INV_Q31(24), INV_Q31(32), INV_Q31(44), INV_Q31(12)},
    { INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(6), INV_Q31(8), INV_Q31(10), INV_Q31(12),
      INV_Q31(14), INV_Q31(18), INV_Q31(24), INV_Q31(30), INV_Q31(40), INV_Q31(18)},
    { INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(6), INV_Q31(8), INV_Q31(10), INV_Q31(12),
      INV_Q31(14), INV_Q31(18), INV_Q31(24), INV_Q31(30), INV_Q31(40), INV_Q31(18)},
    { INV_Q31(4), INV_Q31(4), INV_Q31(4), INV_Q31(6), INV_Q31(8), INV_Q31(10), INV_Q31(12),
      INV_Q31(14), INV_Q31(18), INV_Q31(24), INV_Q31(30), INV_Q31(40), INV_Q31(18)},
    { INV_Q31(8), INV_Q31(8), INV_Q31(8), INV_Q31(12), INV_Q31(16), INV_Q31(20), INV_Q31(24),
      INV_Q31(28), INV_Q31(36), INV_Q31(2), INV_Q31(2), INV_Q31(2), INV_Q31(26)}
};


#define Q30_fmt(a)    (int32((0x40000000)*(a)))

const int32 pqmfSynthWin[(HAN_SIZE/2) + 8] =
{
    Q30_fmt(-0.000015259F), Q30_fmt(0.000396729F), Q30_fmt(0.000473022F), Q30_fmt(0.003173828F),
    Q30_fmt(0.003326416F), Q30_fmt(0.006118770F), Q30_fmt(0.007919310F), Q30_fmt(0.031478880F),
    Q30_fmt(0.030517578F), Q30_fmt(0.073059080F), Q30_fmt(0.084182740F), Q30_fmt(0.108856200F),
    Q30_fmt(0.090927124F), Q30_fmt(0.543823240F), Q30_fmt(0.600219727F), Q30_fmt(1.144287109F),

    Q30_fmt(-0.000015259F), Q30_fmt(0.000366211F), Q30_fmt(0.000534058F), Q30_fmt(0.003082275F),
    Q30_fmt(0.003387451F), Q30_fmt(0.005294800F), Q30_fmt(0.008865360F), Q30_fmt(0.031738280F),
    Q30_fmt(0.029785160F), Q30_fmt(0.067520140F), Q30_fmt(0.089706420F), Q30_fmt(0.116577150F),
    Q30_fmt(0.080688480F), Q30_fmt(0.515609740F), Q30_fmt(0.628295900F), Q30_fmt(1.142211914F),

    Q30_fmt(-0.000015259F), Q30_fmt(0.000320435F), Q30_fmt(0.000579834F), Q30_fmt(0.002990723F),
    Q30_fmt(0.003433228F), Q30_fmt(0.004486080F), Q30_fmt(0.009841920F), Q30_fmt(0.031845090F),
    Q30_fmt(0.028884890F), Q30_fmt(0.061996460F), Q30_fmt(0.095169070F), Q30_fmt(0.123474120F),
    Q30_fmt(0.069595340F), Q30_fmt(0.487472530F), Q30_fmt(0.656219480F), Q30_fmt(1.138763428F),

    Q30_fmt(-0.000015259F), Q30_fmt(0.000289917F), Q30_fmt(0.000625610F), Q30_fmt(0.002899170F),
    Q30_fmt(0.003463745F), Q30_fmt(0.003723140F), Q30_fmt(0.010849000F), Q30_fmt(0.031814580F),
    Q30_fmt(0.027801510F), Q30_fmt(0.056533810F), Q30_fmt(0.100540160F), Q30_fmt(0.129577640F),
    Q30_fmt(0.057617190F), Q30_fmt(0.459472660F), Q30_fmt(0.683914180F), Q30_fmt(1.133926392F),

    Q30_fmt(-0.000015259F), Q30_fmt(0.000259399F), Q30_fmt(0.000686646F), Q30_fmt(0.002792358F),
    Q30_fmt(0.003479004F), Q30_fmt(0.003005981F), Q30_fmt(0.011886600F), Q30_fmt(0.031661990F),
    Q30_fmt(0.026535030F), Q30_fmt(0.051132200F), Q30_fmt(0.105819700F), Q30_fmt(0.134887700F),
    Q30_fmt(0.044784550F), Q30_fmt(0.431655880F), Q30_fmt(0.711318970F), Q30_fmt(1.127746582F),

    Q30_fmt(-0.000015259F), Q30_fmt(0.000244141F), Q30_fmt(0.000747681F), Q30_fmt(0.002685547F),
    Q30_fmt(0.003479004F), Q30_fmt(0.002334595F), Q30_fmt(0.012939450F), Q30_fmt(0.031387330F),
    Q30_fmt(0.025085450F), Q30_fmt(0.045837400F), Q30_fmt(0.110946660F), Q30_fmt(0.139450070F),
    Q30_fmt(0.031082153F), Q30_fmt(0.404083250F), Q30_fmt(0.738372800F), Q30_fmt(1.120223999F),

    Q30_fmt(-0.000030518F), Q30_fmt(0.000213623F), Q30_fmt(0.000808716F), Q30_fmt(0.002578735F),
    Q30_fmt(0.003463745F), Q30_fmt(0.001693726F), Q30_fmt(0.014022830F), Q30_fmt(0.031005860F),
    Q30_fmt(0.023422240F), Q30_fmt(0.040634160F), Q30_fmt(0.115921020F), Q30_fmt(0.143264770F),
    Q30_fmt(0.016510010F), Q30_fmt(0.376800540F), Q30_fmt(0.765029907F), Q30_fmt(1.111373901F),

    Q30_fmt(-0.000030518F), Q30_fmt(0.000198364F), Q30_fmt(0.000885010F), Q30_fmt(0.002456665F),
    Q30_fmt(0.003417969F), Q30_fmt(0.001098633F), Q30_fmt(0.015121460F), Q30_fmt(0.030532840F),
    Q30_fmt(0.021575930F), Q30_fmt(0.035552980F), Q30_fmt(0.120697020F), Q30_fmt(0.146362300F),
    Q30_fmt(0.001068120F), Q30_fmt(0.349868770F), Q30_fmt(0.791213990F), Q30_fmt(1.101211548F),

    Q30_fmt(-0.000030518F), Q30_fmt(0.000167847F), Q30_fmt(0.000961304F), Q30_fmt(0.002349854F),
    Q30_fmt(0.003372192F), Q30_fmt(0.000549316F), Q30_fmt(0.016235350F), Q30_fmt(0.029937740F),
    Q30_fmt(0.019531250F), Q30_fmt(0.030609130F), Q30_fmt(0.125259400F), Q30_fmt(0.148773190F),
    Q30_fmt(-0.015228270F), Q30_fmt(0.323318480F), Q30_fmt(0.816864010F), Q30_fmt(1.089782715F),

    Q30_fmt(-0.000030518F), Q30_fmt(0.000152588F), Q30_fmt(0.001037598F), Q30_fmt(0.002243042F),
    Q30_fmt(0.003280640F), Q30_fmt(0.000030518F), Q30_fmt(0.017349240F), Q30_fmt(0.029281620F),
    Q30_fmt(0.017257690F), Q30_fmt(0.025817870F), Q30_fmt(0.129562380F), Q30_fmt(0.150497440F),
    Q30_fmt(-0.032379150F), Q30_fmt(0.297210693F), Q30_fmt(0.841949463F), Q30_fmt(1.077117920F),

    Q30_fmt(-0.000045776F), Q30_fmt(0.000137329F), Q30_fmt(0.001113892F), Q30_fmt(0.002120972F),
    Q30_fmt(0.003173828F), Q30_fmt(-0.000442505F), Q30_fmt(0.018463130F), Q30_fmt(0.028533940F),
    Q30_fmt(0.014801030F), Q30_fmt(0.021179200F), Q30_fmt(0.133590700F), Q30_fmt(0.151596070F),
    Q30_fmt(-0.050354000F), Q30_fmt(0.271591190F), Q30_fmt(0.866363530F), Q30_fmt(1.063217163F),

    Q30_fmt(-0.000045776F), Q30_fmt(0.000122070F), Q30_fmt(0.001205444F), Q30_fmt(0.002014160F),
    Q30_fmt(0.003051758F), Q30_fmt(-0.000869751F), Q30_fmt(0.019577030F), Q30_fmt(0.027725220F),
    Q30_fmt(0.012115480F), Q30_fmt(0.016708370F), Q30_fmt(0.137298580F), Q30_fmt(0.152069090F),
    Q30_fmt(-0.069168090F), Q30_fmt(0.246505740F), Q30_fmt(0.890090940F), Q30_fmt(1.048156738F),

    Q30_fmt(-0.000061035F), Q30_fmt(0.000106812F), Q30_fmt(0.001296997F), Q30_fmt(0.001907349F),
    Q30_fmt(0.002883911F), Q30_fmt(-0.001266479F), Q30_fmt(0.020690920F), Q30_fmt(0.026840210F),
    Q30_fmt(0.009231570F), Q30_fmt(0.012420650F), Q30_fmt(0.140670780F), Q30_fmt(0.151962280F),
    Q30_fmt(-0.088775630F), Q30_fmt(0.221984860F), Q30_fmt(0.913055420F), Q30_fmt(1.031936646F),

    Q30_fmt(-0.000061035F), Q30_fmt(0.000106812F), Q30_fmt(0.001388550F), Q30_fmt(0.001785278F),
    Q30_fmt(0.002700806F), Q30_fmt(-0.001617432F), Q30_fmt(0.021789550F), Q30_fmt(0.025909420F),
    Q30_fmt(0.006134030F), Q30_fmt(0.008316040F), Q30_fmt(0.143676760F), Q30_fmt(0.151306150F),
    Q30_fmt(-0.109161380F), Q30_fmt(0.198059080F), Q30_fmt(0.935195920F), Q30_fmt(1.014617920F),

    Q30_fmt(-0.000076294F), Q30_fmt(0.000091553F), Q30_fmt(0.001480103F), Q30_fmt(0.001693726F),
    Q30_fmt(0.002487183F), Q30_fmt(-0.001937866F), Q30_fmt(0.022857670F), Q30_fmt(0.024932860F),
    Q30_fmt(0.002822880F), Q30_fmt(0.004394530F), Q30_fmt(0.146255490F), Q30_fmt(0.150115970F),
    Q30_fmt(-0.130310060F), Q30_fmt(0.174789430F), Q30_fmt(0.956481930F), Q30_fmt(0.996246338F),

    Q30_fmt(0.000000000F), Q30_fmt(0.000442505F), Q30_fmt(0.001586910F), Q30_fmt(0.003250122F),
    Q30_fmt(0.007003780F), Q30_fmt(0.023910525F), Q30_fmt(0.031082153F), Q30_fmt(0.078628545F),
    Q30_fmt(0.148422240F), Q30_fmt(0.100311279F), Q30_fmt(0.572036740F), Q30_fmt(0.976852417F),
    Q30_fmt(1.144989014F), Q30_fmt(-0.572036745F), Q30_fmt(-0.152206421F), Q30_fmt(0.100311279F),

    Q30_fmt(-0.078628540F), Q30_fmt(-0.000686646F), Q30_fmt(0.031082153F), Q30_fmt(-0.007003785F),
    Q30_fmt(0.002227783F), Q30_fmt(0.003250122F), Q30_fmt(-0.000442500F), Q30_fmt(-0.000076294F),
};





const uint16  huffTable_1[8] =
{
    0x1103,    0x0103,    0x1002,    0x1002,
    0x0001,    0x0001,    0x0001,    0x0001
};

const uint16  huffTable_2[15] =
{
    0x1103,    0x0103,    0x1003,    0x0001,
    0x0001,    0x0001,    0x0001,    0x2206,
    0x0206,    0x1205,    0x1205,    0x2105,
    0x2105,    0x2005,    0x2005
};

const uint16 huffTable_3[15] =
{

    0x1003,    0x1102,    0x1102,    0x0102,
    0x0102,    0x0002,    0x0002,    0x2206,
    0x0206,    0x1205,    0x1205,    0x2105,
    0x2105,    0x2005,    0x2005
};

const uint16 huffTable_5[25] =
{

    0x1103,    0x0103,    0x1003,    0x0001,
    0x0001,    0x0001,    0x0001,    0x3106,
    0x3106,    0x1307,    0x0307,    0x3007,
    0x2207,    0x1206,    0x1206,    0x2106,
    0x2106,    0x0206,    0x0206,    0x2006,
    0x2006,    0x3308,    0x2308,    0x3207,
    0x3207
};


const uint16 huffTable_6[26] =
{

    0x1204,    0x2104,    0x2004,    0x0103,
    0x0103,    0x1102,    0x1102,    0x1102,
    0x1102,    0x1003,    0x1003,    0x0003,
    0x0003,    0x2306,    0x3206,    0x3006,
    0x1305,    0x1305,    0x3105,    0x3105,
    0x2205,    0x2205,    0x0205,    0x0205,
    0x3307,    0x0307
};



const uint16 huffTable_7[73] =
{
    0x0103,
    0x1003,
    0x0001,
    0x0001,
    0x0001,
    0x0001,
    0x1206,
    0x2105,
    0x2105,
    0x0206,
    0x2006,
    0x1104,
    0x1104,
    0x1104,
    0x1104,
    0x3509,
    0x4409,
    0x2509,
    0x5209,
    0x1508,
    0x1508,
    0x5108,
    0x5108,
    0x0509,
    0x3409,
    0x5008,
    0x5008,
    0x4309,
    0x3309,
    0x2408,
    0x2408,
    0x4208,
    0x4208,
    0x1407,
    0x1407,
    0x1407,
    0x1407,
    0x4107,
    0x4107,
    0x4107,
    0x4107,
    0x4007,
    0x4007,
    0x4007,
    0x4007,
    0x0408,
    0x0408,
    0x2308,
    0x2308,
    0x3208,
    0x3208,
    0x0308,
    0x0308,
    0x1307,
    0x1307,
    0x1307,
    0x1307,
    0x3107,
    0x3107,
    0x3107,
    0x3107,
    0x3007,
    0x3007,
    0x3007,
    0x3007,
    0x2207,
    0x2207,
    0x2207,
    0x2207,
    0x550a,
    0x450a,
    0x540a,
    0x530a
};

const uint16 huffTable_8[66] =
{
    0x1204,
    0x2104,
    0x1102,
    0x1102,
    0x1102,
    0x1102,
    0x0103,
    0x0103,
    0x1003,
    0x1003,
    0x0002,
    0x0002,
    0x0002,
    0x0002,
    0x2206,
    0x0206,
    0x2006,
    0x2509,
    0x5209,
    0x0509,
    0x1508,
    0x1508,
    0x5108,
    0x5108,
    0x3409,
    0x4309,
    0x5009,
    0x3309,
    0x2408,
    0x2408,
    0x4208,
    0x4208,
    0x1408,
    0x1408,
    0x4107,
    0x4107,
    0x4107,
    0x4107,
    0x0408,
    0x0408,
    0x4008,
    0x4008,
    0x2308,
    0x2308,
    0x3208,
    0x3208,
    0x1308,
    0x1308,
    0x3108,
    0x3108,
    0x0308,
    0x0308,
    0x3008,
    0x3008,
    0x550b,
    0x540b,
    0x450a,
    0x450a,
    0x5309,
    0x5309,
    0x5309,
    0x5309,
    0x350a,
    0x350a,
    0x440a,
    0x440a

};


const uint16 huffTable_9[53] =
{
    0x1204,
    0x2104,
    0x2004,
    0x1103,
    0x1103,
    0x0103,
    0x0103,
    0x1003,
    0x1003,
    0x0003,
    0x0003,
    0x1406,
    0x4106,
    0x2306,
    0x3206,
    0x1305,
    0x1305,
    0x3105,
    0x3105,
    0x0306,
    0x3006,
    0x2205,
    0x2205,
    0x0205,
    0x0205,
    0x4408,
    0x2508,
    0x5208,
    0x1508,
    0x5107,
    0x5107,
    0x3407,
    0x3407,
    0x4307,
    0x4307,
    0x5008,
    0x0408,
    0x2407,
    0x2407,
    0x4207,
    0x4207,
    0x3307,
    0x3307,
    0x4007,
    0x4007,
    0x5509,
    0x4509,
    0x3508,
    0x3508,
    0x5308,
    0x5308,
    0x5409,
    0x0509

};


const uint16 huffTable_10[96] =
{
    0x0001,
    0x1104,
    0x0103,
    0x0103,
    0x1003,
    0x1003,
    0x1206,
    0x2106,
    0x0206,
    0x2006,
    0x1408,
    0x4108,
    0x4008,
    0x2308,
    0x3208,
    0x0308,
    0x1307,
    0x1307,
    0x3107,
    0x3107,
    0x3007,
    0x3007,
    0x2207,
    0x2207,
    0x1608,
    0x1608,
    0x6108,
    0x6108,
    0x6008,
    0x6008,
    0x0509,
    0x5009,
    0x2409,
    0x4209,
    0x3309,
    0x0409,
    0x2709,
    0x2709,
    0x7209,
    0x7209,
    0x640a,
    0x070a,
    0x7009,
    0x7009,
    0x6209,
    0x6209,
    0x450a,
    0x350a,
    0x0609,
    0x0609,
    0x530a,
    0x440a,
    0x1708,
    0x1708,
    0x1708,
    0x1708,
    0x7108,
    0x7108,
    0x7108,
    0x7108,
    0x3609,
    0x3609,
    0x2609,
    0x2609,
    0x250a,
    0x520a,
    0x1509,
    0x1509,
    0x5109,
    0x5109,
    0x340a,
    0x430a,
    0x770b,
    0x670b,
    0x760b,
    0x570b,
    0x750b,
    0x660b,
    0x470a,
    0x470a,
    0x740a,
    0x740a,
    0x560a,
    0x560a,
    0x650a,
    0x650a,
    0x370a,
    0x370a,
    0x730a,
    0x730a,
    0x460a,
    0x460a,
    0x550b,
    0x540b,
    0x630a,
    0x630a
};


const uint16 huffTable_11[116] =
{
    0x1103,
    0x0103,
    0x1003,
    0x0002,
    0x0002,
    0x2105,
    0x1204,     /*  0100         */
    0x1204,     /*  010         */
    0x0205,     /*  01010        */
    0x2005,     /*  01011        */
    0x1408,     /*      10 */
    0x4108,     /*   00      */
    0x0408,     /*   0 0     */
    0x4008,     /*   0 1     */
    0x2307,     /*    0      */
    0x2307,     /*          */
    0x3207,     /*    1      */
    0x3207,     /*          */
    0x1306,     /*   010       */
    0x1306,     /*   01       */
    0x1306,     /*   01       */
    0x1306,     /*   01       */
    0x3106,     /*   011       */
    0x3106,     /*   01      */
    0x3106,     /*   01      */
    0x3106,     /*   01      */
    0x0307,     /*   1000      */
    0x0307,     /*   100      */
    0x3007,     /*   1       */
    0x3007,     /*   100      */
    0x2206,     /*   101       */
    0x2206,     /*   10      */
    0x2206,     /*   10      */
    0x2206,     /*   10      */
    0x2708,
    0x2708,     /*  000 0     */
    0x7208,     /*  000 10     */
    0x7208,     /*  000 1     */
    0x6409,     /*  000 110    */
    0x0709,
    0x7107,
    0x7107,
    0x7107,     /*  00 0      */
    0x7107,     /*  00 0      */
    0x1708,
    0x1708,     /*  00 01     */
    0x7008,
    0x7008,
    0x3608,
    0x3608,     /*  00 10     */
    0x6308,     /*  00 101     */
    0x6308,     /*  00 10     */
    0x6008,
    0x6008,     /*  00 11     */
    0x4409,
    0x2509,
    0x5209,     /*  0      */
    0x0509,     /*  0 00     */
    0x1508,     /*  0 0      */
    0x1508,     /*  0 000     */
    0x6207,     /*  0        */
    0x6207,     /*  0 00     */
    0x6207,     /*  0 00     */
    0x6207,     /*  0 00     */
    0x2608,
    0x2608,     /*  0 010     */
    0x0608,
    0x0608,
    0x1607,
    0x1607,
    0x1607,
    0x1607,
    0x6107,
    0x6107,
    0x6107,
    0x6107,
    0x5108,
    0x5108,
    0x3408,
    0x3408,
    0x5008,
    0x5008,
    0x4309,
    0x3309,
    0x2408,
    0x2408,     /*  0 111     */
    0x4208,     /*  0 1111     */
    0x4208,     /*  0 111     */
    0x560a,
    0x650a,
    0x3709,
    0x3709,
    0x7309,
    0x7309,
    0x4609,
    0x4609,
    0x450a,
    0x540a,     /*  000 0    */
    0x350a,     /*  000  0   */
    0x530a,     /*  000  1   */
    0x770a,
    0x770a,
    0x670a,
    0x670a,
    0x760a,     /*    0   */
    0x760a,     /*       */
    0x750a,     /*    1   */
    0x750a,     /*       */
    0x660a,     /*    00   */
    0x660a,     /*    0   */
    0x470a,     /*    01   */
    0x470a,     /*    0   */
    0x740a,     /*    10   */
    0x740a,     /*    1   */
    0x570b,     /*    110  */
    0x550b  /*    111  */

};

const uint16 huffTable_12[134] =
{

    0x1103,     /*  101          */
    0x0103,     /*  110          */
    0x1003,     /*  111          */
    0x1204,
    0x1204,     /*  011         */
    0x2104,     /*  0111         */
    0x2104,     /*  011         */
    0x0205,     /*  10000        */
    0x2005,     /*  10         */
    0x0004,     /*  1          */
    0x0004,     /*  100         */
    0x3006,
    0x1305,     /*  01         */
    0x1305,     /*  0100        */
    0x3105,
    0x3105,
    0x2205,
    0x2205,     /*  0101        */
    0x1507,
    0x1507,     /*   000      */
    0x5107,     /*   0       */
    0x5107,     /*   000      */
    0x3407,     /*    0      */
    0x3407,     /*          */
    0x4307,     /*    1      */
    0x4307,     /*          */
    0x5008,
    0x0408,
    0x2407,
    0x2407,     /*   010      */
    0x4207,
    0x4207,
    0x1407,     /*   0111      */
    0x1407,     /*   011      */
    0x3306,
    0x3306,
    0x3306,
    0x3306,
    0x4106,
    0x4106,
    0x4106,
    0x4106,
    0x2306,
    0x2306,
    0x2306,
    0x2306,
    0x3206,
    0x3206,
    0x3206,
    0x3206,
    0x4007,
    0x4007,
    0x0307,
    0x0307,     /*  010000      */
    0x7208,
    0x7208,     /*  00 00     */
    0x4608,     /*  00       */
    0x4608,     /*  00 00     */
    0x6408,
    0x6408,     /*  00 01     */
    0x1708,     /*  00 011     */
    0x1708,
    0x7108,     /*  00 100     */
    0x7108,
    0x0709,
    0x7009,
    0x3608,
    0x3608,     /*  00 11     */
    0x6308,
    0x6308,
    0x4508,
    0x4508,
    0x5408,     /*  0 0      */
    0x5408,     /*  0 000     */
    0x4408,     /*  0  0     */
    0x4408,     /*  0       */
    0x0609,     /*  0  10    */
    0x0509,     /*  0  11    */
    0x2607,
    0x2607,
    0x2607,
    0x2607,
    0x6207,
    0x6207,
    0x6207,
    0x6207,
    0x6107,
    0x6107,
    0x6107,
    0x6107,
    0x1608,     /*  0 1010     */
    0x1608,     /*  0 101     */
    0x6008,     /*  0 1011     */
    0x6008,     /*  0 101     */
    0x3508,
    0x3508,     /*  0 110     */
    0x5308,     /*  0 1101     */
    0x5308,     /*  0 110     */
    0x2508,
    0x2508,     /*  0 111     */
    0x5208,     /*  0 1111     */
    0x5208,     /*  0 111     */
    0x770a,
    0x670a,
    0x7609,     /*        */
    0x7609,
    0x5709,     /*    0    */
    0x5709,     /*        */
    0x7509,     /*    1    */
    0x7509,     /*        */
    0x6609,
    0x6609,
    0x4709,     /*  0000 01    */
    0x4709,     /*  0000 0    */
    0x7409,
    0x7409,     /*  0000 1    */
    0x6509,
    0x6509,     /*  0000 1    */
    0x5608,
    0x5608,
    0x5608,
    0x5608,
    0x3708,
    0x3708,
    0x3708,
    0x3708,
    0x7309,     /*  000 100    */
    0x7309,     /*  000 10    */
    0x5509,
    0x5509,     /*  000 10    */
    0x2708,
    0x2708,
    0x2708,
    0x2708,
};



const uint16 huffTable_13[491] =
{
    0x0001,
    0x1104,
    0x0104,
    0x1003,
    0x1003,
    0x4107,
    0x4107,
    0x0408,
    0x4008,
    0x2308,
    0x3208,
    0x1307,
    0x1307,
    0x3107,
    0x3107,
    0x0307,
    0x0307,
    0x3007,
    0x3007,
    0x2207,
    0x2207,
    0x1206,
    0x1206,
    0x1206,
    0x1206,
    0x2106,
    0x2106,
    0x2106,
    0x2106,
    0x0206,
    0x0206,
    0x0206,
    0x0206,
    0x2006,
    0x2006,
    0x2006,
    0x2006,
    0x370a,
    0x270a,     /*  0 000           */
    0x1709,     /*  0 00            */
    0x1709,
    0x7109,
    0x7109,     /*  0 0            */
    0x550a,
    0x070a,     /*  0 0 11          */
    0x700a,
    0x360a,     /*  0             */
    0x630a,
    0x450a,     /*  0  011          */
    0x540a,
    0x260a,     /*  0  101          */
    0x620a,
    0x350a,     /*  0  111          */
    0x8108,
    0x8108,     /*  0 010            */
    0x8108,
    0x8108,     /*  0 010            */
    0x0809,
    0x0809,     /*  0 0101           */
    0x8009,
    0x8009,     /*  0 0101           */
    0x1609,
    0x1609,     /*  0 0110           */
    0x6109,
    0x6109,     /*  0 0110           */
    0x0609,
    0x0609,     /*  0 0111           */
    0x6009,
    0x6009,     /*  0 0111           */
    0x530a,
    0x440a,     /*  0 100           */
    0x2509,
    0x2509,     /*  0 1000           */
    0x5209,
    0x5209,     /*  0 1            */
    0x0509,
    0x0509,     /*  0 1            */
    0x1508,
    0x1508,     /*  0 101            */
    0x1508,
    0x1508,     /*  0 101            */
    0x5108,
    0x5108,     /*  0 101           */
    0x5108,
    0x5108,     /*  0 101           */
    0x3409,
    0x3409,     /*  0 1100           */
    0x4309,
    0x4309,     /*  0 1100           */
    0x5009,
    0x5009,     /*  0 1101           */
    0x2409,
    0x2409,     /*  0 1101           */
    0x4209,
    0x4209,     /*  0 1110           */
    0x3309,
    0x3309,     /*  0 1110           */
    0x1408,
    0x1408,     /*  0 111           */
    0x1408,
    0x1408,     /*  0 111           */
    0x1a0a,
    0x1a0a,
    0xa10a,     /*  00 00           */
    0xa10a,
    0x0a0b,
    0x680b,
    0xa00a,
    0xa00a,
    0x860b,
    0x490b,
    0x930a,
    0x930a,
    0x390b,
    0x580b,
    0x850b,
    0x670b,
    0x290a,
    0x290a,
    0x920a,
    0x920a,
    0x570b,
    0x750b,
    0x380a,
    0x380a,
    0x830a,
    0x830a,
    0x660b,
    0x470b,
    0x740b,
    0x560b,
    0x650b,
    0x730b,
    0x1909,
    0x1909,
    0x1909,
    0x1909,
    0x9109,
    0x9109,
    0x9109,
    0x9109,
    0x090a,     /*  00 10100          */
    0x090a,
    0x900a,     /*  00 10101          */
    0x900a,
    0x480a,     /*  00 10110          */
    0x480a,
    0x840a,     /*  00 10111          */
    0x840a,
    0x720a,     /*  00 11000          */
    0x720a,
    0x460b,     /*  00 11 0         */
    0x640b,
    0x2809,
    0x2809,
    0x2809,
    0x2809,
    0x8209,
    0x8209,
    0x8209,
    0x8209,
    0x1809,
    0x1809,
    0x1809,
    0x1809,
    0xc10b,
    0xc10b,     /*  000 0000         */
    0x980c,
    0x0c0c,     /*  000 00 1        */
    0xc00b,
    0xc00b,     /*  000 0          */
    0xb40c,
    0x6a0c,     /*  000 0 11        */
    0xa60c,
    0x790c,     /*  000           */
    0x3b0b,
    0x3b0b,     /*  000  0         */
    0xb30b,
    0xb30b,     /*  000  1         */
    0x880c,
    0x5a0c,     /*  000  111        */
    0x2b0b,
    0x2b0b,     /*  000 0100         */
    0xa50c,
    0x690c,     /*  000 01 1        */
    0xa40b,
    0xa40b,     /*  000 0101         */
    0x780c,
    0x870c,
    0x940b,
    0x940b,     /*  000 0110         */
    0x770c,
    0x760c,     /*  000 011011        */
    0xb20a,
    0xb20a,     /*  000 011         */
    0xb20a,
    0xb20a,     /*  000 011         */
    0x1b0a,
    0x1b0a,     /*  000 100          */
    0x1b0a,
    0x1b0a,     /*  000 100          */
    0xb10a,
    0xb10a,
    0xb10a,     /*  000 100         */
    0xb10a,     /*  000 100         */
    0x0b0b,     /*  000 10100         */
    0x0b0b,     /*  000 1010         */
    0xb00b,
    0xb00b,     /*  000 1010         */
    0x960b,     /*  000 10110         */
    0x960b,     /*  000 1011         */
    0x4a0b,
    0x4a0b,     /*  000 1011         */
    0x3a0b,     /*  000 11000         */
    0x3a0b,     /*  000 1100         */
    0xa30b,     /*  000 11          */
    0xa30b,     /*  000 1100         */
    0x590b,
    0x590b,     /*  000 1101         */
    0x950b,     /*  000 11011         */
    0x950b,     /*  000 1101         */
    0x2a0a,
    0x2a0a,
    0x2a0a,
    0x2a0a,
    0xa20a,
    0xa20a,
    0xa20a,
    0xa20a,
    0xf00c,
    0xf00c,     /*    000        */
    0xba0d,
    0xe50d,     /*    0 1       */
    0xe40d,
    0x8c0d,     /*     01       */
    0x6d0d,
    0xe30d,     /*     11       */
    0xe20c,     /*    0100        */
    0xe20c,
    0x2e0d,     /*    01010       */
    0x0e0d,
    0x1e0c,     /*    0110        */
    0x1e0c,
    0xe10c,     /*    0111        */
    0xe10c,
    0xe00d,     /*    10000       */
    0x5d0d,
    0xd50d,     /*    1 0       */
    0x7c0d,
    0xc70d,
    0x4d0d,
    0x8b0d,
    0xb80d,
    0xd40d,
    0x9a0d,
    0xa90d,
    0x6c0d,
    0xc60c,
    0xc60c,
    0x3d0c,
    0x3d0c,     /*    111        */
    0xd30d,     /*  0000         */
    0x7b0d,
    0x2d0c,
    0x2d0c,
    0xd20c,
    0xd20c,
    0x1d0c,
    0x1d0c,
    0xb70c,
    0xb70c,     /*  0000  0        */
    0x5c0d,
    0xc50d,     /*  0000  011       */
    0x990d,
    0x7a0d,
    0xc30c,
    0xc30c,     /*  0000  1        */
    0xa70d,
    0x970d,
    0x4b0c,
    0x4b0c,
    0xd10b,
    0xd10b,
    0xd10b,     /*  0000 010        */
    0xd10b,
    0x0d0c,
    0x0d0c,
    0xd00c,
    0xd00c,
    0x8a0c,
    0x8a0c,
    0xa80c,
    0xa80c,
    0x4c0c,
    0x4c0c,
    0xc40c,
    0xc40c,
    0x6b0c,
    0x6b0c,     /*  0000 1         */
    0xb60c,     /*  0000 1 1        */
    0xb60c,     /*  0000 1         */
    0x3c0b,
    0x3c0b,
    0x3c0b,
    0x3c0b,
    0x2c0b,     /*  0000 1011         */
    0x2c0b,     /*  0000 101        */
    0x2c0b,     /*  0000 101        */
    0x2c0b,     /*  0000 101        */
    0xc20b,
    0xc20b,
    0xc20b,
    0xc20b,
    0x5b0b,     /*  0000 1101         */
    0x5b0b,
    0x5b0b,     /*  0000 110        */
    0x5b0b,     /*  0000 110        */
    0xb50c,
    0xb50c,
    0x890c,
    0x890c,     /*  0000 1110        */
    0x1c0b,
    0x1c0b,
    0x1c0b,
    0x1c0b,
    0x2f0d,
    0x2f0d,     /*    000       */
    0xf20d,     /*    0        */
    0xf20d,     /*    000       */
    0x6e0e,     /*     00      */
    0x9c0e,     /*     01      */
    0x0f0d,     /*     1       */
    0x0f0d,     /*            */
    0xc90e,
    0x5e0e,     /*    01       */
    0xab0d,     /*    0101       */
    0xab0d,
    0x7d0e,     /*    01100      */
    0xd70e,
    0x4e0d,     /*    0111       */
    0x4e0d,
    0xc80e,
    0xd60e,     /*    10       */
    0x3e0d,
    0x3e0d,     /*    100       */
    0xb90d,
    0xb90d,     /*    101       */
    0x9b0e,
    0xaa0e,     /*    10111      */
    0x1f0c,
    0x1f0c,     /*    11        */
    0x1f0c,     /*    11        */
    0x1f0c,
    0xf10c,     /*    111        */
    0xf10c,     /*    11       */
    0xf10c,     /*    11       */
    0xf10c,     /*    11       */
    0xe80e,
    0xe80e,
    0x5f0e,
    0x5f0e,
    0x9d0e,
    0x9d0e,
    0xd90e,
    0xd90e,     /*  0000000        */
    0xf50e,
    0xf50e,
    0xe70e,
    0xe70e,
    0xac0e,
    0xac0e,
    0xbb0e,
    0xbb0e,
    0x4f0e,
    0x4f0e,
    0xf40e,     /*  0000000 1       */
    0xf40e,
    0xca0f,
    0xe60f,
    0xf30e,
    0xf30e,     /*  0000000 101      */
    0x3f0d,
    0x3f0d,     /*  0000000 11       */
    0x3f0d,
    0x3f0d,     /*  0000000 11       */
    0x8d0e,
    0x8d0e,
    0xd80e,     /*  0000000 1111      */
    0xd80e,
    0x8f0f,
    0x8f0f,     /*  00000000 000     */
    0xf80f,     /*  00000000 0      */
    0xf80f,
    0xcc0f,     /*  00000000  0     */
    0xcc0f,
    0xae10,
    0x9e10,     /*  00000000  11    */
    0x8e0f,
    0x8e0f,
    0x7f10,
    0x7e10,
    0xf70e,     /*  00000000 011      */
    0xf70e,
    0xf70e,
    0xf70e,     /*  00000000 01     */
    0xda0e,
    0xda0e,     /*  00000000 10      */
    0xda0e,
    0xda0e,     /*  00000000 10      */
    0xad0f,
    0xad0f,     /*  00000000 101     */
    0xbc0f,
    0xbc0f,     /*  00000000 101     */
    0xcb0f,
    0xcb0f,     /*  00000000 110     */
    0xf60f,
    0xf60f,     /*  00000000 110     */
    0x6f0e,
    0x6f0e,     /*  00000000 11     */
    0x6f0e,     /*  00000000 11     */
    0x6f0e,     /*  00000000 11     */
    0xff10,
    0xff10,
    0xef10,
    0xef10,     /*  000000000000     */
    0xdf10,     /*  000000000000 1    */
    0xdf10,     /*  000000000000     */
    0xee10,     /*  00000000000 00    */
    0xee10,     /*  00000000000 0    */
    0xcf10,     /*  00000000000 01    */
    0xcf10,
    0xde10,     /*  00000000000 10    */
    0xde10,
    0xbf10,     /*  00000000000 11    */
    0xbf10,     /*  00000000000 1    */
    0xfb10,
    0xfb10,
    0xce10,
    0xce10,     /*  0000000000 00    */
    0xdc10,     /*  0000000000 010    */
    0xdc10,
    0xaf11,
    0xe911,
    0xec0f,     /*  0000000000 10     */
    0xec0f,     /*  0000000000 1     */
    0xec0f,     /*  0000000000 1     */
    0xec0f,     /*  0000000000 1     */
    0xdd0f,     /*  0000000000 11     */
    0xdd0f,     /*  0000000000 1    */
    0xdd0f,     /*  0000000000 1    */
    0xdd0f,     /*  0000000000 1    */
    0xfa10,     /*  000000000 0000    */
    0xfa10,     /*  000000000 000    */
    0xcd10,     /*  000000000 0     */
    0xcd10,     /*  000000000 000    */
    0xbe0f,     /*  000000000       */
    0xbe0f,
    0xbe0f,
    0xbe0f,
    0xeb0f,
    0xeb0f,
    0xeb0f,
    0xeb0f,     /*  000000000 01     */
    0x9f0f,     /*  000000000 011     */
    0x9f0f,     /*  000000000 01    */
    0x9f0f,
    0x9f0f,
    0xf90f,     /*  000000000 100     */
    0xf90f,     /*  000000000 10     */
    0xf90f,     /*  000000000 10     */
    0xf90f,     /*  000000000 10     */
    0xea0f,     /*  000000000 101     */
    0xea0f,     /*  000000000 10    */
    0xea0f,     /*  000000000 10    */
    0xea0f,     /*  000000000 10    */
    0xbd0f,     /*  000000000 110     */
    0xbd0f,     /*  000000000 11     */
    0xbd0f,     /*  000000000 11     */
    0xbd0f,     /*  000000000 11     */
    0xdb0f,     /*  000000000 111     */
    0xdb0f,     /*  000000000 11    */
    0xdb0f,     /*  000000000 11    */
    0xdb0f,     /*  000000000 11    */
    0xfe13,
    0xfc13,
    0xfd12,
    0xfd12,
    0xed11,
    0xed11,
    0xed11,
    0xed11

};



const uint16 huffTable_15[421] =
{
    0x1103,
    0x1103,
    0x0104,
    0x1004,
    0x0003,     /*  111                 */
    0x0003, /*  11                 */
    0x3407,
    0x4307,
    0x2407,     /*  0101              */
    0x4207,     /*  0101010             */
    0x3307,
    0x4106,     /*  010110              */
    0x4106,
    0x1407,     /*  0101110             */
    0x0407,
    0x2306,     /*  011000              */
    0x2306,
    0x3206,     /*  011               */
    0x3206,
    0x4007,
    0x0307,
    0x1306,     /*  011011              */
    0x1306,     /*  01101              */
    0x3106,     /*  011100              */
    0x3106,     /*  01110              */
    0x3006,     /*  011101              */
    0x3006,     /*  01110              */
    0x2205,     /*  01111               */
    0x2205,     /*  0111              */
    0x2205,     /*  0111              */
    0x2205,     /*  0111              */
    0x1205,     /*  10000               */
    0x1205,     /*  1000               */
    0x1205,     /*  1000               */
    0x1205,     /*  1000               */
    0x2105,     /*  10                */
    0x2105,     /*  1000              */
    0x2105,     /*  1000              */
    0x2105,     /*  1000              */
    0x0205,
    0x0205,     /*  1                */
    0x0205,     /*  1                */
    0x0205,     /*  1                */
    0x2005,     /*  1 1               */
    0x2005,     /*  1               */
    0x2005,     /*  1               */
    0x2005,     /*  1               */
    0x5809,
    0x8509,
    0x2909,     /*               */
    0x6709,     /*   000            */
    0x7609,     /*   00 0           */
    0x9209,     /*   00 1           */
    0x9108,     /*   0 0            */
    0x9108,     /*   0             */
    0x1909,     /*   0 10           */
    0x9009,     /*   0 11           */
    0x4809,     /*    000           */
    0x8409,     /*                */
    0x5709,     /*    010           */
    0x7509,     /*    011           */
    0x3809,     /*    100           */
    0x8309,     /*    101           */
    0x6609,     /*    110           */
    0x4709,     /*    111           */
    0x2808,
    0x2808,     /*   0100            */
    0x8208,     /*   01             */
    0x8208,     /*   0100            */
    0x1808,     /*   01010            */
    0x1808,     /*   0101            */
    0x8108,     /*   01011            */
    0x8108,     /*   0101            */
    0x7409,
    0x0809,     /*   011            */
    0x8009,     /*   011010           */
    0x5609,
    0x6509,     /*   011100           */
    0x3709,
    0x7309,     /*   011110           */
    0x4609,
    0x2708,     /*   10000            */
    0x2708,     /*   1000            */
    0x7208,     /*   10             */
    0x7208,     /*   1000            */
    0x6408,     /*   1 0            */
    0x6408,     /*   1             */
    0x1708,     /*   1 1            */
    0x1708,     /*   1             */
    0x5508,     /*   10100            */
    0x5508,
    0x7108,     /*   10101            */
    0x7108,
    0x0709,     /*   101100           */
    0x7009,     /*   101101           */
    0x3608,     /*   10111            */
    0x3608,     /*   1011            */
    0x6308,     /*   11000            */
    0x6308,     /*   1100            */
    0x4508,     /*   11             */
    0x4508,     /*   1100            */
    0x5408,     /*   11010            */
    0x5408,     /*   1101            */
    0x2608,     /*   11011            */
    0x2608,     /*   1101            */
    0x6208,     /*   11100            */
    0x6208,     /*   1110            */
    0x1608,     /*   11101            */
    0x1608,     /*   1110            */
    0x0609,     /*   111100           */
    0x6009,     /*   111101           */
    0x3508,     /*   11111            */
    0x3508,     /*   1111            */
    0x6107,
    0x6107,
    0x6107,
    0x6107,
    0x5308,     /*  0100 0            */
    0x5308,     /*  0100             */
    0x4408,     /*  0100 1            */
    0x4408,     /*  0100             */
    0x2507,     /*  010 0             */
    0x2507,     /*  010              */
    0x2507,     /*  010              */
    0x2507,     /*  010              */
    0x5207,     /*  010 1             */
    0x5207,     /*  010             */
    0x5207,     /*  010             */
    0x5207,     /*  010             */
    0x1507,     /*  01 00             */
    0x1507,     /*  01 0             */
    0x1507,     /*  01 0             */
    0x1507,     /*  01 0             */
    0x5107,     /*  01 01             */
    0x5107,     /*  01 0            */
    0x5107,     /*  01 0            */
    0x5107,     /*  01 0            */
    0x0508,     /*  01 100            */
    0x0508,     /*  01 10            */
    0x5008,     /*  01 101            */
    0x5008,     /*  01 10            */
    0xc209,
    0xc209,
    0x2c0a,     /*  00 11110          */
    0x5b0a,
    0xb50a,     /*  0            */
    0x1c0a,     /*  0 000           */
    0x890a,     /*  0 00 0          */
    0x980a,
    0xc10a,     /*  0 0 00          */
    0x4b0a,     /*  0 0 01          */
    0xb40a,     /*  0 0 10          */
    0x6a0a,     /*  0 0 11          */
    0x3b0a,     /*  0  000          */
    0x790a,     /*  0             */
    0xb309,     /*  0  01           */
    0xb309,     /*  0  0           */
    0x970a,     /*  0  100          */
    0x880a,     /*  0  101          */
    0x2b0a,     /*  0  110          */
    0x5a0a,     /*  0  111          */
    0xb209,     /*  0 01000           */
    0xb209,     /*  0 0100           */
    0xa50a,     /*  0 01 0          */
    0x1b0a,     /*  0 01 1          */
    0xb109,     /*  0 01010           */
    0xb109,     /*  0 0101           */
    0xb00a,     /*  0 010110          */
    0x690a,     /*  0 010111          */
    0x960a,     /*  0 011000          */
    0x4a0a,     /*  0 011           */
    0xa40a,     /*  0 011010          */
    0x780a,     /*  0 011011          */
    0x870a,     /*  0 011100          */
    0x3a0a,     /*  0 011101          */
    0xa309,     /*  0 01111           */
    0xa309,     /*  0 0111           */
    0x5909,     /*  0 10000           */
    0x5909,     /*  0 1000           */
    0x9509,     /*  0 10            */
    0x9509,     /*  0 1000           */
    0x2a09,     /*  0 1 0           */
    0x2a09,     /*  0 1            */
    0xa209,     /*  0 1 1           */
    0xa209,     /*  0 1            */
    0x1a09,     /*  0 10100           */
    0x1a09,     /*  0 1010           */
    0xa109,     /*  0 10101           */
    0xa109,     /*  0 1010           */
    0x0a0a,     /*  0 101100          */
    0xa00a,     /*  0 101101          */
    0x6809,     /*  0 10111           */
    0x6809,     /*  0 1011           */
    0x8609,     /*  0 11000           */
    0x8609,     /*  0 1100           */
    0x4909,     /*  0 11            */
    0x4909,     /*  0 1100           */
    0x9409,     /*  0 11010           */
    0x9409,     /*  0 1101           */
    0x3909,     /*  0 11011           */
    0x3909,     /*  0 1101           */
    0x9309,     /*  0 11100           */
    0x9309,     /*  0 1110           */
    0x770a,     /*  0 111010          */
    0x090a,     /*  0 111011          */
    0x7c0b,
    0xc70b,     /*  00 000          */
    0x4d0b,     /*  00 00 0         */
    0x8b0b,     /*  00 00 1         */
    0xd40a,     /*  00 0 0          */
    0xd40a,     /*  00 0           */
    0xb80b,     /*  00 0 10         */
    0x9a0b,     /*  00 0 11         */
    0xa90b,     /*  00  000         */
    0x6c0b,     /*  00            */
    0xc60b,     /*  00  010         */
    0x3d0b,     /*  00  011         */
    0xd30a,     /*  00  10          */
    0xd30a,     /*  00  1          */
    0xd20a,     /*  00  11          */
    0xd20a,     /*  00  1          */
    0x2d0b,     /*  00 010000         */
    0x0d0b,     /*  00 010          */
    0x1d0a,     /*  00 01           */
    0x1d0a,     /*  00 0100          */
    0x7b0a,     /*  00 01010          */
    0x7b0a,     /*  00 0101          */
    0xb70a,     /*  00 01011          */
    0xb70a,     /*  00 0101          */
    0xd10a,     /*  00 01100          */
    0xd10a,     /*  00 0110          */
    0x5c0b,     /*  00 011010         */
    0xd00b,     /*  00 011011         */
    0xc50a,     /*  00 01110          */
    0xc50a,     /*  00 0111          */
    0x8a0a,     /*  00 01111          */
    0x8a0a,     /*  00 0111          */
    0xa80a,     /*  00 10000          */
    0xa80a,     /*  00 1000          */
    0x4c0a,     /*  00 10           */
    0x4c0a,     /*  00 1000          */
    0xc40a,     /*  00 1 0          */
    0xc40a,     /*  00 1           */
    0x6b0a,     /*  00 1 1          */
    0x6b0a,     /*  00 1           */
    0xb60a,     /*  00 10100          */
    0xb60a,     /*  00 1010          */
    0x990b,     /*  00 101010         */
    0x0c0b,     /*  00 101011         */
    0x3c0a,     /*  00 10110          */
    0x3c0a,     /*  00 1011          */
    0xc30a,     /*  00 10111          */
    0xc30a,     /*  00 1011          */
    0x7a0a,     /*  00 11000          */
    0x7a0a,     /*  00 1100          */
    0xa70a,     /*  00 11           */
    0xa70a,     /*  00 1100          */
    0xa60a,     /*  00 11010          */
    0xa60a,     /*  00 1101          */
    0xc00b,     /*  00 110110         */
    0x0b0b,     /*  00 110111         */
    0xcb0b,
    0xcb0b,     /*  0000 000         */
    0xf60b,     /*  0000 0          */
    0xf60b,     /*  0000 000         */
    0x8e0c,     /*  0000  00        */
    0xe80c,     /*  0000  01        */
    0x5f0c,     /*  0000  10        */
    0x9d0c,     /*  0000  11        */
    0xf50b,     /*  0000 0100         */
    0xf50b,     /*  0000 010         */
    0x7e0b,     /*  0000 0101         */
    0x7e0b,     /*  0000 010         */
    0xe70b,     /*  0000 0110         */
    0xe70b,     /*  0000 011         */
    0xac0b,     /*  0000 0111         */
    0xac0b,     /*  0000 011         */
    0xca0b,     /*  0000 1000         */
    0xca0b,     /*  0000 100         */
    0xbb0b,     /*  0000 1          */
    0xbb0b,     /*  0000 100         */
    0xd90c,     /*  0000 10100        */
    0x8d0c,     /*  0000 10101        */
    0x4f0b,     /*  0000 1011         */
    0x4f0b,     /*  0000 101         */
    0xf40b,     /*  0000 1100         */
    0xf40b,     /*  0000 110         */
    0x3f0b,     /*  0000 1101         */
    0x3f0b,     /*  0000 110         */
    0xf30b,     /*  0000 1110         */
    0xf30b,     /*  0000 111         */
    0xd80b,     /*  0000 1111         */
    0xd80b,     /*  0000 111         */
    0xe60b,     /*  000           */
    0xe60b,     /*  000 0000         */
    0x2f0b,     /*  000 00          */
    0x2f0b,     /*  000 0000         */
    0xf20b,     /*  000 0 0         */
    0xf20b,     /*  000 0          */
    0x6e0c,     /*  000 0 10        */
    0xf00c,     /*  000 0 11        */
    0x1f0b,     /*  000  00         */
    0x1f0b,     /*  000  0         */
    0xf10b,     /*  000  01         */
    0xf10b,     /*  000  0         */
    0x9c0b,     /*  000  10         */
    0x9c0b,     /*  000  1         */
    0xc90b,     /*  000  11         */
    0xc90b,     /*  000  1         */
    0x5e0b,     /*  000 01000         */
    0x5e0b,     /*  000 0100         */
    0xab0b,     /*  000 01          */
    0xab0b,     /*  000 0100         */
    0xba0b,     /*  000 01010         */
    0xba0b,     /*  000 0101         */
    0xe50b,     /*  000 01011         */
    0xe50b,     /*  000 0101         */
    0x7d0b,     /*  000 01100         */
    0x7d0b,     /*  000 0110         */
    0xd70b,     /*  000 01101         */
    0xd70b,
    0x4e0b,     /*  000 01110         */
    0x4e0b,
    0xe40b,     /*  000 01111         */
    0xe40b,     /*  000 0111         */
    0x8c0b,
    0x8c0b,     /*  000 1000         */
    0xc80b,     /*  000 10          */
    0xc80b,
    0x3e0b,     /*  000 1 0         */
    0x3e0b,     /*  000 1          */
    0x6d0b,
    0x6d0b,     /*  000 1          */
    0xd60b,     /*  000 10100         */
    0xd60b,     /*  000 1010         */
    0xe30b,
    0xe30b,     /*  000 1010         */
    0x9b0b,     /*  000 10110         */
    0x9b0b,     /*  000 1011         */
    0xb90b,
    0xb90b,     /*  000 1011         */
    0x2e0b,     /*  000 11000         */
    0x2e0b,     /*  000 1100         */
    0xaa0b,
    0xaa0b,     /*  000 1100         */
    0xe20b,
    0xe20b,     /*  000 1101         */
    0x1e0b,
    0x1e0b,     /*  000 1101         */
    0xe10b,
    0xe10b,     /*  000 1110         */
    0x0e0c,
    0xe00c,     /*  000 111011        */
    0x5d0b,
    0x5d0b,     /*  000 1111         */
    0xd50b,
    0xd50b,     /*  000 1111         */
    0xff0d,
    0xef0d,
    0xfe0d,
    0xdf0d,
    0xee0c,
    0xee0c,
    0xfd0d,
    0xcf0d,
    0xfc0d,
    0xde0d,
    0xed0d,
    0xbf0d,
    0xfb0c,
    0xfb0c,
    0xce0d,
    0xec0d,
    0xdd0c,     /*    000        */
    0xdd0c,     /*    00        */
    0xaf0c,     /*             */
    0xaf0c,     /*    00        */
    0xfa0c,     /*    010        */
    0xfa0c,     /*    01        */
    0xbe0c,     /*    011        */
    0xbe0c,     /*    01        */
    0xeb0c,     /*    100        */
    0xeb0c,     /*    10        */
    0xcd0c,     /*    101        */
    0xcd0c,     /*    10        */
    0xdc0c,     /*    110        */
    0xdc0c,     /*    11        */
    0x9f0c,     /*    111        */
    0x9f0c,     /*    11        */
    0xf90c,     /*    0000        */
    0xf90c,     /*    000        */
    0xea0c,     /*    0         */
    0xea0c,     /*    000        */
    0xbd0c,     /*     0        */
    0xbd0c,     /*             */
    0xdb0c,     /*     1        */
    0xdb0c,     /*             */
    0x8f0c,     /*    0100        */
    0x8f0c,     /*    010        */
    0xf80c,     /*    0101        */
    0xf80c,     /*    010        */
    0xcc0c,
    0xcc0c,     /*    011        */
    0x9e0c,     /*    0111        */
    0x9e0c,     /*    011        */
    0xe90c,     /*    1000        */
    0xe90c,     /*    100        */
    0x7f0c,     /*    1         */
    0x7f0c,
    0xf70c,     /*    1010        */
    0xf70c,     /*    101        */
    0xad0c,     /*    1011        */
    0xad0c,     /*    101        */
    0xda0c,     /*    1100        */
    0xda0c,     /*    110        */
    0xbc0c,
    0xbc0c,     /*    110        */
    0x6f0c,     /*    1110        */
    0x6f0c,     /*    111        */
    0xae0d,     /*    11110       */
    0x0f0d
};

const uint16 huffTable_16[465] =
{
    0x0001,
    0x1104,
    0x0104,
    0x1003,
    0x1003,
    0x2308,
    0x3208,     /*    11            */
    0x1307,     /*   0100             */
    0x1307,     /*   010             */
    0x3107,
    0x3107,
    0x0308,
    0x3008,
    0x2207,     /*   0111             */
    0x2207,     /*   011             */
    0x1206,     /*   100              */
    0x1206,     /*   10              */
    0x1206,     /*   10              */
    0x1206,     /*   10              */
    0x2106,     /*   101              */
    0x2106,     /*   10             */
    0x2106,     /*   10             */
    0x2106,     /*   10             */
    0x0206,
    0x0206,
    0x0206,
    0x0206,
    0x2006,
    0x2006,
    0x2006,
    0x2006,
    0x1709,
    0x1709,     /*  0 0111           */
    0x7109,     /*  0 10000           */
    0x7109,     /*  0 1000           */
    0x700a,     /*  0 10 0          */
    0x360a,     /*  0 10 1          */
    0x630a,     /*  0 1 00          */
    0x450a,     /*  0 1 01          */
    0x540a,     /*  0 1 10          */
    0x260a,     /*  0 1 11          */
    0x6209,     /*  0 10100           */
    0x6209,     /*  0 1010           */
    0x1609,
    0x1609,
    0x6109,     /*  0 10110           */
    0x6109,     /*  0 1011           */
    0x060a,     /*  0 101110          */
    0x600a,
    0x5309,
    0x5309,     /*  0 1100           */
    0x350a,     /*  0 11 0          */
    0x440a,     /*  0 11 1          */
    0x2509,     /*  0 11010           */
    0x2509,     /*  0 1101           */
    0x5209,     /*  0 11011           */
    0x5209,     /*  0 1101           */
    0x5108,
    0x5108,
    0x5108,
    0x5108,
    0x1509,
    0x1509,     /*  0 1111           */
    0x0509,     /*  0 11111           */
    0x0509,     /*  0 1111           */
    0x3409,     /*               */
    0x3409,     /*               */
    0x4309,     /*   000            */
    0x4309,     /*               */
    0x5009,     /*   00 0           */
    0x5009,     /*   00            */
    0x2409,     /*   00 1           */
    0x2409,     /*   00            */
    0x4209,     /*   0 00           */
    0x4209,     /*   0 0           */
    0x3309,     /*   0 01           */
    0x3309,     /*   0 0           */
    0x1408,     /*   0 1            */
    0x1408,     /*   0            */
    0x1408,     /*   0            */
    0x1408,     /*   0            */
    0x4108,     /*    00            */
    0x4108,     /*    0            */
    0x4108,     /*    0            */
    0x4108,     /*    0            */
    0x0409,     /*    010           */
    0x0409,     /*    01           */
    0x4009,     /*    011           */
    0x4009,     /*    01           */
    0x1d0b,
    0x1d0b,     /*  00 10101         */
    0xc40c,     /*  00 1011000        */
    0x6b0c,     /*  00 1011         */
    0xc30c,     /*  00 1011010        */
    0xa70c,     /*  00 1011011        */
    0x2c0b,     /*  00 101110         */
    0x2c0b,     /*  00 10111         */
    0xc20c,     /*  00 1011110        */
    0xb50c,     /*  00 1011111        */
    0xc10c,     /*  00 1100000        */
    0x0c0c,     /*  00 1100         */
    0x4b0c,     /*  00 110 0        */
    0xb40c,     /*  00 110 1        */
    0x6a0c,     /*  00 11 00        */
    0xa60c,     /*  00 11 01        */
    0xb30b,     /*  00 11 1         */
    0xb30b,     /*  00 11          */
    0x5a0c,     /*  00 1101000        */
    0xa50c,     /*  00 1101         */
    0x2b0b,     /*  00 110101         */
    0x2b0b,     /*  00 11010         */
    0xb20b,     /*  00 110110         */
    0xb20b,     /*  00 11011         */
    0x1b0b,     /*  00 110111         */
    0x1b0b,     /*  00 11011         */
    0xb10b,     /*  00 111000         */
    0xb10b,     /*  00 11100         */
    0x0b0c,     /*  00 111 0        */
    0xb00c,     /*  00 111 1        */
    0x690c,     /*  00 1110100        */
    0x960c,     /*  00 1110101        */
    0x4a0c,     /*  00 1110110        */
    0xa40c,     /*  00 1110111        */
    0x780c,     /*  00 1111000        */
    0x870c,     /*  00 1111         */
    0xa30b,     /*  00 111101         */
    0xa30b,     /*  00 11110         */
    0x3a0c,     /*  00 1111100        */
    0x590c,     /*  00 1111101        */
    0x2a0b,     /*  00 111111         */
    0x2a0b,     /*  00 11111         */
    0x950c,     /*  0 00000000        */
    0x680c,     /*  0           */
    0xa10b,     /*  0 0000          */
    0xa10b,     /*  0           */
    0x860c,     /*  0 000 00        */
    0x770c,     /*  0 000 01        */
    0x940b,     /*  0 000 1         */
    0x940b,     /*  0 000          */
    0x490c,     /*  0 00 000        */
    0x570c,     /*  0 00          */
    0x670b,     /*  0 00 01         */
    0x670b,     /*  0 00 0         */
    0xa20a,     /*  0 00 1          */
    0xa20a,     /*  0 00          */
    0xa20a,     /*  0 00          */
    0xa20a,     /*  0 00          */
    0x1a0a,     /*  0 0 00          */
    0x1a0a,     /*  0 0 0          */
    0x1a0a,     /*  0 0 0          */
    0x1a0a,     /*  0 0 0          */
    0x0a0b,     /*  0 0 010         */
    0x0a0b,     /*  0 0 01         */
    0xa00b,     /*  0 0 011         */
    0xa00b,     /*  0 0 01         */
    0x390b,     /*  0 0 100         */
    0x390b,     /*  0 0 10         */
    0x930b,     /*  0 0 101         */
    0x930b,     /*  0 0 10         */
    0x580b,     /*  0 0 110         */
    0x580b,     /*  0 0 11         */
    0x850b,     /*  0 0 111         */
    0x850b,     /*  0 0 11         */
    0x290a,     /*  0  000          */
    0x290a,     /*  0  00          */
    0x290a,     /*  0  00          */
    0x290a,     /*  0  00          */
    0x920a,     /*  0             */
    0x920a,     /*  0  00         */
    0x920a,     /*  0  00         */
    0x920a,     /*  0  00         */
    0x760b,     /*  0  0100         */
    0x760b,     /*  0  010         */
    0x090b,     /*  0  0101         */
    0x090b,     /*  0  010         */
    0x190a,     /*  0  011          */
    0x190a,     /*  0  01         */
    0x190a,     /*  0  01         */
    0x190a,     /*  0  01         */
    0x910a,     /*  0  100          */
    0x910a,     /*  0  10          */
    0x910a,     /*  0  10          */
    0x910a,     /*  0  10          */
    0x900b,     /*  0  1010         */
    0x900b,     /*  0  101         */
    0x480b,     /*  0  1011         */
    0x480b,     /*  0  101         */
    0x840b,     /*  0  1100         */
    0x840b,     /*  0  110         */
    0x750b,     /*  0  1101         */
    0x750b,     /*  0  110         */
    0x380b,     /*  0  1110         */
    0x380b,     /*  0  111         */
    0x830b,     /*  0  1111         */
    0x830b,     /*  0  111         */
    0x660b,     /*  0 0100000         */
    0x660b,     /*  0 010000         */
    0x280b,     /*  0 0100          */
    0x280b,     /*  0 010000         */
    0x820a,     /*  0 010           */
    0x820a,     /*  0 01000         */
    0x820a,     /*  0 01000         */
    0x820a,     /*  0 01000         */
    0x470b,     /*  0 01 00         */
    0x470b,     /*  0 01 0         */
    0x740b,     /*  0 01 01         */
    0x740b,     /*  0 01 0         */
    0x180a,     /*  0 01 1          */
    0x180a,     /*  0 01          */
    0x180a,     /*  0 01          */
    0x180a,     /*  0 01          */
    0x810a,     /*  0 010100          */
    0x810a,     /*  0 01010          */
    0x810a,     /*  0 01010          */
    0x810a,     /*  0 01010          */
    0x800a,     /*  0 010101          */
    0x800a,     /*  0 01010         */
    0x800a,     /*  0 01010         */
    0x800a,     /*  0 01010         */
    0x080b,     /*  0 0101100         */
    0x080b,     /*  0 010110         */
    0x560b,     /*  0 0101101         */
    0x560b,     /*  0 010110         */
    0x370a,     /*  0 010111          */
    0x370a,     /*  0 01011         */
    0x370a,     /*  0 01011         */
    0x370a,     /*  0 01011         */
    0x730a,     /*  0 011000          */
    0x730a,     /*  0 01100          */
    0x730a,     /*  0 01100          */
    0x730a,     /*  0 01100          */
    0x650b,     /*  0 011 0         */
    0x650b,     /*  0 011          */
    0x460b,     /*  0 011 1         */
    0x460b,     /*  0 011          */
    0x270a,     /*  0 011010          */
    0x270a,     /*  0 01101          */
    0x270a,     /*  0 01101          */
    0x270a,     /*  0 01101          */
    0x720a,     /*  0 011011          */
    0x720a,     /*  0 01101         */
    0x720a,     /*  0 01101         */
    0x720a,     /*  0 01101         */
    0x640b,     /*  0 0111000         */
    0x640b,     /*  0 011100         */
    0x550b,     /*  0 0111          */
    0x550b,     /*  0 011100         */
    0x070a,     /*  0 011101          */
    0x070a,     /*  0 01110         */
    0x070a,     /*  0 01110         */
    0x070a,     /*  0 01110         */
    0x9e0d,
    0x9e0d,     /*  00 0110000       */
    0xbc0e,     /*  00 01100 0      */
    0xcb0e,     /*  00 01100 1      */
    0x8e0e,     /*  00 0110 00      */
    0xe80e,     /*  00 0110 01      */
    0x9d0e,     /*  00 0110 10      */
    0xe70e,     /*  00 0110 11      */
    0xbb0e,     /*  00 011 000      */
    0x8d0e,     /*  00 011        */
    0xd80e,     /*  00 011 010      */
    0x6e0e,     /*  00 011 011      */
    0xe60d,     /*  00 011 10       */
    0xe60d,     /*  00 011 1       */
    0x9c0d,     /*  00 011 11       */
    0x9c0d,     /*  00 011 1       */
    0xab0e,     /*  00 011010000      */
    0xba0e,     /*  00 011010       */
    0xe50e,     /*  00 01101 0      */
    0xd70e,     /*  00 01101 1      */
    0x4e0d,     /*  00 01101010       */
    0x4e0d,     /*  00 0110101       */
    0xe40e,     /*  00 011010110      */
    0x8c0e,     /*  00 011010111      */
    0xc80d,     /*  00 01101100       */
    0xc80d,     /*  00 0110110       */
    0x3e0d,     /*  00 01101101       */
    0x3e0d,     /*  00 0110110       */
    0x6d0d,     /*  00 01101110       */
    0x6d0d,     /*  00 0110111       */
    0xd60e,     /*  00 011011110      */
    0x9b0e,     /*  00 011011111      */
    0xb90e,     /*  00 011100000      */
    0xaa0e,     /*  00 011100       */
    0xe10d,     /*  00 01110        */
    0xe10d,     /*  00 0111000       */
    0xd40d,     /*  00 0111 0       */
    0xd40d,     /*  00 0111        */
    0xb80e,     /*  00 0111 10      */
    0xa90e,     /*  00 0111 11      */
    0x7b0d,     /*  00 01110100       */
    0x7b0d,     /*  00 0111010       */
    0xb70e,     /*  00 011101010      */
    0xd00e,     /*  00 011101011      */
    0xe30c,     /*  00 0111011        */
    0xe30c,     /*  00 011101       */
    0xe30c,     /*  00 011101       */
    0xe30c,     /*  00 011101       */
    0x0e0d,     /*  00 01111000       */
    0x0e0d,     /*  00 0111100       */
    0xe00d,     /*  00 01111        */
    0xe00d,     /*  00 0111100       */
    0x5d0d,     /*  00 01111010       */
    0x5d0d,     /*  00 0111101       */
    0xd50d,     /*  00 01111011       */
    0xd50d,     /*  00 0111101       */
    0x7c0d,     /*  00 01111100       */
    0x7c0d,     /*  00 0111110       */
    0xc70d,     /*  00 01111101       */
    0xc70d,     /*  00 0111110       */
    0x4d0d,     /*  00 01111110       */
    0x4d0d,     /*  00 0111111       */
    0x8b0d,     /*  00 01111111       */
    0x8b0d,     /*  00 0111111       */
    0x9a0d,
    0x6c0d,     /*  00 10000        */
    0xc60d,     /*  00 1000 0       */
    0x3d0d,     /*  00 1000 1       */
    0x5c0d,     /*  00 100 00       */
    0xc50d,     /*  00 100 01       */
    0x0d0c,     /*  00 100 1        */
    0x0d0c,     /*  00 100         */
    0x8a0d,     /*  00 10 000       */
    0xa80d,     /*  00 10         */
    0x990d,     /*  00 10 010       */
    0x4c0d,     /*  00 10 011       */
    0xb60d,     /*  00 10 100       */
    0x7a0d,     /*  00 10 101       */
    0x3c0c,     /*  00 10 11        */
    0x3c0c,     /*  00 10 1        */
    0x5b0d,     /*  00 1 0000       */
    0x890d,     /*  00 1 0        */
    0x1c0c,     /*  00 1          */
    0x1c0c,     /*  00 1 00        */
    0xc00c,     /*  00 1 010        */
    0xc00c,     /*  00 1 01        */
    0x980d,     /*  00 1 0110       */
    0x790d,     /*  00 1 0111       */
    0xe20b,     /*  00 1 10         */
    0xe20b,     /*  00 1 1         */
    0xe20b,     /*  00 1 1         */
    0xe20b,     /*  00 1 1         */
    0x2e0c,     /*  00 1 110        */
    0x2e0c,     /*  00 1 11        */
    0x1e0c,     /*  00 1 111        */
    0x1e0c,     /*  00 1 11        */
    0xd30c,     /*  00 1010000        */
    0xd30c,     /*  00 101000        */
    0x2d0c,     /*  00 1010         */
    0x2d0c,     /*  00 101000        */
    0xd20c,     /*  00 101 0        */
    0xd20c,     /*  00 101         */
    0xd10c,     /*  00 101 1        */
    0xd10c,     /*  00 101         */
    0x3b0c,     /*  00 1010100        */
    0x3b0c,     /*  00 101010        */
    0x970d,     /*  00 10101010       */
    0x880d,     /*  00 10101011       */
    0xf208,
    0xf208,     /*  000 1            */
    0x2f09,     /*  00 0000           */
    0x0f09,     /*  00 0            */
    0x1f08,     /*  00              */
    0x1f08,     /*  00 00            */
    0xf108,     /*  00 010            */
    0xf108,     /*  00 01            */
    0xce10,
    0xce10,     /*  000 101100000    */
    0xec11,     /*  000 1011000 0   */
    0xdd11,     /*  000 1011000 1   */
    0xde0f,     /*  000 101100      */
    0xde0f,     /*  000 10110000    */
    0xde0f,     /*  000 10110000    */
    0xde0f,     /*  000 10110000    */
    0xe90f,     /*  000 10110 0     */
    0xe90f,     /*  000 10110      */
    0xe90f,     /*  000 10110      */
    0xe90f,     /*  000 10110      */
    0xea10,     /*  000 10110 10    */
    0xea10,     /*  000 10110 1    */
    0xd910,     /*  000 10110 11    */
    0xd910,     /*  000 10110 1    */
    0xee0e,
    0xee0e,     /*  000 1011       */
    0xed0f,     /*  000 1011 10     */
    0xeb0f,     /*  000 1011 11     */
    0xbe0e,     /*  000 10110100      */
    0xbe0e,     /*  000 1011010      */
    0xcd0e,     /*  000 10110101      */
    0xcd0e,     /*  000 1011010      */
    0xdc0f,     /*  000 101101100     */
    0xdb0f,     /*  000 101101101     */
    0xae0e,     /*  000 10110111      */
    0xae0e,     /*  000 1011011      */
    0xcc0e,     /*  000 10111000      */
    0xcc0e,     /*  000 1011100      */
    0xad0f,     /*  000 10111 0     */
    0xda0f,     /*  000 10111 1     */
    0x7e0f,     /*  000 101110100     */
    0xac0f,     /*  000 101110101     */
    0xca0e,     /*  000 10111011      */
    0xca0e,     /*  000 1011101      */
    0xc90f,     /*  000 101111000     */
    0x7d0f,     /*  000 101111      */
    0x5e0e,     /*  000 10111101      */
    0x5e0e,     /*  000 1011110      */
    0xbd0d,     /*  000 1011111       */
    0xbd0d,     /*  000 101111      */
    0xbd0d,     /*  000 101111      */
    0xbd0d,     /*  000 101111      */
    0xef0b,
    0xfe0b,     /*  00000000          */
    0xdf0b,     /*  0000000 0         */
    0xfd0b,     /*  0000000 1         */
    0xcf0b,     /*    00         */
    0xfc0b,     /*    01         */
    0xbf0b,     /*    10         */
    0xfb0b,     /*    11         */
    0xaf0a,     /*    00          */
    0xaf0a,     /*    0          */
    0xfa0b,     /*    010         */
    0x9f0b,     /*    011         */
    0xf90b,     /*    100         */
    0xf80b,     /*    101         */
    0x8f0a,     /*    11          */
    0x8f0a,     /*    1          */
    0x7f0a,     /*  0000 000          */
    0x7f0a,     /*  0000 00          */
    0xf70a,     /*  0000            */
    0xf70a,     /*  0000 00          */
    0x6f0a,     /*  0000 010          */
    0x6f0a,     /*  0000 01          */
    0xf60a,     /*  0000 011          */
    0xf60a,     /*  0000 01          */
    0xff08,     /*  0000 1            */
    0xff08,     /*  0000            */
    0xff08,     /*  0000            */
    0xff08,     /*  0000            */
    0xff08,     /*  0000            */
    0xff08,     /*  0000            */
    0xff08,     /*  0000            */
    0xff08,     /*  0000            */
    0x5f0a,     /*  000 0000          */
    0x5f0a,     /*  000 000          */
    0xf50a,     /*  000 0           */
    0xf50a,     /*  000 000          */
    0x4f09,     /*  000             */
    0x4f09,     /*  000 00          */
    0x4f09,     /*  000 00          */
    0x4f09,     /*  000 00          */
    0xf409,     /*  000 010           */
    0xf409,     /*  000 01           */
    0xf409,     /*  000 01           */
    0xf409,     /*  000 01           */
    0xf309,     /*  000 011           */
    0xf309,     /*  000 01          */
    0xf309,     /*  000 01          */
    0xf309,     /*  000 01          */
    0xf009,     /*  000 100           */
    0xf009,     /*  000 10           */
    0xf009,     /*  000 10           */
    0xf009,     /*  000 10           */
    0x3f0a,
    0x3f0a
};



const uint16 huffTable_24[478] =
{

    0x2206,     /*  101               */
    0x1205,     /*  10101               */
    0x1205,     /*  1010               */
    0x2105,     /*  10110               */
    0x2105,     /*  1011               */
    0x0206,     /*  101110              */
    0x2006,     /*  101111              */
    0x1104,
    0x1104,
    0x1104,
    0x1104,
    0x0104,
    0x0104,
    0x0104,
    0x0104,
    0x1004,
    0x1004,
    0x1004,
    0x1004,
    0x0004,
    0x0004,
    0x0004,
    0x0004,
    0x7308,
    0x7308,
    0x3709,
    0x2709,
    0x7208,
    0x7208,
    0x4608,     /*  01110000            */
    0x4608,     /*  0111000            */
    0x6408,     /*  01110             */
    0x6408,     /*  0111000            */
    0x5508,     /*  0111 0            */
    0x5508,     /*  0111             */
    0x7108,     /*  0111 1            */
    0x7108,     /*  0111             */
    0x3608,     /*  01110100            */
    0x3608,     /*  0111010            */
    0x6308,     /*  01110101            */
    0x6308,     /*  0111010            */
    0x4508,     /*  01110110            */
    0x4508,     /*  0111011            */
    0x5408,     /*  01110111            */
    0x5408,     /*  0111011            */
    0x2608,     /*  01111000            */
    0x2608,     /*  0111100            */
    0x6208,     /*  01111             */
    0x6208,     /*  0111100            */
    0x1608,     /*  01111010            */
    0x1608,     /*  0111101            */
    0x6108,     /*  01111011            */
    0x6108,     /*  0111101            */
    0x0609,     /*  011111000           */
    0x6009,     /*  011111            */
    0x3508,     /*  01111101            */
    0x3508,     /*  0111110            */
    0x5308,     /*  01111110            */
    0x5308,     /*  0111111            */
    0x4408,     /*  01111111            */
    0x4408,     /*  0111111            */
    0x2508,     /*  10000000            */
    0x2508,     /*  1000000            */
    0x5208,     /*  10000             */
    0x5208,     /*  1000000            */
    0x1508,     /*  1000 0            */
    0x1508,     /*  1000             */
    0x0509,     /*  1000 10           */
    0x5009,     /*  1000 11           */
    0x5107,     /*  100 0             */
    0x5107,     /*  100              */
    0x5107,     /*  100              */
    0x5107,     /*  100              */
    0x3408,     /*  100 10            */
    0x3408,     /*  100 1            */
    0x4308,     /*  100 11            */
    0x4308,     /*  100 1            */
    0x2407,     /*  10 00             */
    0x2407,     /*  10 0             */
    0x2407,     /*  10 0             */
    0x2407,     /*  10 0             */
    0x4207,     /*  10 01             */
    0x4207,     /*  10 0            */
    0x4207,     /*  10 0            */
    0x4207,     /*  10 0            */
    0x3307,     /*  10 10             */
    0x3307,     /*  10 1             */
    0x3307,     /*  10 1             */
    0x3307,     /*  10 1             */
    0x1407,     /*  10 11             */
    0x1407,     /*  10 1            */
    0x1407,     /*  10 1            */
    0x1407,     /*  10 1            */
    0x4107,     /*  1 000             */
    0x4107,     /*  1 00             */
    0x4107,     /*  1 00             */
    0x4107,     /*  1 00             */
    0x0408,     /*  1  0            */
    0x0408,     /*  1              */
    0x4008,     /*  1  1            */
    0x4008,     /*  1              */
    0x2307,     /*  1 010             */
    0x2307,     /*  1 01             */
    0x2307,     /*  1 01             */
    0x2307,     /*  1 01             */
    0x3207,     /*  1 011             */
    0x3207,     /*  1 01            */
    0x3207,     /*  1 01            */
    0x3207,     /*  1 01            */
    0x1306,     /*  1 10              */
    0x1306,     /*  1 1              */
    0x1306,     /*  1 1              */
    0x1306,     /*  1 1              */
    0x1306,     /*  1 1              */
    0x1306,     /*  1 1              */
    0x1306,     /*  1 1              */
    0x1306,     /*  1 1              */
    0x3106,     /*  1 11              */
    0x3106,     /*  1 1             */
    0x3106,     /*  1 1             */
    0x3106,     /*  1 1             */
    0x3106,     /*  1 1             */
    0x3106,     /*  1 1             */
    0x3106,     /*  1 1             */
    0x3106,     /*  1 1             */
    0x0307,     /*  1010000             */
    0x0307,     /*  101000             */
    0x0307,     /*  101000             */
    0x0307,     /*  101000             */
    0x3007,     /*  1010              */
    0x3007,     /*  101000            */
    0x3007,     /*  101000            */
    0x3007,     /*  101000            */
    0xb309,
    0xb309,
    0x8809,     /*  010101            */
    0x8809,     /*  01010100           */
    0x2b0a,     /*  0101010100          */
    0x5a0a,     /*  0101010101          */
    0xb209,     /*  010101011           */
    0xb209,     /*  01010101           */
    0xa50a,     /*  0101011000          */
    0x1b0a,     /*  0101011           */
    0xb10a,     /*  0101011010          */
    0x690a,     /*  0101011011          */
    0x9609,     /*  010101110           */
    0x9609,     /*  01010111           */
    0xa409,     /*  010101111           */
    0xa409,     /*  01010111           */
    0x4a0a,     /*  0101100000          */
    0x780a,     /*  0101100           */
    0x8709,     /*  010110            */
    0x8709,     /*  01011000           */
    0x3a09,     /*  01011 0           */
    0x3a09,     /*  01011            */
    0xa309,     /*  01011 1           */
    0xa309,     /*  01011            */
    0x5909,     /*  010110100           */
    0x5909,     /*  01011010           */
    0x9509,     /*  010110101           */
    0x9509,     /*  01011010           */
    0x2a09,     /*  010110110           */
    0x2a09,     /*  01011011           */
    0xa209,     /*  010110111           */
    0xa209,     /*  01011011           */
    0xa109,     /*  010111000           */
    0xa109,     /*  01011100           */
    0x6809,     /*  010111            */
    0x6809,     /*  01011100           */
    0x8609,     /*  010111010           */
    0x8609,     /*  01011101           */
    0x7709,     /*  010111011           */
    0x7709,     /*  01011101           */
    0x4909,     /*  010111100           */
    0x4909,     /*  01011110           */
    0x9409,     /*  010111101           */
    0x9409,     /*  01011110           */
    0x3909,     /*  010111110           */
    0x3909,     /*  01011111           */
    0x9309,     /*  010111111           */
    0x9309,     /*  01011111           */
    0x5809,     /*  011000000           */
    0x5809,     /*  01100000           */
    0x8509,     /*  011000            */
    0x8509,     /*  01100000           */
    0x2909,     /*  01100 0           */
    0x2909,     /*  01100            */
    0x6709,     /*  01100 1           */
    0x6709,     /*  01100            */
    0x7609,     /*  0110 00           */
    0x7609,     /*  0110 0           */
    0x9209,     /*  0110 01           */
    0x9209,     /*  0110 0           */
    0x1909,     /*  0110 10           */
    0x1909,     /*  0110 1           */
    0x9109,     /*  0110 11           */
    0x9109,     /*  0110 1           */
    0x4809,     /*  011 000           */
    0x4809,     /*  011 00           */
    0x8409,     /*  011             */
    0x8409,     /*  011 00           */
    0x5709,     /*  011 010           */
    0x5709,     /*  011 01           */
    0x7509,     /*  011 011           */
    0x7509,     /*  011 01           */
    0x3809,     /*  011 100           */
    0x3809,     /*  011 10           */
    0x8309,     /*  011 101           */
    0x8309,     /*  011 10           */
    0x6609,     /*  011 110           */
    0x6609,     /*  011 11           */
    0x2809,     /*  011 111           */
    0x2809,     /*  011 11           */
    0x8209,     /*  011010000           */
    0x8209,     /*  01101000           */
    0x1809,     /*  011010            */
    0x1809,     /*  01101000           */
    0x4709,     /*  01101 0           */
    0x4709,     /*  01101            */
    0x7409,     /*  01101 1           */
    0x7409,     /*  01101            */
    0x8109,     /*  011010100           */
    0x8109,     /*  01101010           */
    0x080a,     /*  0110101010          */
    0x800a,     /*  0110101011          */
    0x5609,     /*  011010110           */
    0x5609,     /*  01101011           */
    0x6509,     /*  011010111           */
    0x6509,     /*  01101011           */
    0x1709,     /*  011011000           */
    0x1709,     /*  01101100           */
    0x070a,     /*  011011 0          */
    0x700a,     /*  011011 1          */
    0x6e0b,
    0x9c0b,
    0xc90a,     /*  01000 01          */
    0xc90a,     /*  01000 0          */
    0x5e0a,     /*  01000 10          */
    0x5e0a,     /*  01000 1          */
    0xba0a,     /*  01000 11          */
    0xba0a,     /*  01000 1          */
    0xe50a,     /*  0100 000          */
    0xe50a,     /*  0100 00          */
    0xab0b,     /*  0100  0         */
    0x7d0b,     /*  0100  1         */
    0xd70a,     /*  0100 010          */
    0xd70a,     /*  0100 01          */
    0xe40a,     /*  0100 011          */
    0xe40a,     /*  0100 01          */
    0x8c0a,     /*  0100 100          */
    0x8c0a,
    0xc80a,
    0xc80a,
    0x4e0b,     /*  0100 1100         */
    0x2e0b,     /*  0100 1101         */
    0x3e0a,     /*  0100 111          */
    0x3e0a,     /*  0100 11          */
    0x6d0a,     /*  010 0000          */
    0x6d0a,     /*  010 000          */
    0xd60a,     /*  010 0           */
    0xd60a,     /*  010 000          */
    0xe30a,     /*  010  0          */
    0xe30a,     /*  010            */
    0x9b0a,     /*  010  1          */
    0x9b0a,     /*  010            */
    0xb90a,     /*  010 0100          */
    0xb90a,     /*  010 010          */
    0xaa0a,     /*  010 0101          */
    0xaa0a,
    0xe20a,
    0xe20a,
    0x1e0a,
    0x1e0a,
    0xe10a,
    0xe10a,
    0x5d0a,
    0x5d0a,
    0xd50a,
    0xd50a,
    0x7c0a,
    0x7c0a,
    0xc70a,
    0xc70a,
    0x4d0a,
    0x4d0a,
    0x8b0a,
    0x8b0a,
    0xb80a,
    0xb80a,
    0xd40a,
    0xd40a,
    0x9a0a,
    0x9a0a,
    0xa90a,     /*  01 0 0          */
    0xa90a,     /*  01 0           */
    0x6c0a,     /*  01 0 1          */
    0x6c0a,     /*  01 0           */
    0xc60a,     /*  01  00          */
    0xc60a,     /*  01  0          */
    0x3d0a,     /*  01  01          */
    0x3d0a,     /*  01  0          */
    0xd30a,     /*  01  10          */
    0xd30a,     /*  01  1          */
    0x2d0a,     /*  01  11          */
    0x2d0a,     /*  01  1          */
    0xd20a,
    0xd20a,
    0x1d0a,     /*  01 01           */
    0x1d0a,     /*  01 0100          */
    0x7b0a,     /*  01 01010          */
    0x7b0a,     /*  01 0101          */
    0xb70a,     /*  01 01011          */
    0xb70a,
    0xd10a,
    0xd10a,     /*  01 0110          */
    0x5c0a,     /*  01 01101          */
    0x5c0a,     /*  01 0110          */
    0xc50a,     /*  01 01110          */
    0xc50a,     /*  01 0111          */
    0x8a0a,     /*  01 01111          */
    0x8a0a,     /*  01 0111          */
    0xa80a,     /*  01 10000          */
    0xa80a,     /*  01 1000          */
    0x990a,     /*  01 10           */
    0x990a,     /*  01 1000          */
    0x4c0a,     /*  01 1 0          */
    0x4c0a,     /*  01 1           */
    0xc40a,     /*  01 1 1          */
    0xc40a,     /*  01 1           */
    0x6b0a,     /*  01 10100          */
    0x6b0a,     /*  01 1010          */
    0xb60a,     /*  01 10101          */
    0xb60a,     /*  01 1010          */
    0xd00b,     /*  01 101100         */
    0x0c0b,     /*  01 101101         */
    0x3c0a,     /*  01 10111          */
    0x3c0a,     /*  01 1011          */
    0xc30a,     /*  01 11000          */
    0xc30a,     /*  01 1100          */
    0x7a0a,     /*  01 11           */
    0x7a0a,     /*  01 1100          */
    0xa70a,     /*  01 11010          */
    0xa70a,     /*  01 1101          */
    0x2c0a,     /*  01 11011          */
    0x2c0a,     /*  01 1101          */
    0xc20a,     /*  01 11100          */
    0xc20a,     /*  01 1110          */
    0x5b0a,     /*  01 11101          */
    0x5b0a,     /*  01 1110          */
    0xb50a,     /*  01 11110          */
    0xb50a,     /*  01 1111          */
    0x1c0a,
    0x1c0a,
    0x890a,
    0x890a,
    0x980a,
    0x980a,
    0xc10a,     /*  010100 0          */
    0xc10a,     /*  010100           */
    0x4b0a,     /*  010100 1          */
    0x4b0a,     /*  010100           */
    0xc00b,     /*  01010 000         */
    0x0b0b,     /*  01010           */
    0x3b0a,     /*  01010 01          */
    0x3b0a,     /*  01010 0          */
    0xb00b,     /*  01010 100         */
    0x0a0b,     /*  01010 101         */
    0x1a0a,     /*  01010 11          */
    0x1a0a,     /*  01010 1          */
    0xb409,     /*  0101 00           */
    0xb409,     /*  0101 0           */
    0xb409,     /*  0101 0           */
    0xb409,     /*  0101 0           */
    0x6a0a,     /*  0101 010          */
    0x6a0a,     /*  0101 01          */
    0xa60a,     /*  0101 011          */
    0xa60a,     /*  0101 01          */
    0x790a,     /*  0101 100          */
    0x790a,     /*  0101 10          */
    0x970a,     /*  0101 101          */
    0x970a,     /*  0101 10          */
    0xa00b,     /*  0101 1100         */
    0x090b,     /*  0101 1101         */
    0x900a,     /*  0101 111          */
    0x900a,     /*  0101 11          */
    0xca0b,
    0xca0b,
    0xbb0b,
    0xbb0b,
    0x8d0b,
    0x8d0b,     /*  0100000          */
    0xd80b,     /*  0100000 1         */
    0xd80b,     /*  0100000          */
    0x0e0c,     /*  010000 000        */
    0xe00c,     /*  010000          */
    0x0d0b,     /*  010000 01         */
    0x0d0b,     /*  010000 0         */
    0xe60a,     /*  010000 1          */
    0xe60a,     /*  010000          */
    0xe60a,     /*  010000          */
    0xe60a,     /*  010000          */
    0x0f09,     /*   011000      401  */
    0x0f09,     /*   01100           */
    0x0f09,     /*   01100           */
    0x0f09,     /*   01100           */
    0xee0b,     /*   011 00         */
    0xde0b,     /*   011 01         */
    0xed0b,     /*   011 10         */
    0xce0b,     /*   011 11         */
    0xec0b,     /*   01101000         */
    0xdd0b,     /*   01101          */
    0xbe0b,     /*   01101010         */
    0xeb0b,     /*   01101011         */
    0xcd0b,     /*   01101100         */
    0xdc0b,     /*   01101101         */
    0xae0b,     /*   01101110         */
    0xea0b,     /*   01101111         */
    0xbd0b,     /*   01110000         */
    0xdb0b,     /*   01110          */
    0xcc0b,     /*   0111 0         */
    0x9e0b,     /*   0111 1         */
    0xe90b,     /*   01110100         */
    0xad0b,     /*   01110101         */
    0xda0b,     /*   01110110         */
    0xbc0b,     /*   01110111         */
    0xcb0b,     /*   01111000         */
    0x8e0b,
    0xe80b,
    0x9d0b,
    0xd90b,
    0x7e0b,
    0xe70b,
    0xac0b,
    0xff04,
    0xef08,
    0xfe08,
    0xdf08,     /*  0000 0            */
    0xfd08,     /*  0000 1            */
    0xcf08,     /*  000 00            */
    0xfc08,     /*  000 01            */
    0xbf08,     /*  000 10            */
    0xfb08,     /*  000 11            */
    0xfa07,     /*  00 00             */
    0xfa07,     /*  00 0             */
    0xaf08,     /*  00 010            */
    0x9f08,     /*  00 011            */
    0xf907,     /*  00 10             */
    0xf907,     /*  00 1             */
    0xf807,     /*  00 11             */
    0xf807,     /*  00 1             */
    0x8f08,
    0x7f08,     /*  0 0             */
    0xf707,     /*  0               */
    0xf707,     /*  0 00             */
    0x6f07,     /*  0 010             */
    0x6f07,     /*  0 01             */
    0xf607,     /*  0 011             */
    0xf607,     /*  0 01             */
    0x5f07,
    0x5f07,     /*  0 10             */
    0xf507,     /*  0 101             */
    0xf507,     /*  0 10             */
    0x4f07,     /*  0 110             */
    0x4f07,     /*  0 11             */
    0xf407,     /*  0 111             */
    0xf407,     /*  0 11             */
    0x3f07,
    0x3f07,
    0xf307,     /*   0              */
    0xf307,
    0x2f07,     /*    0             */
    0x2f07,     /*                 */
    0xf207,     /*    1             */
    0xf207,     /*                 */
    0xf107,
    0xf107,
    0x1f08,
    0xf008

};


const uint16 huffTable_32[33] =
{

    0x0b06,
    0x0f06,
    0x0d06,
    0x0e06,
    0x0706,
    0x0506,
    0x0905,
    0x0905,
    0x0605,
    0x0605,
    0x0305,
    0x0305,
    0x0a05,
    0x0a05,
    0x0c05,
    0x0c05,
    0x0204,
    0x0204,
    0x0204,
    0x0204,
    0x0104,
    0x0104,
    0x0104,
    0x0104,
    0x0404,
    0x0404,
    0x0404,
    0x0404,
    0x0804,
    0x0804,
    0x0804,
    0x0804,
    0x0001

};


/*
 *  MM = 512; z = [0:(MM)]; a = z.^(1/3);
 *  Table is in Q27
 */
const int32  power_one_third[513] =
{

    0x00000000,  0x08000000,  0x0A14517D,  0x0B89BA25,
    0x0CB2FF53,  0x0DAE07DE,  0x0E897685,  0x0F4DAEDD,
    0x10000000,  0x10A402FD,  0x113C4841,  0x11CAB613,
    0x1250BFE2,  0x12CF8890,  0x1347F8AB,  0x13BACD65,
    0x1428A2FA,  0x1491FC15,  0x14F74744,  0x1558E2F7,
    0x15B72095,  0x161246D7,  0x166A9399,  0x16C03D55,
    0x17137449,  0x17646369,  0x17B33124,  0x18000000,
    0x184AEF29,  0x18941AD8,  0x18DB9CB7,  0x19218C2E,
    0x1965FEA5,  0x19A907C2,  0x19EAB998,  0x1A2B24D0,
    0x1A6A58D5,  0x1AA863EE,  0x1AE5535D,  0x1B213377,
    0x1B5C0FBD,  0x1B95F2EC,  0x1BCEE70F,  0x1C06F590,
    0x1C3E2745,  0x1C74847A,  0x1CAA1501,  0x1CDEE035,
    0x1D12ED0B,  0x1D464212,  0x1D78E582,  0x1DAADD3A,
    0x1DDC2ECF,  0x1E0CDF8C,  0x1E3CF476,  0x1E6C7257,
    0x1E9B5DBA,  0x1EC9BAF6,  0x1EF78E2C,  0x1F24DB4E,
    0x1F51A620,  0x1F7DF23C,  0x1FA9C314,  0x1FD51BF2,
    0x20000000,  0x202A7244,  0x205475A6,  0x207E0CEE,
    0x20A73ACA,  0x20D001CC,  0x20F8646D,  0x2120650E,
    0x214805FA,  0x216F4963,  0x2196316C,  0x21BCC020,
    0x21E2F77A,  0x2208D961,  0x222E67AD,  0x2253A425,
    0x22789082,  0x229D2E6E,  0x22C17F82,  0x22E5854F,
    0x23094155,  0x232CB509,  0x234FE1D5,  0x2372C918,
    0x23956C26,  0x23B7CC47,  0x23D9EABB,  0x23FBC8B9,
    0x241D676E,  0x243EC7FF,  0x245FEB86,  0x2480D319,
    0x24A17FC3,  0x24C1F28B,  0x24E22C6C,  0x25022E5F,
    0x2521F954,  0x25418E33,  0x2560EDE2,  0x2580193E,
    0x259F111F,  0x25BDD657,  0x25DC69B4,  0x25FACBFE,
    0x2618FDF8,  0x26370060,  0x2654D3EF,  0x2672795C,
    0x268FF156,  0x26AD3C8A,  0x26CA5BA2,  0x26E74F41,
    0x27041808,  0x2720B695,  0x273D2B81,  0x27597762,
    0x27759ACB,  0x2791964B,  0x27AD6A6F,  0x27C917C0,
    0x27E49EC5,  0x28000000,  0x281B3BF3,  0x2836531B,
    0x285145F3,  0x286C14F5,  0x2886C096,  0x28A1494B,
    0x28BBAF85,  0x28D5F3B3,  0x28F01641,  0x290A179B,
    0x2923F82A,  0x293DB854,  0x2957587E,  0x2970D90A,
    0x298A3A59,  0x29A37CCA,  0x29BCA0BB,  0x29D5A687,
    0x29EE8E87,  0x2A075914,  0x2A200684,  0x2A38972C,
    0x2A510B5F,  0x2A696370,  0x2A819FAE,  0x2A99C069,
    0x2AB1C5ED,  0x2AC9B088,  0x2AE18085,  0x2AF9362C,
    0x2B10D1C6,  0x2B28539B,  0x2B3FBBEF,  0x2B570B09,
    0x2B6E412B,  0x2B855E97,  0x2B9C6390,  0x2BB35056,
    0x2BCA2527,  0x2BE0E242,  0x2BF787E4,  0x2C0E1649,
    0x2C248DAD,  0x2C3AEE4A,  0x2C513859,  0x2C676C13,
    0x2C7D89AF,  0x2C939164,  0x2CA98368,  0x2CBF5FF1,
    0x2CD52731,  0x2CEAD95E,  0x2D0076A9,  0x2D15FF45,
    0x2D2B7363,  0x2D40D332,  0x2D561EE4,  0x2D6B56A7,
    0x2D807AAA,  0x2D958B19,  0x2DAA8823,  0x2DBF71F4,
    0x2DD448B7,  0x2DE90C98,  0x2DFDBDC0,  0x2E125C5C,
    0x2E26E892,  0x2E3B628D,  0x2E4FCA75,  0x2E642070,
    0x2E7864A8,  0x2E8C9741,  0x2EA0B862,  0x2EB4C831,
    0x2EC8C6D3,  0x2EDCB46C,  0x2EF09121,  0x2F045D14,
    0x2F18186A,  0x2F2BC345,  0x2F3F5DC7,  0x2F52E812,
    0x2F666247,  0x2F79CC88,  0x2F8D26F4,  0x2FA071AC,
    0x2FB3ACD0,  0x2FC6D87F,  0x2FD9F4D7,  0x2FED01F8,
    0x30000000,  0x3012EF0C,  0x3025CF39,  0x3038A0A6,
    0x304B636D,  0x305E17AD,  0x3070BD81,  0x30835504,
    0x3095DE51,  0x30A85985,  0x30BAC6B9,  0x30CD2609,
    0x30DF778D,  0x30F1BB60,  0x3103F19C,  0x31161A59,
    0x312835B0,  0x313A43BA,  0x314C4490,  0x315E3849,
    0x31701EFD,  0x3181F8C4,  0x3193C5B4,  0x31A585E6,
    0x31B7396F,  0x31C8E066,  0x31DA7AE1,  0x31EC08F6,
    0x31FD8ABC,  0x320F0047,  0x322069AC,  0x3231C702,
    0x3243185C,  0x32545DCF,  0x32659770,  0x3276C552,
    0x3287E78A,  0x3298FE2C,  0x32AA094A,  0x32BB08F9,
    0x32CBFD4A,  0x32DCE652,  0x32EDC423,  0x32FE96D0,
    0x330F5E6A,  0x33201B04,  0x3330CCB0,  0x33417380,
    0x33520F85,  0x3362A0D0,  0x33732774,  0x3383A380,
    0x33941506,  0x33A47C17,  0x33B4D8C4,  0x33C52B1B,
    0x33D5732F,  0x33E5B10F,  0x33F5E4CA,  0x34060E71,
    0x34162E14,  0x342643C1,  0x34364F88,  0x34465178,
    0x345649A1,  0x34663810,  0x34761CD6,  0x3485F800,
    0x3495C99D,  0x34A591BB,  0x34B55069,  0x34C505B4,
    0x34D4B1AB,  0x34E4545B,  0x34F3EDD2,  0x35037E1D,
    0x3513054B,  0x35228367,  0x3531F881,  0x354164A3,
    0x3550C7DC,  0x35602239,  0x356F73C5,  0x357EBC8E,
    0x358DFCA0,  0x359D3408,  0x35AC62D1,  0x35BB8908,
    0x35CAA6B9,  0x35D9BBF0,  0x35E8C8B9,  0x35F7CD20,
    0x3606C92F,  0x3615BCF3,  0x3624A878,  0x36338BC8,
    0x364266EE,  0x365139F6,  0x366004EC,  0x366EC7D9,
    0x367D82C9,  0x368C35C6,  0x369AE0DC,  0x36A98414,
    0x36B81F7A,  0x36C6B317,  0x36D53EF7,  0x36E3C323,
    0x36F23FA5,  0x3700B488,  0x370F21D5,  0x371D8797,
    0x372BE5D7,  0x373A3CA0,  0x37488BF9,  0x3756D3EF,
    0x37651489,  0x37734DD1,  0x37817FD1,  0x378FAA92,
    0x379DCE1D,  0x37ABEA7C,  0x37B9FFB7,  0x37C80DD7,
    0x37D614E6,  0x37E414EC,  0x37F20DF1,  0x38000000,
    0x380DEB20,  0x381BCF5A,  0x3829ACB6,  0x3837833D,
    0x384552F8,  0x38531BEE,  0x3860DE28,  0x386E99AF,
    0x387C4E89,  0x3889FCC0,  0x3897A45B,  0x38A54563,
    0x38B2DFDF,  0x38C073D7,  0x38CE0152,  0x38DB885A,
    0x38E908F4,  0x38F68329,  0x3903F701,  0x39116483,
    0x391ECBB6,  0x392C2CA1,  0x3939874D,  0x3946DBC0,
    0x39542A01,  0x39617218,  0x396EB40C,  0x397BEFE4,
    0x398925A7,  0x3996555C,  0x39A37F09,  0x39B0A2B7,
    0x39BDC06A,  0x39CAD82B,  0x39D7EA01,  0x39E4F5F0,
    0x39F1FC01,  0x39FEFC3A,  0x3A0BF6A2,  0x3A18EB3E,
    0x3A25DA16,  0x3A32C32F,  0x3A3FA691,  0x3A4C8441,
    0x3A595C46,  0x3A662EA6,  0x3A72FB67,  0x3A7FC28F,
    0x3A8C8425,  0x3A99402E,  0x3AA5F6B1,  0x3AB2A7B3,
    0x3ABF533A,  0x3ACBF94D,  0x3AD899F1,  0x3AE5352C,
    0x3AF1CB03,  0x3AFE5B7D,  0x3B0AE6A0,  0x3B176C70,
    0x3B23ECF3,  0x3B306830,  0x3B3CDE2C,  0x3B494EEB,
    0x3B55BA74,  0x3B6220CC,  0x3B6E81F9,  0x3B7ADE00,
    0x3B8734E5,  0x3B9386B0,  0x3B9FD364,  0x3BAC1B07,
    0x3BB85D9E,  0x3BC49B2F,  0x3BD0D3BE,  0x3BDD0751,
    0x3BE935ED,  0x3BF55F97,  0x3C018453,  0x3C0DA427,
    0x3C19BF17,  0x3C25D52A,  0x3C31E662,  0x3C3DF2C6,
    0x3C49FA5B,  0x3C55FD24,  0x3C61FB27,  0x3C6DF468,
    0x3C79E8ED,  0x3C85D8B9,  0x3C91C3D2,  0x3C9DAA3C,
    0x3CA98BFC,  0x3CB56915,  0x3CC1418E,  0x3CCD156A,
    0x3CD8E4AE,  0x3CE4AF5E,  0x3CF0757F,  0x3CFC3714,
    0x3D07F423,  0x3D13ACB0,  0x3D1F60BF,  0x3D2B1055,
    0x3D36BB75,  0x3D426224,  0x3D4E0466,  0x3D59A23F,
    0x3D653BB4,  0x3D70D0C8,  0x3D7C6180,  0x3D87EDE0,
    0x3D9375EC,  0x3D9EF9A8,  0x3DAA7918,  0x3DB5F43F,
    0x3DC16B23,  0x3DCCDDC7,  0x3DD84C2E,  0x3DE3B65D,
    0x3DEF1C58,  0x3DFA7E22,  0x3E05DBC0,  0x3E113535,
    0x3E1C8A85,  0x3E27DBB3,  0x3E3328C4,  0x3E3E71BB,
    0x3E49B69C,  0x3E54F76B,  0x3E60342B,  0x3E6B6CE0,
    0x3E76A18D,  0x3E81D237,  0x3E8CFEE0,  0x3E98278D,
    0x3EA34C40,  0x3EAE6CFE,  0x3EB989CA,  0x3EC4A2A8,
    0x3ECFB79A,  0x3EDAC8A5,  0x3EE5D5CB,  0x3EF0DF10,
    0x3EFBE478,  0x3F06E606,  0x3F11E3BE,  0x3F1CDDA2,
    0x3F27D3B6,  0x3F32C5FD,  0x3F3DB47B,  0x3F489F32,
    0x3F538627,  0x3F5E695C,  0x3F6948D5,  0x3F742494,
    0x3F7EFC9D,  0x3F89D0F3,  0x3F94A19A,  0x3F9F6E94,
    0x3FAA37E4,  0x3FB4FD8E,  0x3FBFBF94,  0x3FCA7DFB,
    0x3FD538C4,  0x3FDFEFF3,  0x3FEAA38A,  0x3FF5538E,
    0x40000000
};


