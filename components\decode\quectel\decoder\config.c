
#include "quec_decoder_config.h"
#include <stdlib.h>     /* strtol */
#include <string.h>     /* strchr, strncmp, strlen */


#include "quec_decoder.h"

int quec_decoder_parse_config (const char *cfgstr,
                        quec_decoder_symbol_type_t *sym,
                        quec_decoder_config_t *cfg,
                        int *val)
{
    if(!cfgstr)
        return(1);

    const char *dot = strchr(cfgstr, '.');
    if(dot) {
        int len = dot - cfgstr;
        if(!len || (len == 1 && !strncmp(cfgstr, "*", len)))
            *sym = 0;
        else if(len < 2)
            return(1);
        else if(!strncmp(cfgstr, "qrcode", len))
            *sym = QUEC_DECODER_QRCODE;
        else if(len < 3)
            return(1);
        else if(!strncmp(cfgstr, "upca", len))
            *sym = QUEC_DECODER_UPCA;
        else if(!strncmp(cfgstr, "upce", len))
            *sym = QUEC_DECODER_UPCE;
        else if(!strncmp(cfgstr, "ean13", len))
            *sym = QUEC_DECODER_EAN13;
        else if(!strncmp(cfgstr, "ean8", len))
            *sym = QUEC_DECODER_EAN8;
        else if(!strncmp(cfgstr, "i25", len))
            *sym = QUEC_DECODER_I25;
        else if(len < 4)
            return(1);
        else if(!strncmp(cfgstr, "scanner", len))
            *sym = QUEC_DECODER_PARTIAL; /* FIXME lame */
        else if(!strncmp(cfgstr, "isbn13", len))
            *sym = QUEC_DECODER_ISBN13;
        else if(!strncmp(cfgstr, "isbn10", len))
            *sym = QUEC_DECODER_ISBN10;
        else if(len < 6)
            return(1);
        else if(!strncmp(cfgstr, "code39", len))
            *sym = QUEC_DECODER_CODE39;
        else if(!strncmp(cfgstr, "pdf417", len))
            *sym = QUEC_DECODER_PDF417;
        else if(len < 7)
            return(1);
        else if(!strncmp(cfgstr, "code128", len))
            *sym = QUEC_DECODER_CODE128;
        else
            return(1);
        cfgstr = dot + 1;
    }
    else
        *sym = 0;

    int len = strlen(cfgstr);
    const char *eq = strchr(cfgstr, '=');
    if(eq)
        len = eq - cfgstr;
    else
        *val = 1;  /* handle this here so we can override later */
    char negate = 0;

    if(len > 3 && !strncmp(cfgstr, "no-", 3)) {
        negate = 1;
        cfgstr += 3;
        len -= 3;
    }

    if(len < 1)
        return(1);
    else if(!strncmp(cfgstr, "y-density", len))
        *cfg = QUEC_DECODER_CFG_Y_DENSITY;
    else if(!strncmp(cfgstr, "x-density", len))
        *cfg = QUEC_DECODER_CFG_X_DENSITY;
    else if(len < 2)
        return(1);
    else if(!strncmp(cfgstr, "enable", len))
        *cfg = QUEC_DECODER_CFG_ENABLE;
    else if(len < 3)
        return(1);
    else if(!strncmp(cfgstr, "disable", len)) {
        *cfg = QUEC_DECODER_CFG_ENABLE;
        negate = !negate; /* no-disable ?!? */
    }
    else if(!strncmp(cfgstr, "min-length", len))
        *cfg = QUEC_DECODER_CFG_MIN_LEN;
    else if(!strncmp(cfgstr, "max-length", len))
        *cfg = QUEC_DECODER_CFG_MAX_LEN;
    else if(!strncmp(cfgstr, "ascii", len))
        *cfg = QUEC_DECODER_CFG_ASCII;
    else if(!strncmp(cfgstr, "add-check", len))
        *cfg = QUEC_DECODER_CFG_ADD_CHECK;
    else if(!strncmp(cfgstr, "emit-check", len))
        *cfg = QUEC_DECODER_CFG_EMIT_CHECK;
    else if(!strncmp(cfgstr, "position", len))
        *cfg = QUEC_DECODER_CFG_POSITION;
    else 
        return(1);

    if(eq) {
        *val = strtol(eq + 1, NULL, 0);
    }
    if(negate)
        *val = !*val;

    return(0);
}
