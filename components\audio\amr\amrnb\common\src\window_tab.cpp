/* ------------------------------------------------------------------
 * Copyright (C) 1998-2009 PacketVideo
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied.
 * See the License for the specific language governing permissions
 * and limitations under the License.
 * -------------------------------------------------------------------
 */
/****************************************************************************************
Portions of this file are derived from the following 3GPP standard:

    3GPP TS 26.073
    ANSI-C code for the Adaptive Multi-Rate (AMR) speech codec
    Available from http://www.3gpp.org

(C) 2004, 3GPP Organizational Partners (ARIB, ATIS, CCSA, ETSI, TTA, TTC)
Permission to distribute, modify and use this file under the standard license
terms listed above has been obtained from the copyright holder.
****************************************************************************************/
/*
 Pathname: .audio/gsm-amr/c/src/window_tab.c

------------------------------------------------------------------------------
 REVISION HISTORY

 Description: Changed tables from static const to just const.

 Description: Added #ifdef __cplusplus and removed "extern" from table
              definition.

 Description: Put "extern" back.

 Who:                       Date:
 Description:

------------------------------------------------------------------------------
 INPUT AND OUTPUT DEFINITIONS

 Inputs:
    None

 Local Stores/Buffers/Pointers Needed:
    None

 Global Stores/Buffers/Pointers Needed:
    None

 Outputs:
    None

 Pointers and Buffers Modified:
    None

 Local Stores Modified:
    None

 Global Stores Modified:
    None

------------------------------------------------------------------------------
 FUNCTION DESCRIPTION

      File             : window.tab
      Purpose          : Hamming_cos window for LPC analysis.

------------------------------------------------------------------------------
 REQUIREMENTS

 None

------------------------------------------------------------------------------
 REFERENCES

 None

------------------------------------------------------------------------------
 PSEUDO-CODE


------------------------------------------------------------------------------
 RESOURCES USED
   When the code is written for a specific target processor the
     the resources used should be documented below.

 STACK USAGE: [stack count for this module] + [variable to represent
          stack usage for each subroutine called]

     where: [stack usage variable] = stack usage for [subroutine
         name] (see [filename].ext)

 DATA MEMORY USED: x words

 PROGRAM MEMORY USED: x words

 CLOCK CYCLES: [cycle count equation for this module] + [variable
           used to represent cycle count for each subroutine
           called]

     where: [cycle count variable] = cycle count for [subroutine
        name] (see [filename].ext)

------------------------------------------------------------------------------
*/


/*----------------------------------------------------------------------------
; INCLUDES
----------------------------------------------------------------------------*/
#include    "typedef.h"
#include    "cnst.h"
#include    "window_tab.h"

/*--------------------------------------------------------------------------*/
#ifdef __cplusplus
extern "C"
{
#endif

    /*----------------------------------------------------------------------------
    ; MACROS
    ; Define module specific macros here
    ----------------------------------------------------------------------------*/


    /*----------------------------------------------------------------------------
    ; DEFINES
    ; Include all pre-processor statements here. Include conditional
    ; compile variables also.
    ----------------------------------------------------------------------------*/

    /*----------------------------------------------------------------------------
    ; LOCAL FUNCTION DEFINITIONS
    ; Function Prototype declaration
    ----------------------------------------------------------------------------*/


    /*----------------------------------------------------------------------------
    ; LOCAL STORE/BUFFER/POINTER DEFINITIONS
    ; Variable declaration - defined here and used outside this module
    ----------------------------------------------------------------------------*/
    /*************************************************************************
     *
     * Hamming_cos windows for LPC analysis.
     *
     *************************************************************************/

    /* window for non-EFR modesm; uses 40 samples lookahead */

    const Word16 window_200_40[L_WINDOW] =
    {
        2621,  2623,  2629,  2638,  2651,  2668,  2689,  2713,  2741,  2772,
        2808,  2847,  2890,  2936,  2986,  3040,  3097,  3158,  3223,  3291,
        3363,  3438,  3517,  3599,  3685,  3774,  3867,  3963,  4063,  4166,
        4272,  4382,  4495,  4611,  4731,  4853,  4979,  5108,  5240,  5376,
        5514,  5655,  5800,  5947,  6097,  6250,  6406,  6565,  6726,  6890,
        7057,  7227,  7399,  7573,  7750,  7930,  8112,  8296,  8483,  8672,
        8863,  9057,  9252,  9450,  9650,  9852, 10055, 10261, 10468, 10677,
        10888, 11101, 11315, 11531, 11748, 11967, 12187, 12409, 12632, 12856,
        13082, 13308, 13536, 13764, 13994, 14225, 14456, 14688, 14921, 15155,
        15389, 15624, 15859, 16095, 16331, 16568, 16805, 17042, 17279, 17516,
        17754, 17991, 18228, 18465, 18702, 18939, 19175, 19411, 19647, 19882,
        20117, 20350, 20584, 20816, 21048, 21279, 21509, 21738, 21967, 22194,
        22420, 22644, 22868, 23090, 23311, 23531, 23749, 23965, 24181, 24394,
        24606, 24816, 25024, 25231, 25435, 25638, 25839, 26037, 26234, 26428,
        26621, 26811, 26999, 27184, 27368, 27548, 27727, 27903, 28076, 28247,
        28415, 28581, 28743, 28903, 29061, 29215, 29367, 29515, 29661, 29804,
        29944, 30081, 30214, 30345, 30472, 30597, 30718, 30836, 30950, 31062,
        31170, 31274, 31376, 31474, 31568, 31659, 31747, 31831, 31911, 31988,
        32062, 32132, 32198, 32261, 32320, 32376, 32428, 32476, 32521, 32561,
        32599, 32632, 32662, 32688, 32711, 32729, 32744, 32755, 32763, 32767,
        32767, 32741, 32665, 32537, 32359, 32129, 31850, 31521, 31143, 30716,
        30242, 29720, 29151, 28538, 27879, 27177, 26433, 25647, 24821, 23957,
        23055, 22117, 21145, 20139, 19102, 18036, 16941, 15820, 14674, 13505,
        12315, 11106,  9879,  8637,  7381,  6114,  4838,  3554,  2264,   971
    };


    /* window for EFR, first two subframes, no lookahead */

    const Word16 window_160_80[L_WINDOW] =
    {
        2621, 2624, 2633, 2648, 2668, 2695, 2727, 2765, 2809, 2859,
        2915, 2976, 3043, 3116, 3194, 3279, 3368, 3464, 3565, 3671,
        3783, 3900, 4023, 4151, 4285, 4423, 4567, 4716, 4870, 5029,
        5193, 5362, 5535, 5714, 5897, 6084, 6277, 6473, 6674, 6880,
        7089, 7303, 7521, 7742, 7968, 8197, 8430, 8667, 8907, 9151,
        9398, 9648, 9902, 10158, 10417, 10680, 10945, 11212, 11482, 11755,
        12030, 12307, 12586, 12867, 13150, 13435, 13722, 14010, 14299, 14590,
        14882, 15175, 15469, 15764, 16060, 16356, 16653, 16950, 17248, 17546,
        17844, 18141, 18439, 18736, 19033, 19330, 19625, 19920, 20214, 20507,
        20799, 21090, 21380, 21668, 21954, 22239, 22522, 22803, 23083, 23360,
        23635, 23907, 24177, 24445, 24710, 24972, 25231, 25488, 25741, 25991,
        26238, 26482, 26722, 26959, 27192, 27422, 27647, 27869, 28087, 28300,
        28510, 28715, 28916, 29113, 29305, 29493, 29676, 29854, 30028, 30197,
        30361, 30519, 30673, 30822, 30966, 31105, 31238, 31366, 31489, 31606,
        31718, 31825, 31926, 32021, 32111, 32195, 32273, 32346, 32413, 32475,
        32530, 32580, 32624, 32662, 32695, 32721, 32742, 32756, 32765, 32767,
        32767, 32756, 32720, 32661, 32578, 32471, 32341, 32188, 32012, 31813,
        31592, 31349, 31084, 30798, 30492, 30165, 29818, 29453, 29068, 28666,
        28247, 27810, 27358, 26891, 26408, 25913, 25404, 24883, 24350, 23807,
        23255, 22693, 22124, 21548, 20965, 20378, 19786, 19191, 18593, 17994,
        17395, 16796, 16199, 15604, 15012, 14424, 13842, 13265, 12696, 12135,
        11582, 11039, 10507, 9986, 9477, 8981, 8499, 8031, 7579, 7143,
        6723, 6321, 5937, 5571, 5225, 4898, 4591, 4305, 4041, 3798,
        3577, 3378, 3202, 3048, 2918, 2812, 2729, 2669, 2633, 2621
    };

    /* window for EFR, last two subframes, no lookahead */

    const Word16 window_232_8[L_WINDOW] =
    {
        2621, 2623, 2627, 2634, 2644, 2656, 2671, 2689, 2710, 2734,
        2760, 2789, 2821, 2855, 2893, 2933, 2975, 3021, 3069, 3120,
        3173, 3229, 3288, 3350, 3414, 3481, 3550, 3622, 3697, 3774,
        3853, 3936, 4021, 4108, 4198, 4290, 4385, 4482, 4582, 4684,
        4788, 4895, 5004, 5116, 5230, 5346, 5464, 5585, 5708, 5833,
        5960, 6090, 6221, 6355, 6491, 6629, 6769, 6910, 7054, 7200,
        7348, 7498, 7649, 7803, 7958, 8115, 8274, 8434, 8597, 8761,
        8926, 9093, 9262, 9432, 9604, 9778, 9952, 10129, 10306, 10485,
        10665, 10847, 11030, 11214, 11399, 11586, 11773, 11962, 12152, 12342,
        12534, 12727, 12920, 13115, 13310, 13506, 13703, 13901, 14099, 14298,
        14497, 14698, 14898, 15100, 15301, 15504, 15706, 15909, 16112, 16316,
        16520, 16724, 16928, 17132, 17337, 17541, 17746, 17950, 18155, 18359,
        18564, 18768, 18972, 19175, 19379, 19582, 19785, 19987, 20189, 20390,
        20591, 20792, 20992, 21191, 21390, 21588, 21785, 21981, 22177, 22372,
        22566, 22759, 22951, 23143, 23333, 23522, 23710, 23897, 24083, 24268,
        24451, 24633, 24814, 24994, 25172, 25349, 25525, 25699, 25871, 26042,
        26212, 26380, 26546, 26711, 26874, 27035, 27195, 27353, 27509, 27664,
        27816, 27967, 28115, 28262, 28407, 28550, 28691, 28830, 28967, 29102,
        29234, 29365, 29493, 29619, 29743, 29865, 29985, 30102, 30217, 30330,
        30440, 30548, 30654, 30757, 30858, 30956, 31052, 31146, 31237, 31326,
        31412, 31495, 31576, 31655, 31730, 31804, 31874, 31942, 32008, 32071,
        32131, 32188, 32243, 32295, 32345, 32392, 32436, 32477, 32516, 32552,
        32585, 32615, 32643, 32668, 32690, 32709, 32726, 32740, 32751, 32759,
        32765, 32767, 32767, 32097, 30112, 26895, 22576, 17333, 11380, 4962
    };

    /*----------------------------------------------------------------------------
    ; EXTERNAL FUNCTION REFERENCES
    ; Declare functions defined elsewhere and referenced in this module
    ----------------------------------------------------------------------------*/


    /*----------------------------------------------------------------------------
    ; EXTERNAL GLOBAL STORE/BUFFER/POINTER REFERENCES
    ; Declare variables used in this module but defined elsewhere
    ----------------------------------------------------------------------------*/


    /*--------------------------------------------------------------------------*/
#ifdef __cplusplus
}
#endif

/*----------------------------------------------------------------------------
; FUNCTION CODE
----------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------
; Define all local variables
----------------------------------------------------------------------------*/


/*----------------------------------------------------------------------------
; Function body here
----------------------------------------------------------------------------*/


/*----------------------------------------------------------------------------
; Return nothing or data or data pointer
----------------------------------------------------------------------------*/

