
/*
 ** Copyright 2003-2010, VisualOn, Inc.
 **
 ** Licensed under the Apache License, Version 2.0 (the "License");
 ** you may not use this file except in compliance with the License.
 ** You may obtain a copy of the License at
 **
 **     http://www.apache.org/licenses/LICENSE-2.0
 **
 ** Unless required by applicable law or agreed to in writing, software
 ** distributed under the License is distributed on an "AS IS" BASIS,
 ** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ** See the License for the specific language governing permissions and
 ** limitations under the License.
 */


/*-------------------------------------------------------------------*
 *                         qisf_ns.h
 *-------------------------------------------------------------------*
 * Quantization tables for split by 5 VQ of ISFs for a background noise database
 * Version whith no prediction
 *-------------------------------------------------------------------*/

#define ORDER   16            /* order of linear prediction filter */
#define ISF_GAP 128

#define SIZE_BK_NOISE1  64
#define SIZE_BK_NOISE2  64
#define SIZE_BK_NOISE3  64
#define SIZE_BK_NOISE4  32
#define SIZE_BK_NOISE5  32


/* means of ISFs */
 static Word16 mean_isf_noise[ORDER] = {

   478,  1100,  2213,  3267,  4219,  5222,  6198,  7240,
  8229,  9153, 10098, 11108, 12144, 13184, 14165,  3803};


/* 28 bits */
/*-------------------------------------------------------------------*
 *  isf codebooks:  split-by-5 VQ                                    *
 *                                                                   *
 *  codebook   vector dimension    number of vectors                 *
 *  ~~~~~~~~   ~~~~~~~~~~~~~~~~    ~~~~~~~~~~~~~~~~~                 *
 *     1            2                  64                            *
 *     2            3                  64                            *
 *     3            3                  64                            *
 *     4            4                  32                            *
 *     5            4                  32                            *
 *-------------------------------------------------------------------*/

/*------------------------------------------------*
 * 1st split:   isf0 to isf1
 *------------------------------------------------*/


 static Word16 dico1_isf_noise[SIZE_BK_NOISE1*2] = {

  -269,  -673,
  -222,  -537,
  -233,  -430,
  -138,  -451,
  -212,  -331,
  -192,  -241,
   -87,  -231,
  -191,  -128,
   -70,  -106,
  -164,    -6,
    74,  -179,
    27,   -33,
  -102,    74,
  -162,   115,
   -94,   172,
    -6,   130,
  -143,   234,
    14,   218,
   -65,   270,
    88,   182,
  -124,   341,
   -44,   381,
    38,   335,
   117,   274,
  -112,   454,
    74,   431,
    -5,   488,
   175,   384,
   -83,   561,
   122,   529,
    21,   601,
   229,   481,
   231,   303,
   226,   608,
   300,   372,
   210,   187,
   306,   265,
   328,   473,
   382,   331,
   371,   132,
   139,    58,
   365,    21,
   250,   -82,
   443,   218,
   483,   110,
   426,   415,
   579,   222,
   518,   333,
   573,   448,
   455,   529,
   685,   329,
   332,   580,
   595,   593,
   468,   645,
   762,   517,
   326,   709,
   485,   793,
   130,   684,
   671,   737,
   354,   876,
    88,   806,
   -65,   706,
   -35,  1016,
   266,  1123};


/*------------------------------------------------*
 * 2nd split:   isf2 to isf4
 *------------------------------------------------*/

 static Word16 dico2_isf_noise[SIZE_BK_NOISE2*3] = {

  -824,  -884,  -949,
  -805,  -456,  -418,
  -442,  -438,  -541,
  -217,  -578,  -793,
  -168,  -444,  -582,
  -287,  -492,  -274,
  -552,  -297,  -300,
  -163,  -333,  -358,
  -370,  -232,  -232,
  -175,  -358,  -159,
  -381,   -21,  -357,
  -184,  -159,  -162,
   -53,  -191,  -280,
    18,  -267,  -215,
  -138,    61,  -283,
    71,   -95,  -294,
    13,  -156,  -546,
     0,   -83,   -79,
    44,    97,  -316,
   178,   -52,  -213,
   222,  -261,  -422,
   237,  -118,   -44,
   141,   145,  -132,
   363,    81,  -287,
   213,    65,    34,
  -107,    94,    -5,
    91,   -29,   126,
  -355,    51,   -41,
  -219,   -76,   145,
   -63,   100,   244,
  -719,    44,    27,
  -572,  -124,   155,
  -423,   133,   315,
  -917,    71,   224,
  -268,   318,   131,
   -93,  -190,   420,
   -97,   122,   491,
   -79,   317,   355,
   130,   100,   325,
    86,  -293,   210,
   133,   258,   161,
   176,   -73,   465,
   195,   300,   384,
   348,    22,   221,
   376,   183,   409,
   377,   286,   202,
   242,   213,   659,
   257,   565,   248,
   344,   408,   -76,
   405,   440,   509,
   612,   385,   379,
   536,   607,   216,
   -56,   582,   192,
   100,   517,   567,
  -365,   448,   445,
   728,   347,    10,
   505,   357,   759,
   636,   582,   658,
   335,   517,   852,
   378,   809,   572,
  -195,   878,   829,
   529,   707,   987,
   918,   726,   392,
  1250,   997,  1063};

/*------------------------------------------------*
 * 3rd split:   isf5 to isf7
 *------------------------------------------------*/

 static Word16 dico3_isf_noise[SIZE_BK_NOISE3*3] = {

  -805,  -838,  -774,
  -522,  -627,  -828,
  -477,  -486,  -603,
  -295,  -481,  -634,
  -366,  -384,  -393,
  -186,  -414,  -396,
  -237,  -394,  -106,
  -252,  -202,  -275,
   -61,  -177,  -442,
   -84,  -198,  -199,
  -179,  -125,   -31,
   -72,   -47,  -163,
  -298,  -220,   215,
   -64,  -168,   251,
  -133,   156,   -59,
   -30,    -2,   127,
    54,    66,   -61,
  -233,    21,   251,
   209,   -50,    32,
    33,   194,   136,
  -117,   -18,   475,
   202,    46,   309,
   256,   185,    53,
    35,   200,   390,
   200,   263,   242,
  -216,   302,   294,
   128,   358,     0,
    19,   431,   287,
   224,   447,   280,
   367,   165,   213,
   397,   314,   319,
   383,   379,    75,
   277,   325,   462,
   394,   505,   334,
   251,    98,  -213,
   450,   153,   448,
   565,   226,    76,
   470,   383,   502,
   635,   390,   278,
   237,   135,   620,
   342,   401,   649,
   331,   551,   518,
   130,   418,   592,
   531,   306,   737,
   729,   389,   580,
   497,   557,   699,
   296,   383,   874,
   283,   624,   759,
   126,   622,   476,
   559,   595,   472,
   382,   770,   616,
   719,   613,   745,
   540,   639,   928,
   517,   826,   801,
   684,   811,   604,
   752,   786,   857,
   933,   661,   350,
   694,   450,  1061,
   562,   911,  1051,
   824,   813,  1104,
   758,  1047,   882,
  1140,   917,   889,
  1039,  1246,  1426,
  1483,  1666,  1876};

/*------------------------------------------------*
 * 4th split:   isf8 to isf11
 *------------------------------------------------*/

 static Word16 dico4_isf_noise[SIZE_BK_NOISE4*4] = {

  -776,  -854,  -891,  -920,
  -552,  -610,  -663,  -741,
  -321,  -370,  -476,  -565,
   274,  -160,  -456,   201,
   265,    67,  -160,  -306,
    -8,  -210,    79,   272,
   163,   236,   307,   308,
   578,   317,    64,   298,
    -9,   197,   342,   620,
   343,   232,   314,   622,
   173,   149,   548,   527,
   356,   370,   481,   376,
   135,   444,   488,   556,
   391,   471,   487,   653,
   228,   424,   576,   835,
   422,   372,   722,   682,
   295,   673,   693,   635,
   539,   596,   590,   449,
   475,   618,   659,   818,
   735,   517,   491,   673,
   602,   346,   257,   877,
   625,   635,   849,   720,
   727,   818,   698,   595,
   653,   481,   690,  1139,
   814,   762,   704,   908,
   507,   747,   898,   936,
   848,   855,   924,   785,
   646,  1037,   882,   795,
   772,   845,  1024,  1151,
  1133,   983,   818,   921,
   940,  1068,  1252,  1302,
  1588,  1767,  1718,  1513};

/*------------------------------------------------*
 * 5th split:   isf12 to isf15
 *------------------------------------------------*/

 static Word16 dico5_isf_noise[SIZE_BK_NOISE5*4] = {
  -810,  -879,  -945,  -254,
   248,   184,   671,   128,
   288,   703,   918,    99,
   658,   558,   662,   219,
   552,   585,   910,   208,
   559,   804,   759,   119,
   606,   774,   921,  -139,
   782,   761,   748,   208,
   756,   708,   983,    56,
   544,   864,  1010,   152,
   737,   698,   987,   299,
   771,   924,   879,   103,
   536,   785,   961,   405,
   667,   916,   801,   328,
   738,   705,   773,   439,
   823,   871,   992,   355,
   640,  1004,  1052,   369,
   724,   822,   949,   597,
   415,   655,   729,   482,
  1009,   896,   793,   363,
   908,   803,   687,   -25,
  1016,   838,  1011,   189,
   947,  1112,   942,   222,
   914,  1049,   981,   527,
   956,   987,  1011,  -120,
   781,  1049,  1121,    92,
  1178,  1053,   884,    47,
  1123,  1059,  1182,   118,
   933,   972,  1277,   357,
  1109,   918,  1101,   503,
  1039,  1286,  1220,   317,
  1351,  1207,  1010,   326};

